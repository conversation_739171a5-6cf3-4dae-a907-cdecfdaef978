{"-1 for no limit, or a positive integer for a specific limit": "-1 pro žádný limit, nebo kladné celé číslo pro specifický limit", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' nebo '-1' pro <PERSON><PERSON><PERSON><PERSON> v<PERSON>.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(např. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(např. `sh webui.sh --api`)", "(latest)": "(nejnovější)", "(leave blank for to use commercial endpoint)": "(ponechte prázdné pro použití komerčního koncového bodu)", "[Last] dddd [at] h:mm A": "[Naposledy] dddd [v] h:mm A", "[Today at] h:mm A": "[Dnes v] h:mm A", "[Yesterday at] h:mm A": "[Včera v] h:mm A", "{{ models }}": "{{ models }}", "{{COUNT}} Available Tools": "{{COUNT}} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "{{COUNT}} characters": "{{COUNT}} znak<PERSON>", "{{COUNT}} extracted lines": "", "{{COUNT}} hidden lines": "{{COUNT}} s<PERSON><PERSON><PERSON><PERSON><PERSON>", "{{COUNT}} Replies": "{{COUNT}} odpovědí", "{{COUNT}} words": "{{COUNT}} slov", "{{model}} download has been canceled": "Stažení modelu {{model}} by<PERSON>", "{{user}}'s Chats": "Konverzace uživatele {{user}}", "{{webUIName}} Backend Required": "<PERSON><PERSON> backend {{webUIName}}", "*Prompt node ID(s) are required for image generation": "*Pro generování obrázků jsou vyžadována ID uzlů instrukce", "A new version (v{{LATEST_VERSION}}) is now available.": "Nová verze (v{{LATEST_VERSION}}) je nyn<PERSON> k dispozici.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Model pro úkoly se používá při prová<PERSON><PERSON><PERSON><PERSON>, jako je generování názvů pro konverzace a vyhledávací dotazy na webu.", "a user": "uživatel", "About": "O aplikaci", "Accept autocomplete generation / Jump to prompt variable": "Přijmout automatické dokončení / Přejít na proměnnou instrukce", "Access": "Přístup", "Access Control": "Řízení přístupu", "Accessible to all users": "Přístupné pro všechny uživatele", "Account": "Účet", "Account Activation Pending": "Čeká se na aktivaci účtu", "Accurate information": "Přesné informace", "Action": "Ak<PERSON>", "Action not found": "<PERSON><PERSON><PERSON>", "Action Required for Chat Log Storage": "Je vyžadována akce pro uložení záznamu chatu", "Actions": "Ak<PERSON>", "Activate": "Aktivovat", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Aktivujte tento příkaz napsáním \"/{{COMMAND}}\" do vstupního pole konverzace.", "Active": "Aktivní", "Active Users": "Aktivní uživatelé", "Add": "<PERSON><PERSON><PERSON><PERSON>", "Add a model ID": "Přidat ID modelu", "Add a short description about what this model does": "<PERSON>řide<PERSON><PERSON> krátk<PERSON> pop<PERSON> toho, co tento model <PERSON><PERSON><PERSON><PERSON>.", "Add a tag": "Přidat štítek", "Add Arena Model": "Přidat model do arény", "Add Connection": "Přidat připojení", "Add Content": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "Add content here": "Zde přidejte obsah", "Add Custom Parameter": "Přidat vlastní parametr", "Add custom prompt": "Přidat vlastní instrukce", "Add Details": "P<PERSON><PERSON><PERSON> podrobnosti", "Add Files": "<PERSON><PERSON><PERSON><PERSON>", "Add Group": "<PERSON><PERSON><PERSON><PERSON>", "Add Memory": "Přidat vzpomínku", "Add Model": "Přidat model", "Add Reaction": "<PERSON><PERSON><PERSON><PERSON>", "Add Tag": "Přidat štítek", "Add Tags": "<PERSON><PERSON><PERSON><PERSON>", "Add text content": "<PERSON><PERSON><PERSON><PERSON>ový obsah", "Add User": "Přidat <PERSON>", "Add User Group": "Přidat skupinu uživatelů", "Additional Config": "Dodatečná konfigurace", "Additional configuration options for marker. This should be a JSON string with key-value pairs. For example, '{\"key\": \"value\"}'. Supported keys include: disable_links, keep_pageheader_in_output, keep_pagefooter_in_output, filter_blank_pages, drop_repeated_text, layout_coverage_threshold, merge_threshold, height_tolerance, gap_threshold, image_threshold, min_line_length, level_count, default_level": "<PERSON><PERSON><PERSON> m<PERSON>nosti konfigurace pro marker. <PERSON><PERSON><PERSON> by to b<PERSON><PERSON> řetězec JSON s p<PERSON><PERSON>. Například: '{\"key\": \"value\"}'. Podporované klíče zahrnují: disable_links, keep_pageheader_in_output, keep_pagefooter_in_output, filter_blank_pages, drop_repeated_text, layout_coverage_threshold, merge_threshold, height_tolerance, gap_threshold, image_threshold, min_line_length, level_count, default_level", "Adjusting these settings will apply changes universally to all users.": "Úprava těchto nastavení se projeví u všech uživatelů.", "admin": "administ<PERSON><PERSON><PERSON>", "Admin": "Admini<PERSON><PERSON><PERSON><PERSON>", "Admin Panel": "Panel administrátora", "Admin Settings": "Nastavení administrátora", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Administrátoři mají kdykoli přístup ke všem nástrojům; uživatelé potřebují mít nástroje přiřazené k jednotlivým modelům v pracovním prostoru.", "Advanced Parameters": "Pokročilé parametry", "Advanced Params": "Pokročilé parametry", "AI": "UI", "All": "<PERSON><PERSON><PERSON>", "All Documents": "Všechny dokumenty", "All models deleted successfully": "Všechny modely byly <PERSON><PERSON>", "Allow Call": "Povolit volání", "Allow Chat Controls": "Povolit ovládací prvky chatu", "Allow Chat Delete": "Povolit smazání konverzace", "Allow Chat Deletion": "Povolit smazání konverzace", "Allow Chat Edit": "Povolit úpravu konverzace", "Allow Chat Export": "Povolit export konverzace", "Allow Chat Params": "Povolit parametry chatu", "Allow Chat Share": "Povolit sdílení konverzace", "Allow Chat System Prompt": "Povolit systémové instrukce konverzace", "Allow Chat Valves": "Povolit ventily chatu", "Allow Continue Response": "", "Allow Delete Messages": "", "Allow File Upload": "Povolit nahrávání <PERSON>", "Allow Multiple Models in Chat": "Povolit více modelů v chatu", "Allow non-local voices": "Povolit nelokální hlasy", "Allow Rate Response": "", "Allow Regenerate Response": "", "Allow Speech to Text": "Povolit převod řeči na text", "Allow Temporary Chat": "Povolit dočasnou konverzaci", "Allow Text to Speech": "Povolit převod textu na řeč", "Allow User Location": "Povolit zjištění polohy uživatele", "Allow Voice Interruption in Call": "Povolit přerušení hlasu při hovoru", "Allowed Endpoints": "Povolené koncové body", "Allowed File Extensions": "Povolené přípony souborů", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "Povolené přípony souborů pro nahrání. Více přípon oddělte čárkami. Ponechte prázdné pro všechny typy souborů.", "Already have an account?": "<PERSON><PERSON> máte účet?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "Alternativa k top_p, j<PERSON><PERSON><PERSON><PERSON> cílem je zajistit rovnováhu mezi kvalitou a rozmanitostí. Parametr p představuje minimální pravděpodobnost, s jakou je token <PERSON>, vz<PERSON>ženou k pravděpodobnosti nejpravděpodobnějšího tokenu. Například při p=0,05 a nejpravděpodobnějším tokenu s pravděpodobností 0,9 jsou odfiltrovány logity s hodnotou menší než 0,045.", "Always": "Vž<PERSON>", "Always Collapse Code Blocks": "<PERSON><PERSON><PERSON> sbalit bloky kódu", "Always Expand Details": "<PERSON><PERSON>dy rozbalit podrobnosti", "Always Play Notification Sound": "Vždy přehrát zvuk oznámení", "Amazing": "Úžasné", "an assistant": "asistent", "An error occurred while fetching the explanation": "Při načítání vysvětlení došlo k chybě", "Analytics": "<PERSON><PERSON><PERSON><PERSON>", "Analyzed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Analyzing...": "<PERSON><PERSON><PERSON><PERSON>...", "and": "a", "and {{COUNT}} more": "a {{COUNT}} <PERSON><PERSON><PERSON><PERSON><PERSON>", "and create a new shared link.": "a vytvořit nový sdílený odkaz.", "Android": "Android", "API": "API", "API Base URL": "Základní URL adresa API", "API Base URL for Datalab Marker service. Defaults to: https://www.datalab.to/api/v1/marker": "Základní URL adresa API pro službu Datalab Marker. Výchozí hodnota: https://www.datalab.to/api/v1/marker", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "Podrobnosti API pro použití vizuálně-jazykového modelu v popisu obrázku. Tento parametr je vzájemně se vylučující s picture_description_local.", "API Key": "Klíč API", "API Key created.": "API klíč byl vytvořen.", "API Key Endpoint Restrictions": "Omezení koncových bodů API klíče", "API keys": "API klíče", "API Version": "Verze API", "API Version is required": "Verze API je vyžadována", "Application DN": "Application DN", "Application DN Password": "Heslo pro Application DN", "applies to all users with the \"user\" role": "platí pro všechny uživatele s rolí \"user\"", "April": "<PERSON><PERSON>", "Archive": "Archivovat", "Archive All Chats": "Archivovat všechny konverzace", "Archived Chats": "Archivované k<PERSON>", "archived-chat-export": "export-archivovanych-konverzaci", "Are you sure you want to clear all memories? This action cannot be undone.": "Opravdu si přejete vymazat všechny vzpomínky? Tuto akci nelze vrátit zpět.", "Are you sure you want to delete this channel?": "Opravdu chcete smazat tento kanál?", "Are you sure you want to delete this message?": "Opravdu chcete smazat tuto zprávu?", "Are you sure you want to unarchive all archived chats?": "Opravdu chcete zrušit archivaci všech archivovaných konverzací?", "Are you sure?": "Jste si jisti?", "Arena Models": "Modely pro arénu", "Artifacts": "Artefakty", "Ask": "<PERSON>ept<PERSON> se", "Ask a question": "Položit otázku", "Assistant": "Asistent", "Attach file from knowledge": "Př<PERSON><PERSON><PERSON> soubor ze znalostní báze", "Attention to detail": "Pozornost k detailům", "Attribute for Mail": "Atribut pro e-mail", "Attribute for Username": "Atribut pro uživatelské jméno", "Audio": "Zvuk", "August": "<PERSON><PERSON>", "Auth": "Ověření", "Authenticate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Authentication": "Ověřování", "Auto": "Auto", "Auto-Copy Response to Clipboard": "Automaticky kopírovat odpověď do schránky", "Auto-playback response": "<PERSON><PERSON><PERSON> od<PERSON>", "Autocomplete Generation": "Generování automatick<PERSON>ho <PERSON>ní", "Autocomplete Generation Input Max Length": "Maximální délka vstupu pro generování automatického <PERSON>í", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "Ověřovací řetězec API pro AUTOMATIC1111", "AUTOMATIC1111 Base URL": "Základní URL pro AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "Je vyžadována základní URL pro AUTOMATIC1111.", "Available list": "<PERSON>z<PERSON> dostupných", "Available Tools": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "available users": "dostupní <PERSON>", "available!": "k dispozici!", "Away": "Nepřítomen", "Awful": "Hrozné", "Azure AI Speech": "Azure AI Speech", "Azure OpenAI": "Azure OpenAI", "Azure Region": "Oblast Azure", "Back": "<PERSON><PERSON><PERSON><PERSON>", "Bad Response": "Špatná odpověď", "Banners": "Upozornění", "Base Model (From)": "Základ<PERSON><PERSON> model (ze souboru)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "Mezipaměť seznamu základní<PERSON> modelů zrychluje přístup načítáním základních modelů pouze při spuštění nebo při uložení nastavení – je to rych<PERSON><PERSON><PERSON><PERSON>, ale nemusí zobrazovat nedávné změny základních modelů.", "Bearer": "Bearer", "before": "p<PERSON>ed", "Being lazy": "<PERSON><PERSON><PERSON>", "Beta": "Beta", "Bing Search V7 Endpoint": "<PERSON><PERSON><PERSON><PERSON> bod Bing Search V7", "Bing Search V7 Subscription Key": "<PERSON><PERSON><PERSON><PERSON> předplatného Bing Search V7", "Bio": "", "Birth Date": "", "BM25 Weight": "Váha BM25", "Bocha Search API Key": "API klíč pro Bocha Search", "Bold": "Tučně", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "Zvýhodňování nebo penalizace specifických tokenů pro omezené odpovědi. Hodnoty odchylky budou omezeny v rozmezí -100 až 100 (včetně). (Výchozí: ž<PERSON><PERSON><PERSON>)", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> jak j<PERSON><PERSON><PERSON>, ta<PERSON> jaz<PERSON>(y), nebo obo<PERSON><PERSON> pone<PERSON> p<PERSON>.", "Brave Search API Key": "Klíč API pro Brave Search", "Bullet List": "Seznam s odrážkami", "Button ID": "ID tlačítka", "Button Label": "Popisek tlačítka", "Button Prompt": "Instrukce tlačítka", "By {{name}}": "Od {{name}}", "Bypass Embedding and Retrieval": "Obejít vkládání a vyhledávání", "Bypass Web Loader": "Obej<PERSON><PERSON> webový zavaděč", "Cache Base Model List": "Ukládat seznam základních modelů do mezipaměti", "Calendar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Call": "Volání", "Call feature is not supported when using Web STT engine": "Funkce volání není podporována při použití webového STT jádra.", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "Zrušit", "Capabilities": "Sc<PERSON>nosti", "Capture": "Zaznamenat", "Capture Audio": "<PERSON><PERSON><PERSON><PERSON>", "Certificate Path": "Cesta k certifikátu", "Change Password": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Channel deleted successfully": "<PERSON><PERSON><PERSON>l úsp<PERSON>š<PERSON> s<PERSON>", "Channel Name": "Název ka<PERSON>", "Channel updated successfully": "<PERSON><PERSON><PERSON> byl úspěšně aktualizován", "Channels": "<PERSON><PERSON><PERSON>", "Character": "Postava", "Character limit for autocomplete generation input": "Limit znaků pro vstup generování automatického dokončování", "Chart new frontiers": "Objevujte nové hranice", "Chat": "Konverzace", "Chat Background Image": "Obrázek pozadí konverzace", "Chat Bubble UI": "Uživatelské rozhraní s bublinami konverzace", "Chat Controls": "Ovládání konverzace", "Chat Conversation": "", "Chat direction": "<PERSON><PERSON><PERSON><PERSON>", "Chat ID": "ID konverzace", "Chat moved successfully": "", "Chat Overview": "<PERSON><PERSON><PERSON><PERSON> kon<PERSON>zac<PERSON>", "Chat Permissions": "Oprávnění k<PERSON>zac<PERSON>", "Chat Tags Auto-Generation": "Automatické generování štítků konverzace", "Chats": "Konverzace", "Check Again": "Zkontrolovat znovu", "Check for updates": "Zkontrolovat aktualizace", "Checking for updates...": "Kontrola aktualizací...", "Choose a model before saving...": "<PERSON><PERSON><PERSON> v<PERSON> model...", "Chunk Overlap": "Překryv bloků", "Chunk Size": "Velikost bloku", "Ciphers": "<PERSON><PERSON><PERSON>", "Citation": "Citace", "Citations": "Citace", "Clear memory": "Vymazat paměť", "Clear Memory": "Vymazat paměť", "click here": "klikněte zde", "Click here for filter guides.": "Klikněte zde pro průvodce filtry.", "Click here for help.": "Klikněte zde pro nápovědu.", "Click here to": "Klikněte zde pro", "Click here to download user import template file.": "Klikněte zde pro stažení šablony souboru pro import <PERSON><PERSON><PERSON><PERSON><PERSON>.", "Click here to learn more about faster-whisper and see the available models.": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>, abyste se dozvěděli více o faster-whisper a prohlédli si dostupné modely.", "Click here to see available models.": "Klikněte zde pro zobrazení dostupných modelů.", "Click here to select": "Klikněte zde pro výběr", "Click here to select a csv file.": "Klikněte zde pro výběr CSV souboru.", "Click here to select a py file.": "Klikněte zde pro výběr souboru .py.", "Click here to upload a workflow.json file.": "Klikněte zde pro nahrání souboru workflow.json.", "click here.": "klikněte zde.", "Click on the user role button to change a user's role.": "Klikněte na tlačítko role uživatele pro změnu role uživatele.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Přístup k zápisu do schránky byl zamítnut. Prosím, zkontrolujte nastavení svého prohlížeče a udělte potřebný přístup.", "Clone": "<PERSON><PERSON><PERSON><PERSON>", "Clone Chat": "Klonovat konverzaci", "Clone of {{TITLE}}": "Klon konverzace {{TITLE}}", "Close": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Close Banner": "Zav<PERSON><PERSON><PERSON>orn<PERSON>", "Close Configure Connection Modal": "Zavřít modální okno konfigurace připojení", "Close modal": "Zavřít modální okno", "Close settings modal": "Zavřít modální okno nastavení", "Close Sidebar": "Zavřít postranní panel", "CMU ARCTIC speaker embedding name": "Název vektorizace mluvčího CMU ARCTIC", "Code Block": "Blok kódu", "Code execution": "Spouštěn<PERSON> kódu", "Code Execution": "Spouštěn<PERSON> kódu", "Code Execution Engine": "Jádro pro spouštění kódu", "Code Execution Timeout": "Časový limit pro spuštění kódu", "Code formatted successfully": "<PERSON><PERSON><PERSON> <PERSON>l úsp<PERSON>šně naformátov<PERSON>.", "Code Interpreter": "Interpret kódu", "Code Interpreter Engine": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "Code Interpreter Prompt Template": "Šablona instrukce pro interpret kódu", "Collapse": "Sbal<PERSON>", "Collection": "<PERSON><PERSON><PERSON><PERSON>", "Color": "<PERSON><PERSON>", "ComfyUI": "ComfyUI", "ComfyUI API Key": "API klíč pro ComfyUI", "ComfyUI Base URL": "Základní URL pro ComfyUI", "ComfyUI Base URL is required.": "Je vyžadována základní URL pro ComfyUI.", "ComfyUI Workflow": "Pracovní postup ComfyUI", "ComfyUI Workflow Nodes": "<PERSON><PERSON>ly pracovního postupu ComfyUI", "Comma separated Node Ids (e.g. 1 or 1,2)": "ID uzlů oddělená čárkou (např. 1 nebo 1,2)", "Command": "Příkaz", "Comment": "<PERSON><PERSON><PERSON><PERSON>", "Completions": "Dokončení", "Compress Images in Channels": "Komprimovat obrázky v kanálech", "Concurrent Requests": "Souběž<PERSON>é p<PERSON>", "Config imported successfully": "Konfigurace <PERSON><PERSON>", "Configure": "Konfigurovat", "Confirm": "Potvrdit", "Confirm Password": "Potvrdit heslo", "Confirm your action": "Potvrďte svou akci", "Confirm your new password": "Potvrďte své nové heslo", "Confirm Your Password": "Potvrďte své heslo", "Connect to your own OpenAI compatible API endpoints.": "Připojte se k vlastním koncovým bodům API kompatibilním s OpenAI.", "Connect to your own OpenAPI compatible external tool servers.": "Připojte se k vlastním externím serverům nástrojů kompatibilním s OpenAPI.", "Connection failed": "Připojení se nezdařilo", "Connection successful": "Připojení <PERSON>", "Connection Type": "Typ připojení", "Connections": "Připojení", "Connections saved successfully": "Připojení byla úspěšně ul<PERSON>ž<PERSON>", "Connections settings updated": "Nastavení připojení aktualizováno", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "Omezuje úsilí při uvažování u modelů pro uvažování. Platí pouze pro modely pro uvažování od specifických poskytov<PERSON>ů, kteří podporují úsilí při uvažování.", "Contact Admin for WebUI Access": "Pro přístup k webovému rozhraní kontaktujte administrátora.", "Content": "<PERSON><PERSON><PERSON>", "Content Extraction Engine": "J<PERSON>dro pro extrakci obsahu", "Continue Response": "Pokračovat v odpovědi", "Continue with {{provider}}": "Pokračovat s {{provider}}", "Continue with Email": "Pokračovat s e-mailem", "Continue with LDAP": "Pokračovat s LDAP", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jak se text zpr<PERSON>vy dělí pro požadavky TTS. 'Punctuation' (interpunkce) dělí na věty, 'paragraphs' (odstavce) na odstavce a 'none' (ž<PERSON><PERSON><PERSON>) ponechá zprávu jako jeden řetězec.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "Řídí opakování sekvencí tokenů v generovaném textu. <PERSON><PERSON><PERSON><PERSON><PERSON> ho<PERSON> (např. 1,5) bude opakování penalizovat silněji, zatímco ni<PERSON> hodn<PERSON> (např. 1,1) bude mírnější. Při hodnotě 1 je funkce vypnuta.", "Controls": "Ovládací prvky", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "Řídí rovnováhu mezi soudržností a rozmanitostí výstupu. Nižší hodnota povede k soustředěnějšímu a soudržnějšímu textu.", "Conversation saved successfully": "", "Copied": "Zkopírováno", "Copied link to clipboard": "Odkaz zkopírován do schránky", "Copied shared chat URL to clipboard!": "URL sdílené konverzace zkopírována do schránky!", "Copied to clipboard": "Zkopírováno do schránky", "Copy": "Kopírovat", "Copy Formatted Text": "Kopírovat formátovaný text", "Copy last code block": "Kopírovat poslední blok kódu", "Copy last response": "Kopírovat poslední odpověď", "Copy link": "Kopírovat odkaz", "Copy Link": "Kopírovat odkaz", "Copy to clipboard": "Kopírovat do schránky", "Copying to clipboard was successful!": "Kopírování do schránky bylo úspěšné!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "CORS musí být spr<PERSON><PERSON><PERSON><PERSON> nakonfigu<PERSON> poskytovatelem, aby povolil požadavky z Open WebUI.", "Create": "Vytvořit", "Create a knowledge base": "Vytvořit znalostní bázi", "Create a model": "Vytvořit model", "Create Account": "Vytvořit účet", "Create Admin Account": "Vytvořit účet administrátora", "Create Channel": "Vytvořit kanál", "Create Folder": "Vytvořit složku", "Create Group": "Vytvořit skupinu", "Create Knowledge": "Vytvořit znalost", "Create new key": "Vytvořit nový klíč", "Create new secret key": "Vytvořit nový tajný klíč", "Create Note": "Vytvořit poznámku", "Create your first note by clicking on the plus button below.": "Vytvořte svou první poznámku kliknutím na tlačítko plus níže.", "Created at": "Vytvořeno", "Created At": "Vytvořeno", "Created by": "Vytvořil/a", "CSV Import": "Import z CSV", "Ctrl+Enter to Send": "Ctrl+Enter pro odeslání", "Current Model": "Aktuální model", "Current Password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Custom": "Vlastní", "Custom description enabled": "Vlastní popis povolen", "Custom Parameter Name": "Název vlastního parametru", "Custom Parameter Value": "Hodnota vlastního parametru", "Danger Zone": "Nebezpečná zóna", "Dark": "Tmavý", "Database": "<PERSON><PERSON><PERSON><PERSON>", "Datalab Marker API": "Datalab Marker API", "Datalab Marker API Key required.": "Je vyžadován API klíč pro Datalab Marker.", "DD/MM/YYYY": "DD.MM.RRRR", "December": "Prosinec", "Deepgram": "Deepgram", "Default": "Výchozí", "Default (Open AI)": "Výchozí (OpenAI)", "Default (SentenceTransformers)": "Výchozí (SentenceTransformers)", "Default action buttons will be used.": "Budou použita výchozí tlačítka akcí.", "Default description enabled": "Výchozí popis povolen", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "Výchozí režim funguje s <PERSON><PERSON><PERSON><PERSON> modelů voláním nástrojů jednou před spuštěním. Nativní režim využívá vestavěné schopnosti modelu pro volání n<PERSON>, ale vyžaduje, aby model tuto funkci nativně podporoval.", "Default Model": "Výchozí model", "Default model updated": "Výchozí model by<PERSON> <PERSON>.", "Default Models": "Výchozí modely", "Default permissions": "Výchozí oprávnění", "Default permissions updated successfully": "Výchozí oprávnění byla úspěšně aktualizována", "Default Prompt Suggestions": "Výchozí návrhy instrukce", "Default to 389 or 636 if TLS is enabled": "Výchozí <PERSON> 389, <PERSON><PERSON> 636, pokud je povoleno <PERSON>", "Default to ALL": "Výchozí hodnota VŠE", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "Výchozí je segmentované v<PERSON>hledávání pro cílenou a relevantní extrakci obsahu, což se doporučuje ve většině případů.", "Default User Role": "Výchozí <PERSON> už<PERSON><PERSON>", "Delete": "<PERSON><PERSON><PERSON><PERSON>", "Delete a model": "Smazat model", "Delete All Chats": "Smazat všechny konverzace", "Delete All Models": "Smazat všechny modely", "Delete chat": "Smazat konverzaci", "Delete Chat": "Smazat konverzaci", "Delete chat?": "<PERSON><PERSON>zat konverzaci?", "Delete folder?": "<PERSON><PERSON>zat složku?", "Delete function?": "<PERSON><PERSON><PERSON><PERSON>?", "Delete Message": "Smazat zprávu", "Delete message?": "Smazat zprávu?", "Delete note?": "Smazat poznámku?", "Delete prompt?": "<PERSON><PERSON><PERSON><PERSON> instrukci?", "delete this link": "smazat tento odkaz", "Delete tool?": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>j?", "Delete User": "<PERSON><PERSON><PERSON><PERSON>", "Deleted {{deleteModelTag}}": "Smazáno {{deleteModelTag}}", "Deleted {{name}}": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{name}}", "Deleted User": "<PERSON><PERSON><PERSON><PERSON>", "Deployment names are required for Azure OpenAI": "Pro Azure OpenAI jsou vyžadovány názvy nasazení", "Describe Pictures in Documents": "Popisovat obrázky v dokumentech", "Describe your knowledge base and objectives": "Popište svou znalostní bázi a cíle", "Description": "<PERSON><PERSON>", "Detect Artifacts Automatically": "Automaticky detekovat artefakty", "Dictate": "Diktovat", "Didn't fully follow instructions": "Nedodržel plně pokyny", "Direct": "Přímé", "Direct Connections": "Přímá připojení", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "Přímá připojení umožňují uživatelům připojit se k vlastním koncovým bodům API kompatibilním s OpenAI.", "Direct Tool Servers": "<PERSON>ří<PERSON> n<PERSON>", "Directory selection was cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Disable Code Interpreter": "<PERSON><PERSON><PERSON><PERSON><PERSON> interpret kódu", "Disable Image Extraction": "Zak<PERSON>zat extrakci obrázků", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "Zakázat extrakci obrázků z PDF. Pokud je povoleno Použít LLM, obrázky budou automaticky opatřeny popisky. Výchozí hodnota je False.", "Disabled": "Zak<PERSON><PERSON><PERSON><PERSON>", "Discover a function": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Discover a model": "<PERSON><PERSON><PERSON><PERSON><PERSON> model", "Discover a prompt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Discover a tool": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Discover how to use Open WebUI and seek support from the community.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jak používat Open WebUI, a vyhledejte podporu od komunity.", "Discover wonders": "Objevujte zázraky", "Discover, download, and explore custom functions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, stahujte a zkoumejte vlastní funkce", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, stahujte a prozkoumávejte vlastní instrukce", "Discover, download, and explore custom tools": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, stahujte a prozkoumávejte vlastní nástroje", "Discover, download, and explore model presets": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, stahujte a prozkoumávejte přednastavení modelů", "Display": "Zobrazení", "Display Emoji in Call": "Zobrazit emoji při hovoru", "Display Multi-model Responses in Tabs": "Zobrazit odpovědi více modelů v kartách", "Display the username instead of You in the Chat": "Zobrazit v konverzaci uživatelské jméno místo „Vy“", "Displays citations in the response": "Zobrazuje citace v odpovědi", "Dive into knowledge": "Ponořte se do znalostí", "Do not install functions from sources you do not fully trust.": "Neinstalujte funkce ze zdro<PERSON>ů, kterým plně nedůvěřujete.", "Do not install tools from sources you do not fully trust.": "Neinstalujte nástroje ze zdro<PERSON>ů, kter<PERSON>m plně nedůvěřujete.", "Docling": "<PERSON><PERSON>", "Docling Server URL required.": "Je vyžadována URL adresa serveru Docling.", "Document": "Dokument", "Document Intelligence": "Document Intelligence", "Document Intelligence endpoint required.": "", "Documentation": "Dokumentace", "Documents": "Dokumenty", "does not make any external connections, and your data stays securely on your locally hosted server.": "nevytvá<PERSON><PERSON> externí připojení a vaše data zůstávají bezpečně na vašem lokálně hostovaném serveru.", "Domain Filter List": "Seznam pro filtrování domén", "don't fetch random pipelines from sources you don't trust.": "Nestahujte náhodné pipelines ze zdrojů, kterým nedůvěřujete.", "Don't have an account?": "<PERSON>emáte <PERSON>?", "don't install random functions from sources you don't trust.": "neinstalujte náhodné funkce ze zdrojů, k<PERSON><PERSON><PERSON> nedůvěřujete.", "don't install random tools from sources you don't trust.": "neinstalujte náhodné nástroje ze zdrojů, kter<PERSON>m nedůvěřujete.", "Don't like the style": "Nelíbí se mi styl", "Done": "Hotovo", "Download": "<PERSON><PERSON><PERSON><PERSON>", "Download & Delete": "St<PERSON>hn<PERSON> a s<PERSON>zat", "Download as SVG": "Stáhnout jako SVG", "Download canceled": "Stahování zrušeno", "Download Database": "Stáhnout <PERSON>zi", "Drag and drop a file to upload or select a file to view": "Přetáhněte soubor pro nahrání nebo vyberte soubor k zobrazení", "Draw": "<PERSON><PERSON><PERSON>", "Drop any files here to upload": "Přetáhněte sem soubory pro nahrání", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "např. '30s','10m'. <PERSON><PERSON><PERSON><PERSON> j<PERSON> jsou 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "<PERSON><PERSON><PERSON> \"j<PERSON>\" nebo J<PERSON><PERSON> sch<PERSON>ma", "e.g. 60": "např. 60", "e.g. A filter to remove profanity from text": "např. Filtr pro odstranění vulgarismů z textu", "e.g. en": "např. cs", "e.g. My Filter": "<PERSON><PERSON><PERSON><PERSON>", "e.g. My Tools": "nap<PERSON><PERSON> n<PERSON>", "e.g. my_filter": "např. muj_filtr", "e.g. my_tools": "např. moje_nastroje", "e.g. pdf, docx, txt": "např. pdf, docx, txt", "e.g. Tools for performing various operations": "např. Nástroje pro provádění různých operací", "e.g., 3, 4, 5 (leave blank for default)": "např. 3, 4, 5 (pro výchozí ponechte prázdné)", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "např. audio/wav,audio/mpeg,video/* (pro výchozí ponechte prázdné)", "e.g., en-US,ja-JP (leave blank for auto-detect)": "např. cs-CZ,en-US (pro automatickou detekci ponechte prázdné)", "e.g., westus (leave blank for eastus)": "<PERSON><PERSON><PERSON> (pro výchozí ponechte prázdné)", "Edit": "<PERSON><PERSON><PERSON><PERSON>", "Edit Arena Model": "Upravit model <PERSON><PERSON><PERSON>", "Edit Channel": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "Edit Connection": "Upravit připojení", "Edit Default Permissions": "Upravit výchozí oprávnění", "Edit Folder": "Upravit složku", "Edit Memory": "Upravit vzpomínku", "Edit User": "Upravi<PERSON>", "Edit User Group": "Upravit skupinu už<PERSON>lů", "Edited": "Upraveno", "Editing": "Upravuje se", "Eject": "Vysunout", "ElevenLabs": "ElevenLabs", "Email": "E-mail", "Embark on adventures": "Vydejte se za dobrodružstvím", "Embedding": "Vektorizace", "Embedding Batch Size": "Velikost dávky pro vektorizaci", "Embedding Model": "Model pro vektorizaci", "Embedding Model Engine": "Jádro modelu pro vektorizaci", "Embedding model set to \"{{embedding_model}}\"": "Model pro vektorizaci nastaven na \"{{embedding_model}}\"", "Enable API Key": "Povolit API klíč", "Enable autocomplete generation for chat messages": "Povolit generování automatického dokončování pro zprávy v konverzaci", "Enable Code Execution": "Povolit spouštění kódu", "Enable Code Interpreter": "Povolit interpret kódu", "Enable Community Sharing": "Povolit komunitní sdílení", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Po<PERSON>lit uzamč<PERSON> (mlock), aby se z<PERSON><PERSON><PERSON><PERSON> o<PERSON>kládání dat modelu z RAM. Tato možnost uzamkne pracovní sadu stránek modelu v RAM, <PERSON><PERSON><PERSON><PERSON>, že nebudou odloženy na disk. To může pomoci udržet výkon tím, že se zabrání výpadkům stránek a zajistí rychlý přístup k datům.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Povolit mapování p<PERSON> (mmap) pro načítání dat modelu. Tato možnost umožňuje systému používat diskové úložiště jako rozšíření RAM tím, že se se soubory na disku zachází, jako by byly v RAM. To může zlepšit výkon modelu tím, že umožní rychlejší přístup k datům. nemusí však správně fungovat se všemi systémy a může spotřebovat značné množství místa na disku.", "Enable Message Rating": "Povolit hodnocení zpráv", "Enable Mirostat sampling for controlling perplexity.": "Povolit vzorkování Mirostat pro řízení perplexity.", "Enable New Sign Ups": "Povolit nové registrace", "Enable, disable, or customize the reasoning tags used by the model. \"Enabled\" uses default tags, \"Disabled\" turns off reasoning tags, and \"Custom\" lets you specify your own start and end tags.": "", "Enabled": "Povoleno", "End Tag": "", "Endpoint URL": "URL koncového bodu", "Enforce Temporary Chat": "Vynutit dočasnou konverzaci", "Enhance": "<PERSON><PERSON>pš<PERSON>", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Uji<PERSON><PERSON><PERSON> se, že váš CSV soubor obsahuje 4 sloupce v tomto pořadí: <PERSON><PERSON><PERSON>, E-<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>.", "Enter {{role}} message here": "<PERSON><PERSON> z<PERSON>jte zprávu {{role}}", "Enter a detail about yourself for your LLMs to recall": "Zadejte podrobnost o sobě, kterou si vaše LLM mají pamatovat.", "Enter a title for the pending user info overlay. Leave empty for default.": "Zadejte název pro překryvnou vrstvu s informacemi o čekajícím uživateli. Pro výchozí ponechte prázdné.", "Enter a watermark for the response. Leave empty for none.": "Zadejte vodoznak pro odpověď. Pro žádný vodoznak ponechte prázdné.", "Enter api auth string (e.g. username:password)": "Zadejte ověřovací řetězec API (např. uzivatelske_jmeno:heslo)", "Enter Application DN": "Zadejte Application DN", "Enter Application DN Password": "Zadejte heslo pro Application DN", "Enter Bing Search V7 Endpoint": "Zadejte koncový bod Bing Search V7", "Enter Bing Search V7 Subscription Key": "Zadejte klíč předplatného Bing Search V7", "Enter Bocha Search API Key": "Zadejte API klíč pro Bocha Search", "Enter Brave Search API Key": "Zadejte API klíč pro Brave Search", "Enter certificate path": "Zadejte cestu k certifikátu", "Enter CFG Scale (e.g. 7.0)": "Zadejte škálu CFG (např. 7.0)", "Enter Chunk Overlap": "Zadejte překryv bloků", "Enter Chunk Size": "Zadejte velikost bloku", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON> \"token:hodn<PERSON>_o<PERSON><PERSON><PERSON><PERSON>\" <PERSON><PERSON><PERSON><PERSON> (příklad: 5432:100, 413:-100)", "Enter Config in JSON format": "Zadejte konfiguraci ve formátu JSON", "Enter content for the pending user info overlay. Leave empty for default.": "Zadejte obsah pro překryvnou vrstvu s informacemi o čekajícím uživateli. Pro výchozí ponechte prázdné.", "Enter coordinates (e.g. 51.505, -0.09)": "Zadejte souřadnice (např. 50.0755, 14.4378)", "Enter Datalab Marker API Base URL": "Zadejte základní URL API pro Datalab Marker", "Enter Datalab Marker API Key": "Zadejte API klíč pro Datalab Marker", "Enter description": "<PERSON>adej<PERSON> popis", "Enter Docling OCR Engine": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Enter Docling OCR Language(s)": "Zadej<PERSON> jazyk(y) pro Docling OCR", "Enter Docling Server URL": "Zadejte URL serveru Docling", "Enter Document Intelligence Endpoint": "Zadejte koncový bod Document Intelligence", "Enter Document Intelligence Key": "Zadejte klíč pro Document Intelligence", "Enter domains separated by commas (e.g., example.com,site.org)": "Zadejte domény oddělen<PERSON> (např. priklad.cz,stranka.org)", "Enter Exa API Key": "Zadejte API klíč pro Exa", "Enter External Document Loader API Key": "Zadejte API klíč pro externí zavaděč dokumentů", "Enter External Document Loader URL": "Zadejte URL pro externí zavaděč dokumentů", "Enter External Web Loader API Key": "Zadejte API klíč pro externí webový zavaděč", "Enter External Web Loader URL": "Zadejte URL pro externí webový zavaděč", "Enter External Web Search API Key": "Zadejte API klíč pro externí webové vyhledávání", "Enter External Web Search URL": "Zadejte URL pro externí webové vyhledávání", "Enter Firecrawl API Base URL": "Zadejte základní URL API pro Firecrawl", "Enter Firecrawl API Key": "Zadejte API klíč pro Firecrawl", "Enter folder name": "Zadejte název složky", "Enter Github Raw URL": "Zadejte URL pro Github Raw", "Enter Google PSE API Key": "Zadejte API klíč pro Google PSE", "Enter Google PSE Engine Id": "Zadejte ID jádra Google PSE", "Enter hex color (e.g. #FF0000)": "Zadejte barvu v hex formátu (např. #FF0000)", "Enter ID": "", "Enter Image Size (e.g. 512x512)": "Zadejte velikost obrázku (např. 512x512)", "Enter Jina API Key": "Zadejte API klíč pro Jina", "Enter JSON config (e.g., {\"disable_links\": true})": "Zadejte JSON konfiguraci (např. {\"disable_links\": true})", "Enter Jupyter Password": "<PERSON><PERSON><PERSON><PERSON> pro <PERSON>", "Enter Jupyter Token": "Zadej<PERSON> token pro <PERSON><PERSON><PERSON>", "Enter Jupyter URL": "Zadejte URL pro Jupyter", "Enter Kagi Search API Key": "Zadejte API klíč pro Kagi Search", "Enter Key Behavior": "Zadejte chování klávesy", "Enter language codes": "Zadej<PERSON> kó<PERSON>", "Enter Mistral API Key": "Zadejte API klíč pro Mistral", "Enter Model ID": "Zadejte ID modelu", "Enter model tag (e.g. {{modelTag}})": "Zadejte štítek modelu (např. {{modelTag}})", "Enter Mojeek Search API Key": "Zadejte API klíč pro Mojeek Search", "Enter name": "Zadej<PERSON> j<PERSON>no", "Enter New Password": "Zadejte nov<PERSON> he<PERSON>lo", "Enter Number of Steps (e.g. 50)": "Zadejte počet krok<PERSON> (např. 50)", "Enter Perplexity API Key": "Zadejte API klíč pro Perplexity", "Enter Playwright Timeout": "<PERSON><PERSON><PERSON><PERSON>ý limit pro Playwright", "Enter Playwright WebSocket URL": "Zadejte WebSocket URL pro Playwright", "Enter proxy URL (e.g. **************************:port)": "Zadejte URL proxy (např. *******************************:port)", "Enter reasoning effort": "Zadejte úsilí pro uvažování", "Enter Sampler (e.g. Euler a)": "Zadej<PERSON> (např. Euler a)", "Enter Scheduler (e.g. Karras)": "<PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "Enter Score": "Zadejte skóre", "Enter SearchApi API Key": "Zadejte API klíč pro SearchApi", "Enter SearchApi Engine": "Zadejte jádro pro <PERSON>pi", "Enter Searxng Query URL": "Zadejte URL dotazu pro Searxng", "Enter Seed": "<PERSON><PERSON><PERSON><PERSON>", "Enter SerpApi API Key": "Zadejte API klíč pro SerpApi", "Enter SerpApi Engine": "Zadejte jádro pro SerpApi", "Enter Serper API Key": "Zadejte API klíč pro Serper", "Enter Serply API Key": "Zadejte API klíč pro Serply", "Enter Serpstack API Key": "Zadejte API klíč pro Serpstack", "Enter server host": "Zadejte hostitele serveru", "Enter server label": "<PERSON>adej<PERSON> popisek serveru", "Enter server port": "Zadejte port serveru", "Enter Sougou Search API sID": "Zadejte sID API pro Sougou Search", "Enter Sougou Search API SK": "Zadejte SK API pro Sougou Search", "Enter stop sequence": "Zadejte ukončovací sekvenci", "Enter system prompt": "Zadejte systémové instrukce", "Enter system prompt here": "Zde zadejte systémové instrukce", "Enter Tavily API Key": "Zadejte API klíč pro Tavily", "Enter Tavily Extract Depth": "Zadejte hloubku extrakce pro Tavily", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "Zadejte veřejnou URL adresu vašeho WebUI. Tato URL bude použita k generování odkazů v oznámeních.", "Enter the URL of the function to import": "Zadejte URL funkce k importu", "Enter the URL to import": "Zadejte URL pro import", "Enter Tika Server URL": "Zadejte URL serveru Tika", "Enter timeout in seconds": "Zadejte časový limit v sekundách", "Enter to Send": "Enter pro odeslání", "Enter Top K": "Zadejte Top K", "Enter Top K Reranker": "Zadejte Top K pro přehodnocení", "Enter URL (e.g. http://127.0.0.1:7860/)": "Zadejte URL (např. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Zadejte URL (např. http://localhost:11434)", "Enter value": "<PERSON><PERSON><PERSON><PERSON> hodnotu", "Enter value (true/false)": "<PERSON><PERSON><PERSON><PERSON> hodnot<PERSON> (true/false)", "Enter Yacy Password": "<PERSON><PERSON><PERSON><PERSON>", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "Zadejte URL pro Yacy (např. http://yacy.priklad.com:8090)", "Enter Yacy Username": "Zadejte uživatelské jméno pro Yacy", "Enter your code here...": "Zadejte sem svůj kód...", "Enter your current password": "Zadejte své souč<PERSON>né he<PERSON>lo", "Enter Your Email": "Zadejte svůj e-mail", "Enter Your Full Name": "Zadejte své celé j<PERSON>", "Enter your gender": "", "Enter your message": "Napište zprávu", "Enter your name": "Zadejte své j<PERSON>no", "Enter Your Name": "Zadejte své j<PERSON>no", "Enter your new password": "Zadejte své nové heslo", "Enter Your Password": "Zadejte své he<PERSON>lo", "Enter Your Role": "Zadejte svou roli", "Enter Your Username": "Zadejte své uživatelské jméno", "Enter your webhook URL": "Zadejte URL svého webhooku", "Error": "Chyba", "ERROR": "CHYBA", "Error accessing directory": "Chyba při přístupu k adresáři", "Error accessing Google Drive: {{error}}": "Chyba při přístupu ke Google Drive: {{error}}", "Error accessing media devices.": "Chyba při přístupu k mediálním zařízením.", "Error starting recording.": "Chyba při spuštění na<PERSON>ávání.", "Error unloading model: {{error}}": "Chyba při uvolňování modelu: {{error}}", "Error uploading file: {{error}}": "Chyba při nahrávání souboru: {{error}}", "Error: A model with the ID '{{modelId}}' already exists. Please select a different ID to proceed.": "Chyba: Model s ID '{{modelId}}' ji<PERSON> existuje. Pro pokračování prosím zvolte jiné ID.", "Error: Model ID cannot be empty. Please enter a valid ID to proceed.": "Chyba: ID modelu nemůže být prázdné. Pro pokračování prosím zadejte platné ID.", "Evaluations": "Hodnocení", "Everyone": "<PERSON><PERSON><PERSON><PERSON>", "Exa API Key": "API klíč pro Exa", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Příklad: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Příklad: VŠE", "Example: mail": "Příklad: mail", "Example: ou=users,dc=foo,dc=example": "Příklad: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "Příklad: sAMAccountName nebo uid nebo userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "Překročili jste počet míst ve vaší licenci. Pro navýšení počtu míst prosím kontaktujte podporu.", "Exclude": "Vyloučit", "Execute code for analysis": "Spustit kód pro analýzu", "Executing **{{NAME}}**...": "Spouštím **{{NAME}}**...", "Expand": "Rozbalit", "Experimental": "Experimentální", "Explain": "Vysvětlit", "Explore the cosmos": "Prozkoumejte vesmír", "Export": "Exportovat", "Export All Archived Chats": "Exportovat všechny archivované konverzace", "Export All Chats (All Users)": "Exportovat všechny konverzace (všichni uživatelé)", "Export chat (.json)": "<PERSON>rt<PERSON>t k<PERSON> (.json)", "Export Chats": "Exportovat konverzace", "Export Config to JSON File": "Exportovat konfiguraci do souboru JSON", "Export Functions": "Exportovat funkce", "Export Models": "Exportovat modely", "Export Presets": "Exportovat p<PERSON>", "Export Prompt Suggestions": "Exportovat návrhy instrukcí", "Export Prompts": "Exportovat instrukce", "Export to CSV": "Exportovat do CSV", "Export Tools": "Exportovat nástroje", "Export Users": "Exportovat uživatele", "External": "Externí", "External Document Loader URL required.": "Je vyžadována URL externího zavaděče dokumentů.", "External Task Model": "Externí model pro <PERSON><PERSON><PERSON>", "External Web Loader API Key": "API klíč pro externí webový zavaděč", "External Web Loader URL": "URL pro externí webový zavaděč", "External Web Search API Key": "API klíč pro externí webové vyhledávání", "External Web Search URL": "URL pro externí webové vyhledávání", "Fade Effect for Streaming Text": "Efekt prolínání pro streamovaný text", "Failed to add file.": "Nepodařilo se přidat soubor.", "Failed to connect to {{URL}} OpenAPI tool server": "Nepodařilo se připojit k serveru nástrojů OpenAPI {{URL}}", "Failed to copy link": "Nepodařilo se zkopírovat odkaz", "Failed to create API Key.": "Nepodařilo se vytvořit API klíč.", "Failed to delete note": "Nepodařilo se smazat poznámku", "Failed to extract content from the file: {{error}}": "Nepodařilo se extrahovat obsah ze souboru: {{error}}", "Failed to extract content from the file.": "Nepodařilo se extrahovat obsah ze souboru.", "Failed to fetch models": "Nepodařilo se načíst modely", "Failed to generate title": "Nepodařilo se vygenerovat název", "Failed to load chat preview": "Nepodařilo se načíst náhled konverzace", "Failed to load file content.": "Nepodařilo se načíst obsah souboru.", "Failed to move chat": "", "Failed to read clipboard contents": "Nepodařilo se přečíst obsah s<PERSON>ánky", "Failed to save connections": "Nepodařilo se uložit připojení", "Failed to save conversation": "Nepodařilo se uložit konverzaci", "Failed to save models configuration": "Nepodařilo se uložit konfiguraci modelů", "Failed to update settings": "Nepodařilo se aktualizovat nastavení", "Failed to upload file.": "Nepodařilo se nahr<PERSON>t soubor.", "Features": "Funkce", "Features Permissions": "Oprávně<PERSON><PERSON>", "February": "Únor", "Feedback Details": "Podrobnosti zpětné vazby", "Feedback History": "Historie zpětné vazby", "Feedbacks": "Zpětné vazby", "Feel free to add specific details": "Neváhejte přidat konkrétní detaily.", "Female": "", "File": "<PERSON><PERSON><PERSON>", "File added successfully.": "<PERSON><PERSON><PERSON>.", "File content updated successfully.": "<PERSON><PERSON><PERSON> byl úspěšně aktualizován.", "File Mode": "<PERSON><PERSON><PERSON>", "File not found.": "<PERSON><PERSON><PERSON>.", "File removed successfully.": "<PERSON><PERSON><PERSON> by<PERSON> ods<PERSON>něn.", "File size should not exceed {{maxSize}} MB.": "<PERSON>eli<PERSON><PERSON> souboru by <PERSON><PERSON><PERSON><PERSON> {{maxSize}} MB.", "File Upload": "<PERSON><PERSON><PERSON><PERSON> so<PERSON>", "File uploaded successfully": "<PERSON><PERSON><PERSON>", "Files": "<PERSON><PERSON><PERSON>", "Filter": "Filtr", "Filter is now globally disabled": "Filtr je nyní globálně zakázán", "Filter is now globally enabled": "Filtr je nyní globálně povolen.", "Filters": "Filtry", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Detekován spoofing otisku prstu: <PERSON><PERSON><PERSON> p<PERSON>žít iniciály jako avatar. Používá se výchozí profilový obrázek.", "Firecrawl API Base URL": "Základní URL API pro Firecrawl", "Firecrawl API Key": "API klíč pro Firecrawl", "Floating Quick Actions": "Plovoucí rychlé akce", "Focus chat input": "<PERSON><PERSON><PERSON><PERSON><PERSON> vs<PERSON>p<PERSON><PERSON> pole konverzace", "Folder deleted successfully": "S<PERSON>žka byla úspěšně s<PERSON>ána", "Folder Name": "Název složky", "Folder name cannot be empty.": "Název složky nesmí být prázdný.", "Folder name updated successfully": "Název složky byl úspěšně aktualizován.", "Folder updated successfully": "Složka byla úspěšně aktualizována", "Follow up": "Následná otázka", "Follow Up Generation": "Generování následných otázek", "Follow Up Generation Prompt": "Pokyn pro generování následných instrukcí", "Follow-Up Auto-Generation": "Automatické gene<PERSON> n<PERSON>ledných otázek", "Followed instructions perfectly": "Dokonale dodržel pokyny", "Force OCR": "Vynutit OCR", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "Vynutit OCR na všech stránkách PDF. To může vést k horším výsledkům, pokud máte v PDF kvalitní text. Výchozí hodnota je False.", "Forge new paths": "Vytvářejte nové cesty", "Form": "Formulář", "Format Lines": "Formátovat <PERSON>dky", "Format the lines in the output. Defaults to False. If set to True, the lines will be formatted to detect inline math and styles.": "Formátovat řádky ve výstupu. Výchozí hodnota je False. Pokud je nastaveno na True, řádky budou formátovány pro detekci vložené matematiky a stylů.", "Format your variables using brackets like this:": "Formátujte své proměnné pomocí složených závorek takto:", "Formatting may be inconsistent from source.": "", "Forwards system user session credentials to authenticate": "Přeposílá přihlašovací údaje relace systémového uživatele pro ověření", "Full Context Mode": "<PERSON><PERSON><PERSON> p<PERSON>ého kontex<PERSON>", "Function": "Funkce", "Function Calling": "<PERSON><PERSON><PERSON>", "Function created successfully": "Funkce byla úspěšně vytvořena.", "Function deleted successfully": "<PERSON><PERSON> byla <PERSON>š<PERSON> s<PERSON>", "Function Description": "<PERSON><PERSON>ce", "Function ID": "ID funkce", "Function imported successfully": "<PERSON><PERSON> byla úspěšně <PERSON>", "Function is now globally disabled": "Funkce je nyní globálně zakázána.", "Function is now globally enabled": "Funkce je nyní glo<PERSON>álně povolena.", "Function Name": "<PERSON><PERSON><PERSON><PERSON>", "Function updated successfully": "Funkce byla úspěšně aktualizována.", "Functions": "Funkce", "Functions allow arbitrary code execution.": "Funkce umožňují spouštění libovolného kódu.", "Functions imported successfully": "<PERSON><PERSON> byly <PERSON>", "Gemini": "Gemini", "Gemini API Config": "Konfigurace API Gemini", "Gemini API Key is required.": "Je vyžadován API klíč pro Gemini.", "Gender": "", "General": "Obecné", "Generate": "Generovat", "Generate an image": "Generovat obrázek", "Generate Image": "Generovat obrázek", "Generate prompt pair": "Generovat pár instruk<PERSON>í", "Generating search query": "Generuji vyhledávací dotaz", "Generating...": "Generuji...", "Get information on {{name}} in the UI": "Získat informace o {{name}} v uživatelském rozhraní", "Get started": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Get started with {{WEBUI_NAME}}": "Začněte s {{WEBUI_NAME}}", "Global": "Globální", "Good Response": "Dobrá odpověď", "Google Drive": "Disk Google", "Google PSE API Key": "API klíč pro Google PSE", "Google PSE Engine Id": "ID jádra Google PSE", "Gravatar": "", "Group": "<PERSON><PERSON><PERSON>", "Group created successfully": "<PERSON><PERSON><PERSON> byla úspěšně vytvořena", "Group deleted successfully": "<PERSON><PERSON><PERSON> byla <PERSON>š<PERSON> s<PERSON>", "Group Description": "<PERSON><PERSON> s<PERSON>", "Group Name": "Název skupiny", "Group updated successfully": "<PERSON><PERSON><PERSON> byla úspěšně aktualizována", "Groups": "Skupiny", "H1": "H1", "H2": "H2", "H3": "H3", "Haptic Feedback": "Haptická odezva", "Height": "Výška", "Hello, {{name}}": "<PERSON><PERSON><PERSON><PERSON> den, {{name}}", "Help": "Nápověda", "Help us create the best community leaderboard by sharing your feedback history!": "Pomozte nám vytvořit nejlepší komunitní žebříček sdílením historie vaší zpětné vazby!", "Hex Color": "<PERSON><PERSON> barva", "Hex Color - Leave empty for default color": "Hex barva - pro výchozí barvu ponechte prázdné", "Hide": "Skr<PERSON><PERSON>", "Hide from Sidebar": "Skrýt z postranního panelu", "Hide Model": "S<PERSON><PERSON><PERSON>t model", "High": "Vysoká", "High Contrast Mode": "<PERSON><PERSON><PERSON> v<PERSON>okého kont<PERSON>", "Home": "<PERSON><PERSON>", "Host": "Hostitel", "How can I help you today?": "Jak vám dnes mohu pomoci?", "How would you rate this response?": "Jak byste ohodnotili tuto odpověď?", "HTML": "HTML", "Hybrid Search": "Hybridní v<PERSON>hledávání", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Potv<PERSON><PERSON><PERSON>, že jsem si přečetl/a a rozumím důsledkům svého jednání. Jsem si vědom/a rizik spojených se spouštěním libovolného kódu a ověřil/a jsem si důvěryhodnost zdroje.", "ID": "ID", "iframe Sandbox Allow Forms": "Povolit formuláře v sandboxu iframe", "iframe Sandbox Allow Same Origin": "Povolit stejný původ v sandboxu iframe", "Ignite curiosity": "Probuďte zvědavost", "Image": "Obrázek", "Image Compression": "Komprese obrázků", "Image Compression Height": "Výška komprese obrázku", "Image Compression Width": "Šířka komprese obrázku", "Image Generation": "Generování obrázků", "Image Generation (Experimental)": "Generování o<PERSON> (experimentální)", "Image Generation Engine": "Jádro pro generování obrázků", "Image Max Compression Size": "Maximální velikost komprese obrázku", "Image Max Compression Size height": "Maximální výška komprese obrázku", "Image Max Compression Size width": "Maximální šířka komprese obrázku", "Image Prompt Generation": "Generování instrukcí pro obrázek", "Image Prompt Generation Prompt": "Instrukce pro generování instrukcí pro obrázek", "Image Settings": "Nastavení obrázků", "Images": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Import": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Import Chats": "Importovat konverzace", "Import Config from JSON File": "Importovat konfiguraci ze souboru JSON", "Import From Link": "Importovat z odkazu", "Import Functions": "Importovat funkce", "Import Models": "Importovat modely", "Import Notes": "Importovat p<PERSON>ky", "Import Presets": "Importovat <PERSON>", "Import Prompt Suggestions": "Importovat návrhy instrukcí", "Import Prompts": "Importovat instrukce", "Import Tools": "Importovat nástroje", "Important Update": "Důležitá aktualizace", "Include": "<PERSON><PERSON><PERSON><PERSON>", "Include `--api-auth` flag when running stable-diffusion-webui": "<PERSON><PERSON><PERSON> spouštění stable-diffusion-webui použijte přepínač `--api-auth`.", "Include `--api` flag when running stable-diffusion-webui": "<PERSON><PERSON>i spouštění stable-diffusion-webui použijte přepínač `--api`.", "Includes SharePoint": "Zahrnuje SharePoint", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jak rychle algoritmus reaguje na zpětnou vazbu z generovaného textu. Nižší míra učení povede k pomalejším úpravám, zatímco vyšší míra učení učiní algoritmus citlivějším.", "Info": "Info", "Initials": "", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "Vložit celý obsah jako kontext pro komplexní zpracování, doporučeno pro složité dotazy.", "Input": "<PERSON><PERSON><PERSON>", "Input commands": "Vstupní příkazy", "Input Key (e.g. text, unet_name, steps)": "Vst<PERSON><PERSON><PERSON> (např. text, unet_name, steps)", "Input Variables": "Vstupní proměnn<PERSON>", "Insert": "Vložit", "Insert Follow-Up Prompt to Input": "Vložit následné instrukce do vstupu", "Insert Prompt as Rich Text": "Vložit instrukce jako <PERSON> text", "Install from Github URL": "Instalovat z URL na Githubu", "Instant Auto-Send After Voice Transcription": "Okamžité automatické odeslání po přepisu hlasu", "Integration": "Integrace", "Interface": "Rozhraní", "Invalid file content": "Neplatný obsah souboru", "Invalid file format.": "Neplatný form<PERSON>t so<PERSON>.", "Invalid JSON file": "Neplatný soubor JSON", "Invalid JSON format for ComfyUI Workflow.": "Neplatný formát JSON pro pracovní postup ComfyUI.", "Invalid JSON format in Additional Config": "Neplatný formát JSON v dodatečné konfiguraci", "Invalid Tag": "Neplatný štítek", "is typing...": "p<PERSON><PERSON><PERSON>...", "Italic": "<PERSON><PERSON><PERSON><PERSON>", "January": "<PERSON><PERSON>", "Jina API Key": "API klíč pro Jina", "join our Discord for help.": "připojte se na náš Discord pro pomoc.", "JSON": "JSON", "JSON Preview": "<PERSON><PERSON><PERSON><PERSON> JSO<PERSON>", "July": "Červenec", "June": "Červen", "Jupyter Auth": "Ověření pro Jupy<PERSON>", "Jupyter URL": "URL pro Jupyter", "JWT Expiration": "Expirace JWT", "JWT Token": "JWT Token", "Kagi Search API Key": "API klíč pro Kagi Search", "Keep Follow-Up Prompts in Chat": "Ponechat následné instrukce v konverzaci", "Keep in Sidebar": "Ponechat v postranním panelu", "Key": "<PERSON><PERSON><PERSON><PERSON>", "Key is required": "Klíč je v<PERSON>žadován", "Keyboard shortcuts": "Klávesové zkratky", "Knowledge": "Znalosti", "Knowledge Access": "Přístup ke znalostem", "Knowledge Base": "Znalostní b<PERSON>", "Knowledge created successfully.": "Znalost byla úspěšně vytvořena.", "Knowledge deleted successfully.": "Znalost byla úspěšně s<PERSON>zána.", "Knowledge Description": "<PERSON><PERSON>", "Knowledge Name": "Název znalosti", "Knowledge Public Sharing": "Veřejné sdílení znalostí", "Knowledge reset successfully.": "Znalosti by<PERSON><PERSON> reset<PERSON>.", "Knowledge updated successfully": "Znalost byla úspěšně aktualizována", "Kokoro.js (Browser)": "Kokoro.js (prohlížeč)", "Kokoro.js Dtype": "Datový typ Kokoro.js", "Label": "<PERSON><PERSON><PERSON>", "Landing Page Mode": "<PERSON><PERSON><PERSON>", "Language": "Jazyk", "Language Locales": "Jazykov<PERSON> mutace", "Last Active": "Naposledy aktivní", "Last Modified": "Poslední úprava", "Last reply": "Poslední odpověď", "LDAP": "LDAP", "LDAP server updated": "LDAP server byl aktualizován", "Leaderboard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Learn More": "Zjistit více", "Learn more about OpenAPI tool servers.": "Zjistěte více o serverech nástrojů OpenAPI.", "Leave empty for no compression": "Pro žádnou kompresi ponechte prázdné", "Leave empty for unlimited": "Pro neomezeně ponechte prázdné", "Leave empty to include all models from \"{{url}}\" endpoint": "Ponechte prázdné pro zahrnutí všech modelů z koncového bodu \"{{url}}\"", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "Ponechte prázdné pro zahrnutí všech modelů z koncového bodu \"{{url}}/api/tags\"", "Leave empty to include all models from \"{{url}}/models\" endpoint": "Ponechte prázdné pro zahrnutí všech modelů z koncového bodu \"{{url}}/models\"", "Leave empty to include all models or select specific models": "Ponechte prázdné pro zahrnutí všech modelů nebo vyberte konkrétní modely.", "Leave empty to use the default prompt, or enter a custom prompt": "Ponechte prázdné pro použití výchozích instrukcí, nebo zadejte vlastní instrukce.", "Leave model field empty to use the default model.": "Ponechte pole modelu prázdné pro použití výchozího modelu.", "lexical": "lexikální", "License": "Licence", "Lift List": "Zvýraznit seznam", "Light": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Listening...": "Poslouchám...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "LLM mohou dělat chyby. Ověřte si důležité informace.", "Loader": "<PERSON><PERSON><PERSON><PERSON>č", "Loading Kokoro.js...": "Načítám <PERSON>.js...", "Loading...": "Načítám...", "Local": "Lokální", "Local Task Model": "Lokální model pro <PERSON><PERSON><PERSON>", "Location access not allowed": "Přístup k poloze nebyl povolen", "Lost": "<PERSON><PERSON><PERSON><PERSON>", "Low": "Nízká", "LTR": "LTR", "Made by Open WebUI Community": "Vytvořeno komunitou Open WebUI", "Make password visible in the user interface": "Zviditelnit heslo v uživatelském rozhraní", "Make sure to enclose them with": "Ujistěte se, že jsou uzavřeny v", "Make sure to export a workflow.json file as API format from ComfyUI.": "Uji<PERSON><PERSON><PERSON> se, že exportujete soubor workflow.json ve formátu API z ComfyUI.", "Male": "", "Manage": "Spravovat", "Manage Direct Connections": "Spravovat přímá připojení", "Manage Models": "Spravovat modely", "Manage Ollama": "<PERSON><PERSON><PERSON><PERSON>", "Manage Ollama API Connections": "Spravovat připojení k Ollama API", "Manage OpenAI API Connections": "Spravovat připojení k OpenAI API", "Manage Pipelines": "Spravovat potrubí", "Manage Tool Servers": "Spravovat servery nástrojů", "Manage your account information.": "", "March": "Březen", "Markdown": "<PERSON><PERSON>", "Markdown (Header)": "Markdown (nadpis)", "Max Speakers": "<PERSON><PERSON>", "Max Upload Count": "Maximální počet nahrání", "Max Upload Size": "Maximální velikost nahrávaných souborů", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Současně lze stahovat maximálně 3 modely. Zkuste to prosím později.", "May": "<PERSON><PERSON><PERSON><PERSON>", "Medium": "Střední", "Memories accessible by LLMs will be shown here.": "Zde se zobrazí vzpomínky přístupné pro LLM.", "Memory": "Paměť", "Memory added successfully": "Vzpomínka byla úspěšně přidána.", "Memory cleared successfully": "Paměť byla úspěšně v<PERSON>azána.", "Memory deleted successfully": "Vzpomínka byla úspěšně s<PERSON>a", "Memory updated successfully": "Vzpomínka byla úspěšně aktualizována", "Merge Responses": "Sloučit odpovědi", "Merged Response": "Sloučená odpověď", "Message rating should be enabled to use this feature": "Pro použití této funkce musí být povoleno hodnocení zpráv.", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> po vytvoření o<PERSON>, nebu<PERSON>u sdíleny. Uživatelé s URL adresou budou moci zobrazit sdílenou konverzaci.", "Microsoft OneDrive": "Microsoft OneDrive", "Microsoft OneDrive (personal)": "Microsoft OneDrive (osobní)", "Microsoft OneDrive (work/school)": "Microsoft OneDrive (pracovní/školní)", "Mistral OCR": "Mistral OCR", "Mistral OCR API Key required.": "Je vyžadován API klíč pro Mistral OCR.", "Model": "Model", "Model '{{modelName}}' has been successfully downloaded.": "Model '{{modelName}}' by<PERSON> <PERSON><PERSON><PERSON> s<PERSON>.", "Model '{{modelTag}}' is already in queue for downloading.": "Model '{{modelTag}}' je ji<PERSON> ve <PERSON> na stažení.", "Model {{modelId}} not found": "Model {{modelId}} ne<PERSON>zen", "Model {{modelName}} is not vision capable": "Model {{modelName}} nemá schopnost zpracování obrazu.", "Model {{name}} is now {{status}}": "Model {{name}} je nyní {{status}}.", "Model {{name}} is now hidden": "Model {{name}} je n<PERSON><PERSON>", "Model {{name}} is now visible": "Model {{name}} je n<PERSON><PERSON>", "Model accepts file inputs": "Model př<PERSON><PERSON><PERSON><PERSON><PERSON> souborové vstu<PERSON>", "Model accepts image inputs": "Model přijí<PERSON><PERSON> obrazové vstupy", "Model can execute code and perform calculations": "Model um<PERSON> spo<PERSON><PERSON><PERSON><PERSON> kód a provád<PERSON>t výpočty", "Model can generate images based on text prompts": "Model umí generovat obrázky na základě textových instrukcí", "Model can search the web for information": "Model umí vyhledávat informace na webu", "Model created successfully!": "Model byl úspěšně vytvořen!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "<PERSON><PERSON> cesta k souborovému systému modelu. Pro aktualizaci je vyžadován krátký název modelu, nel<PERSON> p<PERSON>.", "Model Filtering": "Filtrování modelů", "Model ID": "ID modelu", "Model ID is required.": "ID modelu je v<PERSON>.", "Model IDs": "ID modelů", "Model Name": "Název modelu", "Model name already exists, please choose a different one": "Název modelu již existuje, zvolte prosím jiný", "Model Name is required.": "Název modelu je v<PERSON>žadován.", "Model not selected": "<PERSON><PERSON><PERSON> model", "Model Params": "Parametry modelu", "Model Permissions": "Oprávnění modelu", "Model unloaded successfully": "Model by<PERSON><PERSON><PERSON>n", "Model updated successfully": "Model by<PERSON><PERSON><PERSON> aktualiz<PERSON>n", "Model(s) do not support file upload": "<PERSON><PERSON><PERSON><PERSON><PERSON> model (modely) nepodporuje nahrávání <PERSON>", "Modelfile Content": "<PERSON><PERSON><PERSON> modelfile", "Models": "<PERSON><PERSON>", "Models Access": "Přístup k modelům", "Models configuration saved successfully": "Konfigurace modelů <PERSON><PERSON>", "Models Public Sharing": "Veř<PERSON><PERSON><PERSON>ílení modelů", "Mojeek Search API Key": "API klíč pro Mojeek Search", "more": "více", "More": "V<PERSON>ce", "More Concise": "Stručnější", "More Options": "<PERSON><PERSON><PERSON>", "Move": "", "Name": "Jméno", "Name and ID are required, please fill them out": "Jméno a <PERSON> jsou povinn<PERSON>, prosím vyplňte je", "Name your knowledge base": "Pojmenujte svou znalostní bázi", "Native": "Nativní", "New Button": "<PERSON><PERSON>", "New Chat": "Nová konverzace", "New Folder": "Nová složka", "New Function": "<PERSON><PERSON> funkce", "New Note": "Nová poznámka", "New Password": "<PERSON><PERSON>", "New Tool": "Nový nástroj", "new-channel": "novy-kanal", "Next message": "<PERSON><PERSON><PERSON>", "No chats found": "Nebyly nalezeny žádné konverzace", "No chats found for this user.": "Pro tohoto uživatele nebyly nalezeny žádné konverzace.", "No chats found.": "Nebyly nalezeny žádné konverzace.", "No content": "Žádný obsah", "No content found": "Nebyl nalezen žádný obsah.", "No content found in file.": "V souboru nebyl nalezen žádný obsah.", "No content to speak": "Žádný obsah k přečtení.", "No conversation to save": "", "No distance available": "Vzdálenost není k dispozici", "No feedbacks found": "Nebyla nalezena žádná zpětná vazba", "No file selected": "Nebyl vybrán žádný soubor", "No groups with access, add a group to grant access": "Ž<PERSON>dné skupiny s přístupem, přidejte skupinu pro udělení přístupu", "No HTML, CSS, or JavaScript content found.": "Nebyl nalezen žádný obsah HTML, CSS ani JavaScriptu.", "No inference engine with management support found": "Nebyl nalezeno žádné inferenční jádro s podporou správy", "No knowledge found": "Nebyly nalezeny žádné znalosti", "No memories to clear": "Žádné vzpomínky k vymazání", "No model IDs": "Žádná ID modelů", "No models found": "Nebyly nalezeny žádné modely", "No models selected": "Nebyly vybrány žádné modely", "No Notes": "<PERSON><PERSON><PERSON><PERSON>", "No results": "Nebyly nalezeny žádné výsledky", "No results found": "Nebyly nalezeny žádné výsledky", "No search query generated": "Nebyl vygenerován žádný vyhledávací dotaz.", "No source available": "Není k dispozici žádný zdroj.", "No suggestion prompts": "Ž<PERSON><PERSON><PERSON> n<PERSON> promptů", "No users were found.": "Nebyli nalezeni žádní uživatelé.", "No valves": "<PERSON><PERSON><PERSON><PERSON>", "No valves to update": "Žádné ventily k aktualizaci", "Node Ids": "ID uzlů", "None": "<PERSON><PERSON><PERSON><PERSON>", "Not factually correct": "Fakticky nesprávné", "Not helpful": "Nepomohlo", "Note deleted successfully": "Poznámka byla úspěšně s<PERSON>a", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Poznámka: Pokud nastavíte minimální skóre, vyhledávání vrátí pouze dokumenty se skóre vyšším nebo rovným minimálnímu skóre.", "Notes": "Poznámky", "Notification Sound": "Zvuk oznámení", "Notification Webhook": "Webhook pro oznámení", "Notifications": "Oznámení", "November": "Listopad", "OAuth ID": "OAuth ID", "October": "Říjen", "Off": "Vypnuto", "Okay, Let's Go!": "<PERSON><PERSON><PERSON><PERSON>, jdeme na to!", "OLED Dark": "OLED tmavý", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "Nastavení Ollama API byla aktualizována", "Ollama Version": "<PERSON><PERSON><PERSON>", "On": "Zapnuto", "OneDrive": "OneDrive", "Only active when \"Paste Large Text as File\" setting is toggled on.": "Aktivní pouze pokud je zapnuta volba \"Vložit velký text jako soubor\".", "Only active when the chat input is in focus and an LLM is generating a response.": "Aktivní pouze pokud je zaměřeno vstupní pole konverzace a LLM generuje odpověď.", "Only alphanumeric characters and hyphens are allowed": "Jsou povoleny pouze alfanumerické znaky a pomlčky", "Only alphanumeric characters and hyphens are allowed in the command string.": "V řetězci příkazu jsou povoleny pouze alfanumerické znaky a pomlčky.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Lze upravovat pouze kole<PERSON>, pro úpravu/přidání dokumentů vytvořte novou znalostní bázi.", "Only markdown files are allowed": "<PERSON><PERSON><PERSON> povoleny pouze soubory markdown", "Only select users and groups with permission can access": "Přístup mají pouze vybraní uživatelé a skupiny s oprávněním", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Jejda! Zdá se, že URL adresa je neplatná. Zkontrolujte ji prosím a zkuste to znovu.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Jejda! Některé soubory se stále nahrávají. Počkejte prosím na dokončení nahrávání.", "Oops! There was an error in the previous response.": "Jejda! V předchozí odpovědi došlo k chybě.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Jejda! Používáte nepodporovanou metodu (pouze frontend). Spusťte prosím WebUI z backendu.", "Open file": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Open in full screen": "Otevřít na celou obrazovku", "Open modal to configure connection": "Otevřít modální okno pro konfiguraci připojení", "Open Modal To Manage Floating Quick Actions": "Otevřít modální okno pro správu plovoucích rychlých akcí", "Open new chat": "Otevřít novou konverzaci", "Open Sidebar": "Otevřít postranní panel", "Open User Profile Menu": "Otevřít nabídku profilu uživatele", "Open WebUI can use tools provided by any OpenAPI server.": "Open WebUI může používat nástroje poskytované jakýmkoli serverem OpenAPI.", "Open WebUI uses faster-whisper internally.": "Open WebUI intern<PERSON> p<PERSON>ív<PERSON> faster-whisper.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI používá SpeechT5 a vektorizaci mluvčích CMU Arctic.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Verze Open WebUI (v{{OPEN_WEBUI_VERSION}}) je niž<PERSON>í než požadovaná verze (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "Konfigurace OpenAI API", "OpenAI API Key is required.": "Je vyžadován klíč OpenAI API.", "OpenAI API settings updated": "Nastavení OpenAI API byla aktualizována", "OpenAI URL/Key required.": "Je vyžadována URL/klíč OpenAI.", "openapi.json URL or Path": "URL nebo cesta k openapi.json", "Optional": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "Možnosti pro spuštění lokálního vizuálně-jazykového modelu v popisu obrázku. Parametry odkazují na model hostovaný na Hugging Face. Tento parametr je vzájemně se vylučující s picture_description_api.", "or": "nebo", "Ordered List": "Číslovaný seznam", "Organize your users": "Uspořádejte své uživatele", "Other": "<PERSON><PERSON>", "OUTPUT": "VÝSTUP", "Output format": "Formát výstupu", "Output Format": "Formát výstupu", "Overview": "<PERSON><PERSON><PERSON><PERSON>", "page": "<PERSON><PERSON><PERSON><PERSON>", "Paginate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Parameters": "Parametry", "Password": "He<PERSON><PERSON>", "Passwords do not match.": "<PERSON><PERSON> se neshod<PERSON>.", "Paste Large Text as File": "V<PERSON>ž<PERSON> velký text jako soubor", "PDF document (.pdf)": "Dokument PDF (.pdf)", "PDF Extract Images (OCR)": "Extrahovat obrázky z PDF (OCR)", "pending": "čeká na vyřízení", "Pending": "Čeká na vyřízení", "Pending User Overlay Content": "Obsah překryvné vrstvy pro čekajícího už<PERSON>le", "Pending User Overlay Title": "Název překryvné vrstvy pro čekajícího už<PERSON>le", "Permission denied when accessing media devices": "Přístup k mediálním zařízením byl odepřen", "Permission denied when accessing microphone": "Přístup k mikrofonu byl odepřen", "Permission denied when accessing microphone: {{error}}": "Přístup k mikrofonu byl odepřen: {{error}}", "Permissions": "Oprávnění", "Perplexity API Key": "API klíč pro Perplexity", "Perplexity Model": "Model Perplexity", "Perplexity Search Context Usage": "Využití kontextu vyhledávání Perplexity", "Personalization": "Personalizace", "Picture Description API Config": "Konfigurace API pro popis obrázků", "Picture Description Local Config": "Lokální konfigurace pro popis obrázků", "Picture Description Mode": "<PERSON><PERSON><PERSON> obr<PERSON>", "Pin": "Připnout", "Pinned": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pioneer insights": "Objevujte nové p<PERSON>ky", "Pipe": "Roura", "Pipeline deleted successfully": "Potrubí <PERSON> s<PERSON>", "Pipeline downloaded successfully": "Potrubí byla <PERSON>š<PERSON>ě s<PERSON>", "Pipelines": "Potrubí", "Pipelines are a plugin system with arbitrary code execution —": "Pipelines je zásuvný systém s možností libovolného spouštění kódu —", "Pipelines Not Detected": "<PERSON><PERSON><PERSON><PERSON>kovány žádné <PERSON>", "Pipelines Valves": "<PERSON>ent<PERSON> <PERSON><PERSON><PERSON><PERSON>", "Plain text (.md)": "Prostý text (.md)", "Plain text (.txt)": "Prostý text (.txt)", "Playground": "Hř<PERSON><PERSON><PERSON><PERSON>", "Playwright Timeout (ms)": "Časov<PERSON> limit Playwright (ms)", "Playwright WebSocket URL": "WebSocket URL pro Playwright", "Please carefully review the following warnings:": "Prosím, pe<PERSON>livě si přečtěte následující varování:", "Please do not close the settings page while loading the model.": "Během načítání modelu prosím nezavírejte stránku s nastavením.", "Please enter a message or attach a file.": "", "Please enter a prompt": "Zadejte prosím instrukce.", "Please enter a valid path": "Zadejte prosím platnou cestu", "Please enter a valid URL": "Zadejte prosím platnou URL", "Please fill in all fields.": "Vyplňte prosím všechna pole.", "Please select a model first.": "Nejprve prosím vyberte model.", "Please select a model.": "<PERSON><PERSON><PERSON><PERSON> prosím model.", "Please select a reason": "<PERSON><PERSON><PERSON><PERSON> pro<PERSON>ím d<PERSON>", "Please wait until all files are uploaded.": "Prosím počkejte dokud nebudou všechny soubory nahrány.", "Port": "Port", "Positive attitude": "Pozitivní přístup", "Prefer not to say": "", "Prefix ID": "Prefix ID", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "Prefix ID se používá k zamezení konfliktů s jinými připojeními přidáním prefixu k ID modelů - pro vypnutí ponechte prázdné", "Prevent file creation": "Zabránit vytváření souborů", "Preview": "<PERSON><PERSON><PERSON><PERSON>", "Previous 30 days": "Posledních 30 dní", "Previous 7 days": "Posledních 7 dní", "Previous message": "Předchozí zpráva", "Private": "<PERSON><PERSON><PERSON><PERSON>", "Profile": "Profil", "Prompt": "Instrukce", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Instrukce (např. Řekni mi zajímavost o Římské říši)", "Prompt Autocompletion": "Automatické <PERSON>ňování instrukcí", "Prompt Content": "<PERSON><PERSON><PERSON> instruk<PERSON>", "Prompt created successfully": "Pokyn byl úspěšně vytvořen", "Prompt suggestions": "Návrhy instrukcí", "Prompt updated successfully": "Instrukce byly úspěšně aktualizovány", "Prompts": "Instrukce", "Prompts Access": "Přístup k instrukcím", "Prompts Public Sharing": "Veřejné sdílení instrukcí", "Public": "Veřejné", "Pull \"{{searchValue}}\" from Ollama.com": "Stáhnout \"{{searchValue}}\" z Ollama.com", "Pull a model from Ollama.com": "Stáhnout model z Ollama.com", "Query Generation Prompt": "Instrukce pro generování dotazu", "Quick Actions": "<PERSON><PERSON><PERSON> a<PERSON>ce", "RAG Template": "Šablona RAG", "Rating": "Hodnocení", "Re-rank models by topic similarity": "Znovu seřadit modely podle podobnosti témat", "Read": "P<PERSON><PERSON><PERSON><PERSON><PERSON>", "Read Aloud": "Číst nahlas", "Reason": "Důvod", "Reasoning Effort": "Úsilí pro uvažování", "Reasoning Tags": "", "Record": "<PERSON><PERSON><PERSON><PERSON>", "Record voice": "<PERSON><PERSON><PERSON><PERSON>", "Redirecting you to Open WebUI Community": "Přesměrovávám vás do komunity Open WebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Odkazujte na sebe jako na \"Uživatele\" (např. \"Uživatel se učí španělsky\").", "References from": "Reference z", "Refused when it shouldn't have": "Od<PERSON><PERSON><PERSON><PERSON><PERSON>, i když nemělo být", "Regenerate": "Znovu generovat", "Regenerate Menu": "Nabídka Znovu generovat", "Reindex": "Přeindexovat", "Reindex Knowledge Base Vectors": "Přeindexovat vektory znalostní báze", "Release Notes": "Poznámky k vydání", "Releases": "Vydání", "Relevance": "Relevance", "Relevance Threshold": "Prahová hodnota relevance", "Remember Dismissal": "Pamatovat si zavření", "Remove": "<PERSON><PERSON><PERSON><PERSON>", "Remove {{MODELID}} from list.": "Odebrat {{MODELID}} ze seznamu.", "Remove file": "<PERSON><PERSON><PERSON><PERSON> soubor", "Remove File": "<PERSON><PERSON><PERSON><PERSON> soubor", "Remove image": "<PERSON><PERSON><PERSON><PERSON>", "Remove Model": "Odebrat model", "Remove this tag from list": "Odebrat tento štítek ze seznamu", "Rename": "Př<PERSON>menovat", "Reorder Models": "Změnit pořadí modelů", "Reply in Thread": "<PERSON><PERSON><PERSON>v<PERSON><PERSON><PERSON>t ve vlákně", "Reranking Engine": "Jádro pro přehodnocení", "Reranking Model": "Model pro přehodnocení", "Reset": "<PERSON><PERSON><PERSON><PERSON>", "Reset All Models": "Resetovat všechny modely", "Reset Image": "Resetovat obrázek", "Reset Upload Directory": "Resetovat adresář pro nahrávání", "Reset Vector Storage/Knowledge": "Resetovat vektorov<PERSON>/znalosti", "Reset view": "Resetovat zobrazení", "Response": "Odpověď", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Oznámení o odpovědích nelze aktivovat, protože oprávnění webu byla zamítnuta. Navštivte nastavení svého prohlížeče a udělte potřebný přístup.", "Response splitting": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "Response Watermark": "Vodoznak odpovědi", "Result": "<PERSON><PERSON><PERSON><PERSON>", "RESULT": "<PERSON><PERSON><PERSON><PERSON>", "Retrieval": "Vyhledávání", "Retrieval Query Generation": "Generování vyhledávacího dotazu", "Rich Text Input for Chat": "Pokroč<PERSON><PERSON> vstupního pole konverzace", "RK": "RK", "Role": "Role", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "<PERSON><PERSON><PERSON><PERSON>", "Running": "Běží", "Running...": "Běží...", "Save": "Uložit", "Save & Create": "Uložit a vytvořit", "Save & Update": "Uložit a aktualizovat", "Save As Copy": "Uložit jako kopii", "Save Chat": "", "Save Tag": "Uložit štítek", "Saved": "Uloženo", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Ukládání záznamů konverzací přímo do úložiště vašeho prohlížeče již není podpor<PERSON>no. Věnujte prosím chvíli stažení a smazání svých záznamů konverzací kliknutím na tlačítko níže. Nemějte obavy, své záznamy konverzací můžete snadno znovu importovat do backendu prostřednictvím", "Scroll On Branch Change": "Posouvat při změně větve", "Search": "Hledat", "Search a model": "Hledat model", "Search all emojis": "Hledat všechny emoji", "Search Base": "Základ pro vyhledávání", "Search Chats": "Hledat v konverzacích", "Search Collection": "Hledat v kolekci", "Search Filters": "Filtry vyhledávání", "search for archived chats": "hledat archivované konverzace", "search for folders": "hledat složky", "search for pinned chats": "hledat připnuté konverzace", "search for shared chats": "hledat sdílené konverzace", "search for tags": "hledat štítky", "Search Functions": "Hledat funkce", "Search In Models": "Hledat v modelech", "Search Knowledge": "Hledat ve znalostech", "Search Models": "Hledat modely", "Search Notes": "Hledat poznámky", "Search options": "Možnosti vyhledávání", "Search Prompts": "Hledat instrukce", "Search Result Count": "Počet výsledků hledání", "Search the internet": "Hledat na internetu", "Search Tools": "Hledat nástroje", "SearchApi API Key": "API klíč pro SearchApi", "SearchApi Engine": "Jádro pro <PERSON>pi", "Searched {{count}} sites": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} s<PERSON><PERSON><PERSON>", "Searching \"{{searchQuery}}\"": "Hledám \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "Hledám ve znalostech \"{{searchQuery}}\"", "Searching the web...": "Hledám na webu...", "Searxng Query URL": "URL dotazu pro Searxng", "See readme.md for instructions": "Pokyny naleznete v souboru readme.md.", "See what's new": "Podívejte se, co je nového", "Seed": "<PERSON><PERSON><PERSON><PERSON>", "Select": "<PERSON><PERSON><PERSON><PERSON>", "Select a base model": "<PERSON><PERSON><PERSON><PERSON> model", "Select a base model (e.g. llama3, gpt-4o)": "Vyberte z<PERSON>í model (např. llama3, gpt-4o)", "Select a conversation to preview": "Vyberte konverzaci pro náhled", "Select a engine": "<PERSON><PERSON><PERSON><PERSON>", "Select a function": "<PERSON><PERSON><PERSON><PERSON>", "Select a group": "<PERSON><PERSON><PERSON><PERSON>", "Select a language": "<PERSON><PERSON><PERSON><PERSON>", "Select a mode": "<PERSON><PERSON><PERSON><PERSON>", "Select a model": "Vyberte model", "Select a model (optional)": "<PERSON><PERSON><PERSON><PERSON> model (volitelné)", "Select a pipeline": "<PERSON><PERSON><PERSON><PERSON>", "Select a pipeline url": "Vyberte URL potrubí", "Select a reranking model engine": "Vyberte j<PERSON>dro modelu pro přehodnocení", "Select a role": "Vyberte roli", "Select a theme": "Vyberte motiv", "Select a tool": "<PERSON><PERSON><PERSON><PERSON>", "Select a voice": "<PERSON><PERSON><PERSON><PERSON> hlas", "Select an auth method": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "Select an embedding model engine": "Vyberte jádro modelu pro vektorizaci", "Select an engine": "<PERSON><PERSON><PERSON><PERSON>", "Select an Ollama instance": "<PERSON><PERSON><PERSON><PERSON> instanci <PERSON>", "Select an output format": "<PERSON><PERSON><PERSON><PERSON> form<PERSON> v<PERSON>", "Select dtype": "<PERSON><PERSON><PERSON><PERSON> typ", "Select Engine": "<PERSON><PERSON><PERSON><PERSON>", "Select how to split message text for TTS requests": "<PERSON><PERSON><PERSON><PERSON>, jak dě<PERSON> text zprávy pro požadavky TTS", "Select Knowledge": "Vybrat znalosti", "Select only one model to call": "<PERSON><PERSON><PERSON><PERSON> pouze jeden model k volání", "Selected model(s) do not support image inputs": "Vybra<PERSON><PERSON> model (modely) nepodporuje obrazové vstupy.", "semantic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Semantic distance to query": "Sémantická vzdálenost od dotazu", "Send": "<PERSON><PERSON><PERSON>", "Send a Message": "Odeslat zprávu", "Send message": "Odeslat zprávu", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Odešle `stream_options: { include_usage: true }` v požadavku.\nPodporovaní poskytovatelé vrátí informace o využití tokenů v odpovědi, pokud je tato možnost nastavena.", "September": "Září", "SerpApi API Key": "API klíč pro SerpApi", "SerpApi Engine": "Jádro pro SerpApi", "Serper API Key": "API klíč pro Serper", "Serply API Key": "API klíč pro Serply", "Serpstack API Key": "API klíč pro Serpstack", "Server connection verified": "Připojení k serveru ověřeno", "Session": "<PERSON><PERSON>", "Set as default": "Nastavit jako výchozí", "Set CFG Scale": "Nastavit škálu CFG", "Set Default Model": "Na<PERSON><PERSON><PERSON> výchozí model", "Set embedding model": "Nastavit model pro vektorizaci", "Set embedding model (e.g. {{model}})": "Nastavit model pro vektorizaci (např. {{model}})", "Set Image Size": "Nastavit velikost obrázku", "Set reranking model (e.g. {{model}})": "Nastavit model pro přehodnocení (např. {{model}})", "Set Sampler": "Nastavit vzorkovač", "Set Scheduler": "<PERSON><PERSON><PERSON><PERSON>", "Set Steps": "Nastavit kroky", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "Nastavte poč<PERSON> v<PERSON>ev, k<PERSON><PERSON> budou přesunuty na GPU. Zvýšení této hodnoty může výrazně zlepšit výkon u modelů optimalizovaných pro akceleraci na GPU, ale může také spotřeb<PERSON>t více energie a prostředků GPU.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "Nastavte počet pracovních vláken použitých pro výpočty. Tato možnost řídí, kolik vláken se používá ke souběžnému zpracování příchozích požadavků. Zvýšení této hodnoty může zlepšit výkon při vysokém souběžném zatížení, ale může také spotřebovat více prostředků CPU.", "Set Voice": "<PERSON><PERSON><PERSON><PERSON> hlas", "Set whisper model": "Nastavit model <PERSON><PERSON><PERSON>", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Nastaví plošnou penalizaci proti tokenům, kter<PERSON> se objevily alespoň jednou. <PERSON><PERSON><PERSON><PERSON><PERSON> (např. 1,5) bude opakování penalizovat silněji, zatímco <PERSON> ho<PERSON> (např. 0,9) bude mírnější. Při hodnotě 0 je funkce vypnuta.", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Nastaví škálovací penalizaci proti tokenům pro penalizaci opakování na základě toho, kolikr<PERSON>t se objevily. <PERSON><PERSON><PERSON><PERSON><PERSON> (např. 1,5) bude opakování penalizovat silněji, zatímco ni<PERSON> ho<PERSON> (např. 0,9) bude mírnější. Při hodnotě 0 je funkce vypnuta.", "Sets how far back for the model to look back to prevent repetition.": "<PERSON><PERSON><PERSON><PERSON>, jak <PERSON> se má model dí<PERSON>, aby se z<PERSON><PERSON><PERSON><PERSON>.", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "Nastavuje semínko náhodných čísel pro generování. Nastavení na konkrétní číslo z<PERSON>ůsobí, že model bude generovat stejný text pro stejné instrukce.", "Sets the size of the context window used to generate the next token.": "Nastavuje velikost kontextového okna použitého k vygenerování dalšího tokenu.", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Nastavuje ukončovací sekvence. K<PERSON>ž je tento vzor na<PERSON>zen, LLM přestane generovat text a vrátí výsledek. Lze nastavit více ukončovacích vzorů zadáním více samostatných parametrů stop v souboru modelfile.", "Settings": "Nastavení", "Settings saved successfully!": "Nastavení byla úspěšně uložena!", "Share": "Sdílet", "Share Chat": "Sdílet konverzaci", "Share to Open WebUI Community": "Sdílet s komunitou Open WebUI", "Share your background and interests": "", "Sharing Permissions": "Oprávnění pro sdílení", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "Zkratky s hvězdičkou (*) jsou situační a aktivní pouze za specifických podmínek.", "Show": "Zobrazit", "Show \"What's New\" modal on login": "Zobrazit okno \"Co je nového\" při přihlášení", "Show Admin Details in Account Pending Overlay": "Zobrazit podrobnosti administrátora v překryvné vrstvě čekajícího účtu", "Show All": "Zobrazit vše", "Show Formatting Toolbar": "Zobrazit panel nástrojů pro formátování", "Show image preview": "Zobrazit náhled obrázku", "Show Less": "Zobrazit méně", "Show Model": "Zobrazit model", "Show shortcuts": "Zobrazit zkratky", "Show your support!": "Vyjádřete svou podporu!", "Showcased creativity": "Předvedená kreativita", "Sign in": "Přihlásit se", "Sign in to {{WEBUI_NAME}}": "Přihlásit se do {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "Přihlásit se do {{WEBUI_NAME}} pomocí LDAP", "Sign Out": "Odhlásit se", "Sign up": "Zaregistrovat se", "Sign up to {{WEBUI_NAME}}": "Zaregistrovat se do {{WEBUI_NAME}}", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to False.": "Výrazně zlepšuje přesnost použitím LLM k vylepšení tabulek, form<PERSON><PERSON><PERSON><PERSON>, vložené matematiky a detekce rozvržení. Zvýší latenci. Výchozí hodnota je False.", "Signing in to {{WEBUI_NAME}}": "Přihlašování do {{WEBUI_NAME}}", "Sink List": "Seznam cílů", "sk-1234": "sk-1234", "Skip Cache": "Přeskočit mezipaměť", "Skip the cache and re-run the inference. Defaults to False.": "Přeskočit mezipaměť a znovu spustit inferenci. Výchozí hodnota je False.", "Something went wrong :/": "Něco se pokazilo :/", "Sonar": "Sonar", "Sonar Deep Research": "Sonar Deep Research", "Sonar Pro": "Sonar Pro", "Sonar Reasoning": "Sonar Reasoning", "Sonar Reasoning Pro": "Sonar Reasoning Pro", "Sougou Search API sID": "sID API pro Sougou Search", "Sougou Search API SK": "SK API pro Sougou Search", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Speech Playback Speed": "Rychlost přehrávání <PERSON>", "Speech recognition error: {{error}}": "Chyba rozpozná<PERSON>í <PERSON>: {{error}}", "Speech-to-Text": "Převod řeči na text", "Speech-to-Text Engine": "Jádro pro převod řeči na text", "Start of the channel": "Začátek kanálu", "Start Tag": "", "STDOUT/STDERR": "STDOUT/STDERR", "Stop": "<PERSON><PERSON><PERSON><PERSON>", "Stop Generating": "<PERSON><PERSON><PERSON><PERSON>", "Stop Sequence": "Ukončovací sekvence", "Stream Chat Response": "Streamovat odpověď konverzace", "Stream Delta Chunk Size": "Velikost bloku streamu delta", "Strikethrough": "Přeškrtnutí", "Strip Existing OCR": "Odstranit existující OCR", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "Odstranit existující text OCR z PDF a znovu spustit OCR. Ignorováno, pokud je povoleno Vynutit OCR. Výchozí hodnota je False.", "STT Model": "Model STT", "STT Settings": "Nastavení STT", "Stylized PDF Export": "Stylizovaný export do PDF", "Subtitle (e.g. about the Roman Empire)": "Podtitulek (např. o Římské říši)", "Success": "Úspěch", "Successfully imported {{userCount}} users.": "Úspěšně importováno {{userCount}} uživatelů.", "Successfully updated.": "Úspěšně aktualizováno.", "Suggest a change": "Navrhnout změnu", "Suggested": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Support": "Podpora", "Support this plugin:": "Podpořte tento plugin:", "Supported MIME Types": "Podporované typy MIME", "Sync directory": "Synchronizovat adresář", "System": "Systém", "System Instructions": "Systémo<PERSON><PERSON> instrukce", "System Prompt": "Systémo<PERSON><PERSON> instrukce", "Tags": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Tags Generation": "Generování <PERSON>ů", "Tags Generation Prompt": "Instrukce pro generování š<PERSON>í<PERSON>ů", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "Vzorkování bez konce (tail free sampling) se používá ke snížení vlivu méně pravděpodobných tokenů na výstup. Vyšš<PERSON> hodnota (např. 2.0) sníží vliv více, zatímco hodnota 1.0 toto nastavení vypne.", "Talk to model": "<PERSON><PERSON><PERSON><PERSON> s <PERSON>em", "Tap to interrupt": "Klepnutím př<PERSON>uš<PERSON>", "Task List": "Seznam úkolů", "Task Model": "Model pro <PERSON><PERSON><PERSON>", "Tasks": "Úkoly", "Tavily API Key": "API klíč pro Tavily", "Tavily Extract Depth": "Hloub<PERSON> extrak<PERSON>", "Tell us more:": "Řekněte nám více:", "Temperature": "<PERSON><PERSON><PERSON><PERSON>", "Temporary Chat": "Dočasná konverzace", "Temporary Chat by Default": "", "Text Splitter": "Rozdělovač textu", "Text-to-Speech": "Převod textu na řeč", "Text-to-Speech Engine": "Jádro pro převod textu na řeč", "Thanks for your feedback!": "Děkujeme za vaši zpětnou vazbu!", "The Application Account DN you bind with for search": "DN aplikačního <PERSON>, se kterým se vážete pro vyhledávání", "The base to search for users": "Základ pro vyhledávání uživatelů", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "Velikost dávky ur<PERSON>, kolik textových požadavků se zpracovává najednou. Větší velikost dávky může zvýšit výkon a rychlost modelu, ale také vyžaduje více paměti.", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Vývojáři za tímto pluginem jsou nadšení dobrovolníci z komunity. Pokud vám tento plugin přijde užitečný, zvažte prosím příspěvek na jeho vývoj.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "Hodnotící žebříček je založen na systému hodnocení Elo a je aktualizován v reálném čase.", "The format to return a response in. Format can be json or a JSON schema.": "<PERSON><PERSON><PERSON>, ve kterém se má vrátit odpověď. <PERSON><PERSON><PERSON> mů<PERSON>e být json nebo JSON schéma.", "The height in pixels to compress images to. Leave empty for no compression.": "Výška v pixelech, na kterou se mají obrázky komprimovat. Pro žádnou kompresi ponechte prázdné.", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "Jazyk vstupního zvuku. Zadání vstupního jazyka ve formátu ISO-639-1 (např. cs) zlepší přesnost a latenci. Ponechte prázdné pro automatickou detekci jazyka.", "The LDAP attribute that maps to the mail that users use to sign in.": "Atribut LDAP, který se mapuje na e-mail, který uživatelé používají k přihlášení.", "The LDAP attribute that maps to the username that users use to sign in.": "Atribut LDAP, který se mapuje na uživatelské jméno, které uživatelé používají k přihlášení.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "Žebříček je v současné době v beta verzi a můžeme upravit výpočty hodnocení, jak budeme zdokonalovat algoritmus.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "Maximální velikost souboru v MB. Pokud velikost souboru překročí tento limit, soubor nebude nahrán.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Maximální počet souborů, které lze použít najednou v konverzaci. Pokud počet souborů překročí tento limit, soubory nebudou nahrány.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "Výstupní formát textu. M<PERSON><PERSON>e být 'json', 'markdown' nebo 'html'. Výchozí je 'markdown'.", "The passwords you entered don't quite match. Please double-check and try again.": "Zadaná hesla se úplně neshodují. Zkontrolujte je prosím a zkuste to znovu.", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Sk<PERSON>re by m<PERSON><PERSON> b<PERSON>t hodnota mezi 0,0 (0 %) a 1,0 (100 %).", "The stream delta chunk size for the model. Increasing the chunk size will make the model respond with larger pieces of text at once.": "Velikost bloku delta streamu pro model. Zvětšení velikosti bloku způsobí, že model bude odpovídat většími kusy textu najednou.", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "Teplota modelu. Zvýšení teploty způsobí, že model bude odpovídat kreativněji.", "The Weight of BM25 Hybrid Search. 0 more lexical, 1 more semantic. Default 0.5": "Váha hybridního vyhledávání BM25. 0 více lexikální, 1 více sémantické. Výchozí 0.5", "The width in pixels to compress images to. Leave empty for no compression.": "Šířka v pixelech, na kterou se mají obrázky komprimovat. Pro žádnou kompresi ponechte prázdné.", "Theme": "Motiv", "Thinking...": "Přemýšlím...", "This action cannot be undone. Do you wish to continue?": "Tuto akci nelze vrátit zpět. Přejete si pokračovat?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "Tento kanál byl v<PERSON> {{createdAt}}. Toto je úplný začátek kanálu {{channelName}}.", "This chat won't appear in history and your messages will not be saved.": "Tato konverzace se neobjeví v historii a vaše zprávy nebudou uloženy.", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Tím je zaj<PERSON>, že vaše cenné konverzace jsou bezpečně uloženy ve vaší backendové databázi. Děkujeme!", "This feature is experimental and may be modified or discontinued without notice.": "<PERSON><PERSON> je experimentální a může být upravena nebo zrušena bez předchozího upozornění.", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Toto je <PERSON><PERSON>, nemusí fungovat podle očekávání a může být kdykoli změněna.", "This model is not publicly available. Please select another model.": "Tento model ne<PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON> ji<PERSON> model.", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "<PERSON><PERSON> m<PERSON> řídí, jak <PERSON><PERSON><PERSON> model po požadavku načten v paměti (výchozí: 5m)", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "Tato možnost řídí, kolik tokenů se zach<PERSON> při obnovování kontextu. <PERSON><PERSON><PERSON><PERSON><PERSON>, pokud je nastavena na 2, poslední 2 tokeny kontextu konverzace budou zach<PERSON>ny. Zachování kontextu může pomoci udržet kontinuitu konverzace, ale může snížit schopnost reagovat na nová témata.", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "Tato možnost povoluje nebo zakazuje použití funkce uvažování v Ollama, která umožňuje modelu přemýšlet před generováním odpovědi. <PERSON><PERSON><PERSON> je povolena, model si může vzít chvíli na zpracování kontextu konverzace a vygenerovat promyšlenější odpověď.", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "Tato možnost nastavuje maximální počet <PERSON>, k<PERSON><PERSON> mů<PERSON> model vygenerovat ve své odpovědi. Zvýšení tohoto limitu umožňuje modelu poskytovat delš<PERSON> odpovědi, ale může také zvýšit pravděpodobnost generování neužitečného nebo irelevantního obsahu.", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Tato volba smaže všechny existující soubory v kolekci a nahradí je nově nahranými soubory.", "This response was generated by \"{{model}}\"": "<PERSON><PERSON><PERSON><PERSON> byla v<PERSON>generována modelem \"{{model}}\"", "This will delete": "<PERSON>í<PERSON> se smaže", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "<PERSON>í<PERSON> se s<PERSON>e <strong>{{NAME}}</strong> a <strong>ve<PERSON><PERSON><PERSON> jeho obsah</strong>.", "This will delete all models including custom models": "Tím se s<PERSON>žou všechny modely včetně vlastních modelů", "This will delete all models including custom models and cannot be undone.": "Tím se s<PERSON>žou všechny modely včetně vlastních a tuto akci nelze vrátit zpět.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Tím se resetuje znalostní báze a synchronizují se všechny soubory. Přejete si pokrač<PERSON>t?", "Thorough explanation": "Důkladné vysvětlení", "Thought for {{DURATION}}": "P<PERSON><PERSON><PERSON>šlel po dobu {{DURATION}}", "Thought for {{DURATION}} seconds": "Př<PERSON>ýšlel po dobu {{DURATION}} sekund", "Thought for less than a second": "Přem<PERSON>šlel méně než sekundu", "Thread": "Vlákno", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "Je vyžadována URL serveru Tika.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Tip: Aktualizujte více proměnn<PERSON>ch slotů po sobě stisknutím klávesy Tab ve vstupním poli konverzace po každé náhradě.", "Title": "<PERSON><PERSON><PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "Název (např. Řekni mi zajímavost)", "Title Auto-Generation": "Automatické gene<PERSON> n<PERSON>", "Title cannot be an empty string.": "Název nemůže být prázdný řetězec.", "Title Generation": "Generování názvu", "Title Generation Prompt": "Instrukce pro generování názvu", "TLS": "TLS", "To access the available model names for downloading,": "Pro přístup k dostupným názvům modelů ke stažení,", "To access the GGUF models available for downloading,": "Pro přístup k modelům GGUF dostupným ke stažení,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Pro přístup k WebUI se prosím obraťte na administrátora. Administrátoři mohou spravovat stavy uživatelů v panelu administrátora.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Pro připojení znalostní báze sem ji nejprve přidejte do pracovního prostoru \"Znalosti\".", "To learn more about available endpoints, visit our documentation.": "Pro více informací o dostupných koncových bodech navštivte naši dokumentaci.", "To learn more about powerful prompt variables, click here": "Pro více informací o mocných proměnných v instrukcích klikněte zde", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Pro ochranu vašeho soukromí jsou z vaší zpětné vazby sdílena pouze hodnocení, ID modelů, štítky a metadata – vaše záznamy konverzací zůstávají soukromé a nejsou zahrnuty.", "To select actions here, add them to the \"Functions\" workspace first.": "Pro výběr akcí zde je nejprve přidejte do pracovního prostoru \"Funkce\".", "To select filters here, add them to the \"Functions\" workspace first.": "Pro výběr filtrů zde je nejprve přidejte do pracovního prostoru \"Funkce\".", "To select toolkits here, add them to the \"Tools\" workspace first.": "Pro výběr sad nástrojů zde je nejprve přidejte do pracovního prostoru \"Nástroje\".", "Toast notifications for new updates": "Vyskakovací oznámení o nových aktualizacích", "Today": "Dnes", "Toggle search": "Přepnout vyhledávání", "Toggle settings": "Přepnout nastavení", "Toggle sidebar": "Přepnout postranní panel", "Toggle whether current connection is active.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zda je aktuální připojení aktivní.", "Token": "Token", "Too verbose": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "Tool created successfully": "Nástroj byl úspěšně vytvořen.", "Tool deleted successfully": "Nás<PERSON>j byl úsp<PERSON>š<PERSON>ě s<PERSON>.", "Tool Description": "Popis nás<PERSON>je", "Tool ID": "ID nástroje", "Tool imported successfully": "<PERSON>ás<PERSON>j byl ú<PERSON>š<PERSON>n", "Tool Name": "Název nástroje", "Tool Servers": "<PERSON><PERSON>", "Tool updated successfully": "Nástroj byl úspěšně aktualizován.", "Tools": "<PERSON>ás<PERSON><PERSON>", "Tools Access": "Přístup k nástrojům", "Tools are a function calling system with arbitrary code execution": "Nástroje jsou systémem pro volání funkcí se spouštěním libovolného kódu", "Tools Function Calling Prompt": "Instrukce pro volání funkcí nás<PERSON>jů", "Tools have a function calling system that allows arbitrary code execution.": "Nástroje mají systém vol<PERSON>, k<PERSON><PERSON> umožňuje spouštění libovolného kódu.", "Tools Public Sharing": "Veřejné <PERSON> n<PERSON>", "Top K": "Top K", "Top K Reranker": "Top K pro přehodnocení", "Transformers": "Transformers", "Trouble accessing Ollama?": "<PERSON><PERSON><PERSON> s přístupem k Ollama?", "Trust Proxy Environment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> prostředí proxy", "Try Again": "Zkusit znovu", "TTS Model": "Model TTS", "TTS Settings": "Nastavení TTS", "TTS Voice": "Hlas TTS", "Type": "<PERSON><PERSON>", "Type Hugging Face Resolve (Download) URL": "Zadejte URL pro stažení z Hugging Face.", "Uh-oh! There was an issue with the response.": "Jejda! Vyskytl se problém s odpovědí.", "UI": "UI", "Unarchive All": "Zrušit archivaci všech", "Unarchive All Archived Chats": "Zrušit archivaci všech archivovaných konverzací", "Unarchive Chat": "Zrušit archivaci konverzace", "Underline": "Podtržení", "Unloads {{FROM_NOW}}": "Uvolní se {{FROM_NOW}}", "Unlock mysteries": "<PERSON><PERSON><PERSON><PERSON>", "Unpin": "Odepnout", "Unravel secrets": "Rozplétejte tajemství", "Unsupported file type.": "Nepodporovaný typ souboru.", "Untagged": "Bez štítku", "Untitled": "Bez názvu", "Update": "Aktualizovat", "Update and Copy Link": "Aktualizovat a zkopírovat odkaz", "Update for the latest features and improvements.": "Aktualizujte pro nejnovější funkce a vylepšení.", "Update password": "Aktualizovat he<PERSON>lo", "Updated": "Aktualizováno", "Updated at": "Aktualizováno", "Updated At": "Aktualizováno", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "Upgradujte na licencovaný plán pro rozšířené možnosti, včetně vlastních motivů a brandingu, a dedikované podpory.", "Upload": "<PERSON><PERSON><PERSON><PERSON>", "Upload a GGUF model": "Nahrát model GGUF", "Upload Audio": "<PERSON><PERSON><PERSON><PERSON>", "Upload directory": "Adresář pro nahrávání", "Upload files": "<PERSON><PERSON><PERSON><PERSON>", "Upload Files": "<PERSON><PERSON><PERSON><PERSON>", "Upload Pipeline": "<PERSON><PERSON><PERSON><PERSON>", "Upload Progress": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Upload Progress: {{uploadedFiles}}/{{totalFiles}} ({{percentage}}%)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: {{uploadedFiles}}/{{totalFiles}} ({{percentage}}%)", "URL": "URL", "URL is required": "URL je vyžadována", "URL Mode": "<PERSON><PERSON><PERSON>", "Usage": "Využití", "Use '#' in the prompt input to load and include your knowledge.": "Použijte '#' ve vstupu promptu pro načtení a zahrnutí vašich znalostí.", "Use groups to group your users and assign permissions.": "Použijte skupiny pro seskupení uživatelů a přiřazení oprávnění.", "Use LLM": "Použít LLM", "Use no proxy to fetch page contents.": "Nepoužívat proxy pro načítání obsahu str<PERSON>.", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "Použít proxy ur<PERSON><PERSON><PERSON> proměnnými prostředí http_proxy a https_proxy pro načítání obsahu str<PERSON>.", "user": "uživatel", "User": "<PERSON>živatel", "User Groups": "Skupiny uživatelů", "User location successfully retrieved.": "<PERSON><PERSON> byla úspěšně zís<PERSON>ána.", "User menu": "Uživatelská nabídka", "User Webhooks": "Uživatelské webhooky", "Username": "Uživatelské jméno", "Users": "Uživatelé", "Using Entire Document": "Použití celého dokumentu", "Using Focused Retrieval": "Použití cíleného vyhledávání", "Using the default arena model with all models. Click the plus button to add custom models.": "Používá se výchozí model arény se všemi modely. Kliknutím na tlačítko plus přidáte vlastní modely.", "Valid time units:": "<PERSON><PERSON><PERSON><PERSON>:", "Validate certificate": "Ov<PERSON><PERSON><PERSON> certif<PERSON>", "Valves": "Ventily", "Valves updated": "Ventily aktual<PERSON>", "Valves updated successfully": "Ventily byly <PERSON><PERSON>ě aktualizovány.", "variable": "proměnn<PERSON>", "Verify Connection": "Ověřit připojení", "Verify SSL Certificate": "Ověřit SSL certifikát", "Version": "Verze", "Version {{selectedVersion}} of {{totalVersions}}": "Verze {{selectedVersion}} z {{totalVersions}}", "View Replies": "Zobrazit odpovědi", "View Result from **{{NAME}}**": "Zobrazit v<PERSON><PERSON>dek z **{{NAME}}**", "Visibility": "Viditelnost", "Vision": "Zpracovávání obrazu", "Voice": "<PERSON><PERSON>", "Voice Input": "Hlasový vstup", "Voice mode": "<PERSON>las<PERSON><PERSON> režim", "Warning": "Varování", "Warning:": "Varování:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Varování: Povolení této volby umožní uživatelům nahrávat na server libovolný kód.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Varování: Po<PERSON>d aktualizujete nebo změníte svůj model pro vektorizaci, budete muset znovu importovat všechny dokumenty.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "Varování: Spouštění Jupyteru umožňuje provádění libovolného kódu, což představuje vážná bezpečnostní rizika – postupujte s maximální opatrností.", "Web": "Web", "Web API": "Webové API", "Web Loader Engine": "<PERSON><PERSON><PERSON><PERSON>", "Web Search": "Vyhledávání na webu", "Web Search Engine": "Webový vyhledávač", "Web Search in Chat": "Vyhledávání na webu v konverzaci", "Web Search Query Generation": "Generování dotazu pro webové vyhledávání", "Webhook URL": "URL webhooku", "WebUI Settings": "Nastavení WebUI", "WebUI URL": "URL WebUI", "WebUI will make requests to \"{{url}}\"": "WebUI bude odesílat p<PERSON>žadavky na \"{{url}}\"", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI bude odesílat p<PERSON>žadavky na \"{{url}}/api/chat\"", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI bude odesílat p<PERSON>žadavky na \"{{url}}/chat/completions\"", "What are you trying to achieve?": "Čeho se sna<PERSON><PERSON>te <PERSON>out?", "What are you working on?": "Na čem pracujete?", "What's New in": "Co je nového v", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "<PERSON><PERSON><PERSON> je povoleno, model b<PERSON> odpovídat na každou zprávu v konverzaci v reálném čase a generovat odpověď, j<PERSON><PERSON> odešle zprávu. Tento režim je užitečný pro aplikace s živou konverzací, ale může ovlivnit výkon na pomalejším hardwaru.", "wherever you are": "ať jste kdekoli", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "Zda stránkovat výstup. <PERSON><PERSON><PERSON><PERSON> stránka bude oddělena vodorovnou čarou a číslem stránky. Výchozí hodnota je False.", "Whisper (Local)": "Whisper (lokální)", "Why?": "Proč?", "Widescreen Mode": "Široko<PERSON><PERSON><PERSON>", "Width": "Šířka", "Won": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "Funguje společně s top-k. <PERSON><PERSON><PERSON><PERSON><PERSON> ho<PERSON> (např. 0,95) povede k rozmanitějšímu textu, zatímco ni<PERSON> hodnota (např. 0,5) vygeneruje soustředěnější a konzervativnější text.", "Workspace": "Pracovní prostor", "Workspace Permissions": "Oprávnění pracovního prostoru", "Write": "Napsat", "Write a prompt suggestion (e.g. Who are you?)": "Napište návrh instrukcí (např. Kdo jsi?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Na<PERSON>š<PERSON> shrnutí na 50 slov, k<PERSON><PERSON> shr<PERSON> [téma nebo kl<PERSON><PERSON><PERSON><PERSON> slovo].", "Write something...": "Napište něco...", "Write your model system prompt content here\ne.g.) You are Mario from Super Mario Bros, acting as an assistant.": "Sem napište obsah systémových instrukcí vašeho modelu\nnapř.: Jste Mario ze Super Mario Bros a vystupujete jako asistent.", "Yacy Instance URL": "URL instance Yacy", "Yacy Password": "<PERSON><PERSON><PERSON> <PERSON>", "Yacy Username": "Uživatelské jméno pro Yacy", "Yesterday": "Včera", "You": "Vy", "You are currently using a trial license. Please contact support to upgrade your license.": "V současné době používáte zkušební licenci. Pro upgrade licence prosím kontaktujte podporu.", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Můžete konverzovat s maximálně {{maxCount}} souborem (soubory) najednou.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Své interakce s LLM si můžete přizpůsobit přidáním vzpomínek pomocí tlačítka 'Spravovat' níže, č<PERSON><PERSON><PERSON> se stanou užitečnějšími a přizpůsobenými vám.", "You cannot upload an empty file.": "Nemůžete nahrát prázdný soubor.", "You do not have permission to upload files.": "Nemáte oprávnění nahrávat soubory.", "You have no archived conversations.": "Nemáte žádné archivované konverzace.", "You have shared this chat": "<PERSON><PERSON> j<PERSON> sdíleli.", "You're a helpful assistant.": "Jsi užitečný asistent.", "You're now logged in.": "Nyní jste přihlášeni.", "Your Account": "", "Your account status is currently pending activation.": "Stav vašeho účtu aktuálně čeká na aktivaci.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Celý váš příspěvek půjde přímo vývojáři pluginu; Open WebUI si nebere žádné procento. Zvolená platforma pro financování však může mít vlastní poplatky.", "Youtube": "YouTube", "Youtube Language": "Jazyk YouTube", "Youtube Proxy URL": "Proxy URL pro YouTube"}