{"-1 for no limit, or a positive integer for a specific limit": "-1 angir ingen grense, eller angi et positivt heltall for en bestemt grense", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 't', 'd', 'u' eller '-1' for ingen u<PERSON><PERSON><PERSON>.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(f.eks. `sh webui.sh --api --api-auth brukernavn_passord`)", "(e.g. `sh webui.sh --api`)": "(f.eks. `sh webui.sh --api`)", "(latest)": "(siste)", "(leave blank for to use commercial endpoint)": "", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "{{ modeller }}", "{{COUNT}} Available Tools": "", "{{COUNT}} characters": "", "{{COUNT}} extracted lines": "", "{{COUNT}} hidden lines": "", "{{COUNT}} Replies": "{{COUNT}} svar", "{{COUNT}} words": "", "{{model}} download has been canceled": "", "{{user}}'s Chats": "{{user}} sine samtaler", "{{webUIName}} Backend Required": "Backend til {{webUIName}} kreves", "*Prompt node ID(s) are required for image generation": "Node-ID-er for ledetekst kreves for generering av bilder", "A new version (v{{LATEST_VERSION}}) is now available.": "En ny versjon (v{{LATEST_VERSION}}) er nå tilgjengelig.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "En oppgavemodell brukes når du utfører oppgaver som å generere titler for samtaler eller utfører søkeforespørsler på nettet", "a user": "en bruker", "About": "Om", "Accept autocomplete generation / Jump to prompt variable": "Godta autofullføring / Gå til ledetekstvariabel", "Access": "<PERSON><PERSON><PERSON><PERSON>", "Access Control": "Tilgangskontroll", "Accessible to all users": "Tilgjengelig for alle brukere", "Account": "Ko<PERSON>", "Account Activation Pending": "Venter på kontoaktivering", "Accurate information": "Nøyaktig informasjon", "Action": "", "Action not found": "", "Action Required for Chat Log Storage": "Handling kreves for å lagre chatlogg", "Actions": "<PERSON><PERSON>", "Activate": "Aktiver", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Aktiver denne kommandoen ved å skrive inn \"/{{COMMAND}}\" i chattens inntastingsfelt.", "Active": "Aktiv", "Active Users": "Aktive brukere", "Add": "Legg til", "Add a model ID": "Legg til en modell-ID", "Add a short description about what this model does": "Legg til en kort beskrivelse av hva denne modellen gjør", "Add a tag": "Legg til en tag", "Add Arena Model": "<PERSON><PERSON>-modell", "Add Connection": "<PERSON>gg til tilkobling", "Add Content": "Legg til innhold", "Add content here": "Legg til innhold her", "Add Custom Parameter": "", "Add custom prompt": "Legg til tilpasset ledetekst", "Add Details": "", "Add Files": "Legg til filer", "Add Group": "Legg til gruppe", "Add Memory": "Legg til minne", "Add Model": "Legg til modell", "Add Reaction": "Legg til reaksjon", "Add Tag": "<PERSON><PERSON> til etikett", "Add Tags": "<PERSON><PERSON> til etiketter", "Add text content": "Legg til tekstinnhold", "Add User": "Legg til bruker", "Add User Group": "<PERSON>gg til brukergruppe", "Additional Config": "", "Additional configuration options for marker. This should be a JSON string with key-value pairs. For example, '{\"key\": \"value\"}'. Supported keys include: disable_links, keep_pageheader_in_output, keep_pagefooter_in_output, filter_blank_pages, drop_repeated_text, layout_coverage_threshold, merge_threshold, height_tolerance, gap_threshold, image_threshold, min_line_length, level_count, default_level": "", "Adjusting these settings will apply changes universally to all users.": "Endring av disse innstillingene vil gjelde for alle brukere på tvers av systemet.", "admin": "administrator", "Admin": "Administrator", "Admin Panel": "<PERSON><PERSON><PERSON>", "Admin Settings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "<PERSON><PERSON> har alltid tilgang til alle verktøy. Brukere må få tildelt verktøy per modell i arbeidsområdet.", "Advanced Parameters": "Avanserte parametere", "Advanced Params": "Avanserte parametere", "AI": "", "All": "", "All Documents": "Alle dokumenter", "All models deleted successfully": "Alle modeller er slettet", "Allow Call": "", "Allow Chat Controls": "<PERSON><PERSON>", "Allow Chat Delete": "Tillat sletting av chatter", "Allow Chat Deletion": "Tillat sletting av chatter", "Allow Chat Edit": "Tillat redigering av chatter", "Allow Chat Export": "", "Allow Chat Params": "", "Allow Chat Share": "", "Allow Chat System Prompt": "", "Allow Chat Valves": "", "Allow Continue Response": "", "Allow Delete Messages": "", "Allow File Upload": "Tillatt opplasting av filer", "Allow Multiple Models in Chat": "", "Allow non-local voices": "Tillat ikke-lokale stemmer", "Allow Rate Response": "", "Allow Regenerate Response": "", "Allow Speech to Text": "", "Allow Temporary Chat": "Tillat midlertidige chatter", "Allow Text to Speech": "", "Allow User Location": "Aktiver stedstjenester", "Allow Voice Interruption in Call": "Mu<PERSON><PERSON><PERSON><PERSON><PERSON>avbrytel<PERSON> i samtaler", "Allowed Endpoints": "Tillatte endepunkter", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "Har du allerede en konto?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "", "Always": "Alltid", "Always Collapse Code Blocks": "", "Always Expand Details": "", "Always Play Notification Sound": "", "Amazing": "<PERSON><PERSON><PERSON>", "an assistant": "en assistent", "An error occurred while fetching the explanation": "", "Analytics": "", "Analyzed": "Analysert", "Analyzing...": "Analyserer...", "and": "og", "and {{COUNT}} more": "og {{COUNT}} til", "and create a new shared link.": "og opprett en ny delt lenke.", "Android": "", "API": "", "API Base URL": "Absolutt API-URL", "API Base URL for Datalab Marker service. Defaults to: https://www.datalab.to/api/v1/marker": "", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "API-nøkkel", "API Key created.": "API-nøkkel opprettet.", "API Key Endpoint Restrictions": "Begrensninger for API-nøkkelens endepunkt", "API keys": "API-nøkler", "API Version": "", "API Version is required": "", "Application DN": "Applikasjonens DN", "Application DN Password": "Applikasjonens DN-passord", "applies to all users with the \"user\" role": "gjelder for alle brukere med rollen \"user\"", "April": "april", "Archive": "Arkiv", "Archive All Chats": "<PERSON><PERSON> alle chatter", "Archived Chats": "Arkiverte chatter", "archived-chat-export": "archived-chat-export", "Are you sure you want to clear all memories? This action cannot be undone.": "", "Are you sure you want to delete this channel?": "Er du sikker på at du vil slette denne kanalen?", "Are you sure you want to delete this message?": "Er du sikker på at du vil slette denne meldingen?", "Are you sure you want to unarchive all archived chats?": "Er du sikker på at du vil oppheve arkiveringen av alle arkiverte chatter?", "Are you sure?": "<PERSON>r du sikker?", "Arena Models": "Arena-modeller", "Artifacts": "Artifakter", "Ask": "", "Ask a question": "Still et spørsmål", "Assistant": "Assistent", "Attach file from knowledge": "Legg ved fil fra kunnskaper", "Attention to detail": "Fokus på detaljer", "Attribute for Mail": "Attributt for e-post", "Attribute for Username": "Attributt for brukernavn", "Audio": "Lyd", "August": "august", "Auth": "", "Authenticate": "<PERSON><PERSON><PERSON><PERSON>", "Authentication": "<PERSON><PERSON><PERSON><PERSON>", "Auto": "", "Auto-Copy Response to Clipboard": "Ko<PERSON>r svar automatisk til utklippstavlen", "Auto-playback response": "Spill av svar automatisk", "Autocomplete Generation": "Generering av autofullføring", "Autocomplete Generation Input Max Length": "<PERSON><PERSON> lengde for autofullføring av inndata", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "API-Autentiseringsstreng for AUTOMATIC1111", "AUTOMATIC1111 Base URL": "Absolutt URL for AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "Absolutt URL for AUTOMATIC1111 kreves.", "Available list": "Tilgjengelig liste", "Available Tools": "", "available users": "tilgjengelige brukere", "available!": "tilgjengelig!", "Away": "<PERSON><PERSON>", "Awful": "<PERSON><PERSON><PERSON>", "Azure AI Speech": "Azure AI-tale", "Azure OpenAI": "Azure OpenAI", "Azure Region": "Azure område", "Back": "Tilbake", "Bad Response": "<PERSON><PERSON><PERSON><PERSON> svar", "Banners": "<PERSON><PERSON>", "Base Model (From)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (fra)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "Bearer": "", "before": "<PERSON>ør", "Being lazy": "<PERSON><PERSON> lat", "Beta": "Beta", "Bing Search V7 Endpoint": "Endepunkt for Bing Search V7", "Bing Search V7 Subscription Key": "<PERSON>bon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> for Bing Search V7", "Bio": "", "Birth Date": "", "BM25 Weight": "", "Bocha Search API Key": "API-n<PERSON><PERSON><PERSON> for Bocha Search", "Bold": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "API-n<PERSON>k<PERSON> for Brave Search", "Bullet List": "", "Button ID": "", "Button Label": "", "Button Prompt": "", "By {{name}}": "Etter {{name}}", "Bypass Embedding and Retrieval": "", "Bypass Web Loader": "", "Cache Base Model List": "", "Calendar": "<PERSON><PERSON><PERSON>", "Call": "Ring", "Call feature is not supported when using Web STT engine": "Ringefunksjonen støttes ikke når du bruker Web STT-motoren", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Capabilities": "<PERSON><PERSON><PERSON><PERSON>", "Capture": "Opptak", "Capture Audio": "", "Certificate Path": "Sertifikatbane", "Change Password": "<PERSON><PERSON>", "Channel deleted successfully": "", "Channel Name": "Kanalens navn", "Channel updated successfully": "", "Channels": "<PERSON><PERSON><PERSON>", "Character": "<PERSON><PERSON><PERSON>", "Character limit for autocomplete generation input": "Begrensning av tegn for generering av autofullføring", "Chart new frontiers": "Kartlegg ny områder", "Chat": "Cha<PERSON>", "Chat Background Image": "Bakgrunnsbilde for chat", "Chat Bubble UI": "<PERSON><PERSON><PERSON><PERSON><PERSON> for chat-bob<PERSON>", "Chat Controls": "Kontrollere i chat", "Chat Conversation": "", "Chat direction": "Retning på chat", "Chat ID": "", "Chat moved successfully": "", "Chat Overview": "Chatoversikt", "Chat Permissions": "<PERSON><PERSON><PERSON><PERSON> for chat", "Chat Tags Auto-Generation": "Auto-generering av chatetiketter", "Chats": "Chatter", "Check Again": "Sjekk på nytt", "Check for updates": "Sjekk for oppdateringer", "Checking for updates...": "<PERSON><PERSON><PERSON> for oppdateringer ...", "Choose a model before saving...": "Velg en modell før du lagrer ...", "Chunk Overlap": "Chunk-overlapp", "Chunk Size": "Chunk-st<PERSON><PERSON><PERSON>", "Ciphers": "<PERSON><PERSON>", "Citation": "Kildehenvisning", "Citations": "", "Clear memory": "<PERSON><PERSON><PERSON> minnet", "Clear Memory": "", "click here": "<PERSON><PERSON><PERSON> her", "Click here for filter guides.": "<PERSON><PERSON><PERSON> her for å få veiledning om filtre", "Click here for help.": "Klikk her for å få hjelp.", "Click here to": "K<PERSON>k her for å", "Click here to download user import template file.": "Klikk her for å hente ned malfilen for import av brukere.", "Click here to learn more about faster-whisper and see the available models.": "<PERSON><PERSON><PERSON> her for å lære mer om faster-whisper, og se de tilgjengelige modellene.", "Click here to see available models.": "Klikk her for å se tilgjengelige modeller.", "Click here to select": "K<PERSON>k her for å velge", "Click here to select a csv file.": "<PERSON><PERSON>k her for å velge en CSV-fil.", "Click here to select a py file.": "<PERSON><PERSON><PERSON> her for å velge en PY-fil.", "Click here to upload a workflow.json file.": "<PERSON><PERSON><PERSON> her for å laste opp en workflow.json-fil.", "click here.": "klikk her.", "Click on the user role button to change a user's role.": "Klikk på knappen Brukerrolle for å endre en brukers rolle.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Skrivetilgang til utklippstavlen avslått. Kontroller nettleserinnstillingene for å gi den nødvendige tilgangen.", "Clone": "Klon", "Clone Chat": "<PERSON>lone chat", "Clone of {{TITLE}}": "<PERSON>lone av {{TITLE}}", "Close": "Lukk", "Close Banner": "", "Close Configure Connection Modal": "", "Close modal": "", "Close settings modal": "", "Close Sidebar": "", "CMU ARCTIC speaker embedding name": "", "Code Block": "", "Code execution": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Code Execution": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Code Execution Engine": "Motor for kodekjøring", "Code Execution Timeout": "<PERSON><PERSON><PERSON>b<PERSON>d for kodekjøring", "Code formatted successfully": "<PERSON>den er <PERSON>ert", "Code Interpreter": "Kodetolker", "Code Interpreter Engine": "Motor for kodetolking", "Code Interpreter Prompt Template": "<PERSON> for led<PERSON>ks<PERSON> for kodetolker", "Collapse": "", "Collection": "<PERSON><PERSON>", "Color": "<PERSON><PERSON>", "ComfyUI": "ComfyUI", "ComfyUI API Key": "API-nøkkel for ComfyUI", "ComfyUI Base URL": "Absolutt URL for ComfyUI", "ComfyUI Base URL is required.": "Absolutt URL for ComfyUI kreves.", "ComfyUI Workflow": "ComfyUI-arbeidsflyt", "ComfyUI Workflow Nodes": "ComfyUI-arbeidsflytnoder", "Comma separated Node Ids (e.g. 1 or 1,2)": "", "Command": "Kommando", "Comment": "", "Completions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Compress Images in Channels": "", "Concurrent Requests": "<PERSON><PERSON><PERSON><PERSON> fore<PERSON><PERSON><PERSON><PERSON>", "Config imported successfully": "", "Configure": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Confirm": "Bekreft", "Confirm Password": "Bekreft passordet", "Confirm your action": "Bekreft handlingen", "Confirm your new password": "Bekreft det nye passordet ditt", "Confirm Your Password": "", "Connect to your own OpenAI compatible API endpoints.": "Koble til egne OpenAI-kompatible API-endepunkter", "Connect to your own OpenAPI compatible external tool servers.": "", "Connection failed": "", "Connection successful": "", "Connection Type": "", "Connections": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Connections saved successfully": "", "Connections settings updated": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "", "Contact Admin for WebUI Access": "Kontakt administrator for å få tilgang til WebUI", "Content": "Innhold", "Content Extraction Engine": "", "Continue Response": "Fortsett svar", "Continue with {{provider}}": "Fortsett med {{provider}}", "Continue with Email": "Fortsett med e-post", "Continue with LDAP": "Fortsett med LDAP", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Kontrollerer hvordan meldingsteksten deles opp for TTS-forespørsler. 'Punctuation' deler opp i setninger, 'paragraphs' deler opp i avsnitt, og 'none' beholder meldingen som én enkelt streng.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "", "Controls": "<PERSON><PERSON><PERSON><PERSON>", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "", "Conversation saved successfully": "", "Copied": "<PERSON><PERSON><PERSON>", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "Kopierte delt chat-URL til utklippstavlen!", "Copied to clipboard": "Kopier til utklippstaveln", "Copy": "<PERSON><PERSON><PERSON>", "Copy Formatted Text": "", "Copy last code block": "<PERSON><PERSON><PERSON> siste kode<PERSON>lo<PERSON>k", "Copy last response": "<PERSON><PERSON><PERSON> siste svar", "Copy link": "", "Copy Link": "<PERSON><PERSON><PERSON>", "Copy to clipboard": "Ko<PERSON>r til utklippstavle", "Copying to clipboard was successful!": "<PERSON><PERSON><PERSON> til utklippstavlen!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "CORS må være riktig konfigurert av leverandøren for å kunne godkjenne forespørsler fra Open WebUI.", "Create": "<PERSON><PERSON><PERSON><PERSON>", "Create a knowledge base": "Opprett en kunnskapsbase", "Create a model": "<PERSON><PERSON><PERSON><PERSON> en modell", "Create Account": "<PERSON><PERSON><PERSON><PERSON> konto", "Create Admin Account": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Create Channel": "<PERSON><PERSON><PERSON><PERSON> kanal", "Create Folder": "", "Create Group": "Op<PERSON>rett gruppe", "Create Knowledge": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "Create new key": "Lag ny nøkkel", "Create new secret key": "Lag ny hemmelig nøkkel", "Create Note": "", "Create your first note by clicking on the plus button below.": "", "Created at": "Opprettet", "Created At": "Opprettet", "Created by": "Opprettet av", "CSV Import": "CSV-import", "Ctrl+Enter to Send": "", "Current Model": "Nåværende modell", "Current Password": "Nåværende passord", "Custom": "Tilpasset", "Custom description enabled": "", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "", "Dark": "<PERSON><PERSON><PERSON>", "Database": "Database", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "DD/MM/YYYY": "", "December": "desember", "Deepgram": "", "Default": "Standard", "Default (Open AI)": "Standard (Open AI)", "Default (SentenceTransformers)": "Standard (SentenceTransformers)", "Default action buttons will be used.": "", "Default description enabled": "", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "Standard modus fungerer med et bredere utvalg av modeller ved at verktøyene kalles én gang før kjøring. Opprinnelig modus utnytter modellens innebygde funksjoner for verktøykalling, men krever at modellen i seg selv støtter denne funksjonen.", "Default Model": "Standard modell", "Default model updated": "Standard modell oppdatert", "Default Models": "Standard modeller", "Default permissions": "Standard tillatelser", "Default permissions updated successfully": "Standard tillatelser oppdatert", "Default Prompt Suggestions": "Standard forslag til ledetekster", "Default to 389 or 636 if TLS is enabled": "Velg 389 eller 636 som standard hvis TLS er aktivert", "Default to ALL": "Velg ALL som standard", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "", "Default User Role": "Standard brukerrolle", "Delete": "<PERSON><PERSON>", "Delete a model": "<PERSON><PERSON> en modell", "Delete All Chats": "<PERSON><PERSON> alle chatter", "Delete All Models": "<PERSON><PERSON> alle modeller", "Delete chat": "<PERSON><PERSON> chat", "Delete Chat": "<PERSON><PERSON> chat", "Delete chat?": "Slette chat?", "Delete folder?": "Slette mappe?", "Delete function?": "Slette funk<PERSON>jon?", "Delete Message": "<PERSON><PERSON> melding", "Delete message?": "Slette melding?", "Delete note?": "", "Delete prompt?": "<PERSON><PERSON>?", "delete this link": "slett denne lenken", "Delete tool?": "Slette verktøy?", "Delete User": "<PERSON><PERSON> bruker", "Deleted {{deleteModelTag}}": "Slettet {{deleteModelTag}}", "Deleted {{name}}": "Slettet {{name}}", "Deleted User": "<PERSON><PERSON><PERSON> bruker", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "Beskriv kunnskapsbasen din og målene dine", "Description": "Beskrivelse", "Detect Artifacts Automatically": "", "Dictate": "", "Didn't fully follow instructions": "Fulgte ikke instruksjonene fullstendig", "Direct": "", "Direct Connections": "<PERSON><PERSON><PERSON> koblinger", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "Med direkte koblinger kan brukerne koble til egne OpenAI-kompatible API-endepunkter.", "Direct Tool Servers": "", "Directory selection was cancelled": "", "Disable Code Interpreter": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "Deaktivert", "Discover a function": "Oppdag en funksjon", "Discover a model": "Oppdag en modell", "Discover a prompt": "Oppdag en ledetekst", "Discover a tool": "Oppdag et verktøy", "Discover how to use Open WebUI and seek support from the community.": "Finn ut hvordan du bruker Open WebUI, og få støtte fra fellesskapet.", "Discover wonders": "Oppdag ", "Discover, download, and explore custom functions": "<PERSON><PERSON><PERSON><PERSON>, last ned og utforsk tilpassede funksjoner", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON><PERSON>, last ned og utforsk tilpassede ledetekster", "Discover, download, and explore custom tools": "<PERSON><PERSON><PERSON><PERSON>, last ned og utforsk tilpassede verktøy", "Discover, download, and explore model presets": "<PERSON><PERSON><PERSON><PERSON>, last ned og utforsk forhåndsinnstillinger for modeller", "Display": "V<PERSON><PERSON>", "Display Emoji in Call": "Vis emoji i samtale", "Display Multi-model Responses in Tabs": "", "Display the username instead of You in the Chat": "Vis brukernavnet ditt i stedet for Du i chatten", "Displays citations in the response": "<PERSON>is kildehenvisninger i svaret", "Dive into knowledge": "<PERSON><PERSON> kjent med kunnskap", "Do not install functions from sources you do not fully trust.": "Ikke installer funk<PERSON><PERSON><PERSON> fra kilder du ikke stoler på.", "Do not install tools from sources you do not fully trust.": "Ikke installer verkt<PERSON>y fra kilder du ikke stoler på.", "Docling": "", "Docling Server URL required.": "", "Document": "Dokument", "Document Intelligence": "Intelligens i dokumenter", "Document Intelligence endpoint required.": "", "Documentation": "Dokumentasjon", "Documents": "Do<PERSON><PERSON><PERSON>", "does not make any external connections, and your data stays securely on your locally hosted server.": "ikke ingen tilkobling til eksterne tjenester. <PERSON><PERSON> dine forblir sikkert på den lokale serveren.", "Domain Filter List": "Liste over domenefiltre", "don't fetch random pipelines from sources you don't trust.": "Ikke hent tilfeldige pipelines fra kilder du ikke stoler på.", "Don't have an account?": "Har du ingen konto?", "don't install random functions from sources you don't trust.": "ikke installer til<PERSON><PERSON> funksjoner fra kilder du ikke stoler på.", "don't install random tools from sources you don't trust.": "ikke installer til<PERSON>ige verktøy fra kilder du ikke stoler på.", "Don't like the style": "<PERSON>r ikke stilen", "Done": "<PERSON><PERSON><PERSON>", "Download": "Last ned", "Download & Delete": "Last ned og slett", "Download as SVG": "Last ned som SVG", "Download canceled": "Nedlasting a<PERSON><PERSON><PERSON><PERSON>", "Download Database": "Last ned database", "Drag and drop a file to upload or select a file to view": "<PERSON>a og slipp en fil for å laste den opp, eller velg en fil å vise den", "Draw": "Tegne", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "f.eks. '30s','10m'. <PERSON><PERSON><PERSON>ge tidsenheter er 's', 'm', 't'.", "e.g. \"json\" or a JSON schema": "", "e.g. 60": "f.eks. 60", "e.g. A filter to remove profanity from text": "f.eks. et filter for å fjerne banning fra tekst", "e.g. en": "", "e.g. My Filter": "f.e<PERSON><PERSON> filter", "e.g. My Tools": "f.eks. Mine verktøy", "e.g. my_filter": "f.eks. mitt_filter", "e.g. my_tools": "f.eks. mine_verktøy", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "f.e<PERSON><PERSON> for å gjøre ulike handlinger", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "e.g., westus (leave blank for eastus)": "", "Edit": "<PERSON><PERSON>", "Edit Arena Model": "Rediger Arena-modell", "Edit Channel": "Rediger kanal", "Edit Connection": "<PERSON><PERSON> til<PERSON>", "Edit Default Permissions": "Rediger standard tillatelser", "Edit Folder": "", "Edit Memory": "<PERSON>iger minne", "Edit User": "Rediger bruker", "Edit User Group": "Rediger brukergruppe", "Edited": "", "Editing": "", "Eject": "", "ElevenLabs": "ElevenLabs", "Email": "E-postadresse", "Embark on adventures": "Kom med på eventyr", "Embedding": "", "Embedding Batch Size": "<PERSON><PERSON><PERSON>s<PERSON><PERSON><PERSON><PERSON> for innbygging", "Embedding Model": "Innbyggingsmodell", "Embedding Model Engine": "Motor for innbygging av modeller", "Embedding model set to \"{{embedding_model}}\"": "Innbyggingsmodell angitt til \"{{embedding_model}}\"", "Enable API Key": "Aktiver ", "Enable autocomplete generation for chat messages": "Aktiver automatisk utfylling av chatmeldinger", "Enable Code Execution": "", "Enable Code Interpreter": "Aktiver kodetolker", "Enable Community Sharing": "Aktiver deling i fellesskap", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Aktiver Memory Locking (mlock) for å forhindre at modelldata byttes ut av RAM. Dette alternativet låser modellens arbeidssett med sider i RAM-minnet, slik at de ikke byttes ut til disk. Dette kan bidra til å opprettholde ytelsen ved å unngå sidefeil og sikre rask datatilgang.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Aktiver Memory Mapping (mmap) for å laste inn modelldata. Med dette alternativet kan systemet bruke disklagring som en utvidelse av RAM ved å behandle diskfiler som om de befant seg i RAM. Dette kan forbedre modellens ytelse ved å gi raskere datatilgang. Det er imidlertid ikke sikkert at det fungerer som det skal på alle systemer, og det kan kreve mye diskplass.", "Enable Message Rating": "Aktivert vurdering av meldinger", "Enable Mirostat sampling for controlling perplexity.": "", "Enable New Sign Ups": "Aktiver nye registreringer", "Enable, disable, or customize the reasoning tags used by the model. \"Enabled\" uses default tags, \"Disabled\" turns off reasoning tags, and \"Custom\" lets you specify your own start and end tags.": "", "Enabled": "Aktivert", "End Tag": "", "Endpoint URL": "", "Enforce Temporary Chat": "", "Enhance": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "<PERSON><PERSON><PERSON> for at CSV-filen din inkluderer fire kolonner i denne rekkefølgen: Navn, E-post, Passord, Rolle.", "Enter {{role}} message here": "Skriv inn {{role}} melding her", "Enter a detail about yourself for your LLMs to recall": "Skriv inn en detalj om deg selv som språkmodellene dine kan huske", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "Skriv inn API-autentiseringsstreng (f.eks. brukernavn:passord)", "Enter Application DN": "Angi applikasjonens DN", "Enter Application DN Password": "Angi applikasjonens DN-passord", "Enter Bing Search V7 Endpoint": "<PERSON><PERSON> endep<PERSON>t for Bing Search V7", "Enter Bing Search V7 Subscription Key": "<PERSON><PERSON> for Bing Search V7", "Enter Bocha Search API Key": "Aktiver API-nøkkel for Bocha Search", "Enter Brave Search API Key": "Angi API-nøkkel for Brave Search", "Enter certificate path": "<PERSON><PERSON> sertifika<PERSON><PERSON> bane", "Enter CFG Scale (e.g. 7.0)": "Angi CFG-skala (f.eks. 7,0)", "Enter Chunk Overlap": "<PERSON><PERSON>-overlapp", "Enter Chunk Size": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter coordinates (e.g. 51.505, -0.09)": "", "Enter Datalab Marker API Base URL": "", "Enter Datalab Marker API Key": "", "Enter description": "<PERSON><PERSON>", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "", "Enter Document Intelligence Endpoint": "<PERSON><PERSON> endepunkt for Intelligens i dokumenter", "Enter Document Intelligence Key": "<PERSON><PERSON> for Intelligens i dokumenter", "Enter domains separated by commas (e.g., example.com,site.org)": "<PERSON><PERSON> domener atskilt med komma (f.eks. eksempel.com, side.org)", "Enter Exa API Key": "Angi API-nøkkel for Exa", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter folder name": "", "Enter Github Raw URL": "<PERSON><PERSON>-URL", "Enter Google PSE API Key": "Angi API-nøkkel for Google PSE", "Enter Google PSE Engine Id": "Angi motor-ID for Google PSE", "Enter hex color (e.g. #FF0000)": "", "Enter ID": "", "Enter Image Size (e.g. 512x512)": "<PERSON><PERSON> (f.eks. 512x512)", "Enter Jina API Key": "Angi API-n<PERSON><PERSON><PERSON> for Jina", "Enter JSON config (e.g., {\"disable_links\": true})": "", "Enter Jupyter Password": "<PERSON><PERSON> passord for Ju<PERSON><PERSON>", "Enter Jupyter Token": "<PERSON><PERSON> token for <PERSON><PERSON><PERSON>", "Enter Jupyter URL": "<PERSON>i URL for Jupyter", "Enter Kagi Search API Key": "Angi API-nøkkel for Kagi Search", "Enter Key Behavior": "", "Enter language codes": "<PERSON><PERSON>", "Enter Mistral API Key": "", "Enter Model ID": "Angi modellens ID", "Enter model tag (e.g. {{modelTag}})": "<PERSON><PERSON> modellen<PERSON> etikett (f.eks. {{modelTag}})", "Enter Mojeek Search API Key": "Angi API-n<PERSON><PERSON><PERSON> for Mojeek-søk", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "<PERSON><PERSON> antall steg (f.eks. 50)", "Enter Perplexity API Key": "", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "<PERSON><PERSON> proxy-URL (f.eks. ***************************:port)", "Enter reasoning effort": "<PERSON>i hvor mye resonneringsinnsats som skal til", "Enter Sampler (e.g. Euler a)": "<PERSON><PERSON> (e.g. Euler a)", "Enter Scheduler (e.g. Karras)": "<PERSON><PERSON> (f.eks<PERSON>)", "Enter Score": "<PERSON><PERSON>", "Enter SearchApi API Key": "Angi API-nøkkel for SearchApi", "Enter SearchApi Engine": "Angi motor for SearchApi", "Enter Searxng Query URL": "<PERSON><PERSON>-URL for Searxng", "Enter Seed": "<PERSON><PERSON>", "Enter SerpApi API Key": "Angi API-nøkkel for SerpApi", "Enter SerpApi Engine": "Angi motor for SerpApi", "Enter Serper API Key": "Angi API-nøkkel for Serper", "Enter Serply API Key": "Angi API-nøkkel for Serply", "Enter Serpstack API Key": "Angi API-n<PERSON>k<PERSON> for Serpstack", "Enter server host": "Angi server host", "Enter server label": "Angi server etikett", "Enter server port": "Angi server port", "Enter Sougou Search API sID": "", "Enter Sougou Search API SK": "", "Enter stop sequence": "<PERSON><PERSON>", "Enter system prompt": "Angi systemledetekst", "Enter system prompt here": "", "Enter Tavily API Key": "Angi API-n<PERSON><PERSON><PERSON> for Tavily", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "Ang<PERSON> den offentlige URL-adressen til WebUI. Denne URL-adressen vil bli brukt til å generere koblinger i varslene.", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "Angi server-URL for Tika", "Enter timeout in seconds": "<PERSON><PERSON> tidsavbrudd i sekunder", "Enter to Send": "", "Enter Top K": "Angi Top K", "Enter Top K Reranker": "", "Enter URL (e.g. http://127.0.0.1:7860/)": "<PERSON><PERSON> URL (f.eks. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "<PERSON><PERSON> URL (f.eks. http://localhost:11434)", "Enter value": "", "Enter value (true/false)": "", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your code here...": "Skriv inn koden din her...", "Enter your current password": "<PERSON><PERSON> det gjeldende passordet ditt", "Enter Your Email": "Skriv inn e-postadressen din", "Enter Your Full Name": "Skriv inn det fulle navnet ditt", "Enter your gender": "", "Enter your message": "Skriv inn din melding", "Enter your name": "", "Enter Your Name": "", "Enter your new password": "<PERSON><PERSON> ditt nye passord", "Enter Your Password": "Skriv inn passordet ditt", "Enter Your Role": "Skriv inn rollen din", "Enter Your Username": "Skriv inn brukernavnet ditt", "Enter your webhook URL": "<PERSON><PERSON> URL for webhook", "Error": "<PERSON><PERSON>", "ERROR": "FEIL", "Error accessing directory": "", "Error accessing Google Drive: {{error}}": "Feil under tilgang til Google Disk: {{error}}", "Error accessing media devices.": "", "Error starting recording.": "", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "Feil under opplasting av fil: {{error}}", "Error: A model with the ID '{{modelId}}' already exists. Please select a different ID to proceed.": "", "Error: Model ID cannot be empty. Please enter a valid ID to proceed.": "", "Evaluations": "<PERSON><PERSON><PERSON><PERSON>", "Everyone": "", "Exa API Key": "API-n<PERSON>k<PERSON> for Exa", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Eksempel: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Eksempel: ALL", "Example: mail": "Eksempel: mail", "Example: ou=users,dc=foo,dc=example": "Eksempel: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "Eksempel: sAMAccountName eller uid eller userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "", "Exclude": "Utelukk", "Execute code for analysis": "<PERSON><PERSON><PERSON><PERSON> kode for analyse", "Executing **{{NAME}}**...": "", "Expand": "", "Experimental": "Eksperimentell", "Explain": "", "Explore the cosmos": "Utforsk verdensrommet", "Export": "Eksporter", "Export All Archived Chats": "Eksporter alle arkiverte chatter", "Export All Chats (All Users)": "Eksporter alle chatter (alle brukere)", "Export chat (.json)": "Eksporter chat (.json)", "Export Chats": "Eksporter chatter", "Export Config to JSON File": "Ekporter konfigurasjon til en JSON-fil", "Export Functions": "Eksporter funksjoner", "Export Models": "Eksporter modeller", "Export Presets": "Eksporter forhåndsinnstillinger", "Export Prompt Suggestions": "", "Export Prompts": "Eksporter ledetekster", "Export to CSV": "Eksporter til CSV", "Export Tools": "Eksporter verktøy", "Export Users": "", "External": "", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "", "External Web Loader URL": "", "External Web Search API Key": "", "External Web Search URL": "", "Fade Effect for Streaming Text": "", "Failed to add file.": "Kan ikke legge til filen.", "Failed to connect to {{URL}} OpenAPI tool server": "", "Failed to copy link": "", "Failed to create API Key.": "Kan ikke opprette en API-nøkkel.", "Failed to delete note": "", "Failed to extract content from the file: {{error}}": "", "Failed to extract content from the file.": "", "Failed to fetch models": "Kan ikke hente modeller", "Failed to generate title": "", "Failed to load chat preview": "", "Failed to load file content.": "", "Failed to move chat": "", "Failed to read clipboard contents": "Kan ikke lese utklippstavlens innhold", "Failed to save connections": "", "Failed to save conversation": "Kan ikke lagre samtalen", "Failed to save models configuration": "Kan ikke lagre konfigurasjonen av modeller", "Failed to update settings": "Kan ikke oppdatere innstillinger", "Failed to upload file.": "Kan ikke laste opp filen.", "Features": "Funksjoner", "Features Permissions": "<PERSON><PERSON><PERSON><PERSON> for funksjoner", "February": "februar", "Feedback Details": "", "Feedback History": "Tilbakemeldingslogg", "Feedbacks": "Tilbakemeldinger", "Feel free to add specific details": "Legg gjerne til bestemte detaljer", "Female": "", "File": "Fil", "File added successfully.": "Filen er lagt til.", "File content updated successfully.": "Filens innhold er oppdatert.", "File Mode": "Filmodus", "File not found.": "Finner ikke filen.", "File removed successfully.": "<PERSON>n er fjernet.", "File size should not exceed {{maxSize}} MB.": "<PERSON>ls<PERSON><PERSON><PERSON><PERSON> kan ikke være på mer enn {{maxSize} MB", "File Upload": "", "File uploaded successfully": "Filen er lastet opp", "Files": "Filer", "Filter": "", "Filter is now globally disabled": "Filteret er nå globalt deaktivert", "Filter is now globally enabled": "Filteret er nå globalt aktivert", "Filters": "Filtre", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Fingeravtrykk-spoofing oppdaget: kan ikke bruke initialer som avatar. Bruker standard profilbilde.", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Floating Quick Actions": "", "Focus chat input": "Fokusert chat-inndata", "Folder deleted successfully": "<PERSON><PERSON> slettet", "Folder Name": "", "Folder name cannot be empty.": "Mappenavn kan ikke være tomt.", "Folder name updated successfully": "Mappenavn oppdatert", "Folder updated successfully": "", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "Fulgte instruksjonene perfekt", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "<PERSON><PERSON> nye baner", "Form": "Form", "Format Lines": "", "Format the lines in the output. Defaults to False. If set to True, the lines will be formatted to detect inline math and styles.": "", "Format your variables using brackets like this:": "Formatér variablene dine med klammer som disse:", "Formatting may be inconsistent from source.": "", "Forwards system user session credentials to authenticate": "", "Full Context Mode": "Modus for full kontekst", "Function": "Funksjon", "Function Calling": "<PERSON>lling av <PERSON>jon", "Function created successfully": "Funksjonen er opprettet", "Function deleted successfully": "Funksjonen er slettet", "Function Description": "Beskrivelse av funksjon", "Function ID": "Funksjonens ID", "Function imported successfully": "", "Function is now globally disabled": "Funksjonen er nå deaktivert globalt", "Function is now globally enabled": "Funksjonen er nå aktivert globalt", "Function Name": "Funksjonens navn", "Function updated successfully": "Funksjonen er oppdatert", "Functions": "Funksjoner", "Functions allow arbitrary code execution.": "Funksjoner tillater vilkårlig kodekjøring.", "Functions imported successfully": "Funksjoner er importert", "Gemini": "Gemini", "Gemini API Config": "Konfigurasjon", "Gemini API Key is required.": "Det kreves en API-nøkkel for Gemini.", "Gender": "", "General": "Generelt", "Generate": "", "Generate an image": "Genrer et bilde", "Generate Image": "<PERSON><PERSON> bilde", "Generate prompt pair": "<PERSON><PERSON> ledetekst-kombinasjon", "Generating search query": "<PERSON><PERSON><PERSON>", "Generating...": "", "Get information on {{name}} in the UI": "", "Get started": "Kom i gang", "Get started with {{WEBUI_NAME}}": "Kom i gang med {{WEBUI_NAME}}", "Global": "Globalt", "Good Response": "<PERSON><PERSON> svar", "Google Drive": "Google Drive", "Google PSE API Key": "API-nøkkel for Google PSE", "Google PSE Engine Id": "Motor-ID for Google PSE", "Gravatar": "", "Group": "Gruppe", "Group created successfully": "Gruppe opprettet", "Group deleted successfully": "Gruppe slettet", "Group Description": "Beskrivelse av gruppe", "Group Name": "Navn på gruppe", "Group updated successfully": "Gruppe oppdatert", "Groups": "Grupper", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "Haptisk tilbakemelding", "Height": "", "Hello, {{name}}": "Hei, {{name}}!", "Help": "<PERSON><PERSON><PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "Hjelp oss med å skape den beste fellesskapsledertavlen ved å dele tilbakemeldingshistorikken din.", "Hex Color": "Hex-farge", "Hex Color - Leave empty for default color": "Hex-farge – la stå tom for standard farge", "Hide": "Skjul", "Hide from Sidebar": "", "Hide Model": "", "High": "", "High Contrast Mode": "", "Home": "<PERSON><PERSON><PERSON>", "Host": "Host", "How can I help you today?": "Hva kan jeg hjelpe deg med i dag?", "How would you rate this response?": "<PERSON><PERSON><PERSON> vurderer du dette svaret?", "HTML": "", "Hybrid Search": "Hybrid-søk", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Jeg bekrefter at jeg har lest og forstår konsekvensene av mine handlinger. Jeg er klar over risikoen forbundet med å kjøre vilkårlig kode, og jeg har verifisert kildens pålitelighet.", "ID": "ID", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "Vekk nysgjerrigheten", "Image": "<PERSON><PERSON>", "Image Compression": "Bildekomprimering", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "Bildegenering", "Image Generation (Experimental)": "Bildegenerering (eksperimentell)", "Image Generation Engine": "Bildegenereringsmotor", "Image Max Compression Size": "Maks komprimeringsstørrelse for bilder", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "Generering av ledetekster for bilder", "Image Prompt Generation Prompt": "Ledetekst for generering av bilde", "Image Settings": "<PERSON><PERSON>inn<PERSON><PERSON><PERSON>", "Images": "Bilder", "Import": "", "Import Chats": "Importer chatter", "Import Config from JSON File": "Importer konfigurasjon fra en JSON-fil", "Import From Link": "", "Import Functions": "Importer funksjoner", "Import Models": "Importer modeller", "Import Notes": "", "Import Presets": "Importer forhåndsinnstillinger", "Import Prompt Suggestions": "", "Import Prompts": "Importer ledetekster", "Import Tools": "Importer verktøy", "Important Update": "<PERSON><PERSON><PERSON><PERSON> op<PERSON>", "Include": "<PERSON><PERSON><PERSON><PERSON>", "Include `--api-auth` flag when running stable-diffusion-webui": "Inkluder flagget --api-auth når du kjører stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "Inkluder flagget --api når du kjører stable-diffusion-webui", "Includes SharePoint": "Inkluderer SharePoint", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "", "Info": "Info", "Initials": "", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "", "Input": "", "Input commands": "Inntast kommandoer", "Input Key (e.g. text, unet_name, steps)": "", "Input Variables": "", "Insert": "", "Insert Follow-Up Prompt to Input": "", "Insert Prompt as Rich Text": "", "Install from Github URL": "Installer fra GitHub-URL", "Instant Auto-Send After Voice Transcription": "Øyeblikkelig automatisk sending etter taletranskripsjon", "Integration": "", "Interface": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Invalid file content": "", "Invalid file format.": "Ugyldig filformat.", "Invalid JSON file": "", "Invalid JSON format for ComfyUI Workflow.": "", "Invalid JSON format in Additional Config": "", "Invalid Tag": "<PERSON><PERSON><PERSON><PERSON>tt", "is typing...": "Skriver...", "Italic": "", "January": "januar", "Jina API Key": "API-n<PERSON><PERSON><PERSON> for Jina", "join our Discord for help.": "bli med i Discord-fellesskapet vårt for å få hjelp.", "JSON": "JSON", "JSON Preview": "Forhåndsvisning av JSON", "July": "juli", "June": "juni", "Jupyter Auth": "<PERSON><PERSON><PERSON>-autentisering", "Jupyter URL": "<PERSON><PERSON><PERSON>-URL", "JWT Expiration": "JWT-utløp", "JWT Token": "JWT-token", "Kagi Search API Key": "API-nøkkel for Kagi Search", "Keep Follow-Up Prompts in Chat": "", "Keep in Sidebar": "", "Key": "<PERSON><PERSON><PERSON><PERSON>", "Key is required": "", "Keyboard shortcuts": "Hurtigtaster", "Knowledge": "Kunnska<PERSON>", "Knowledge Access": "Tilgang til kunnskap", "Knowledge Base": "", "Knowledge created successfully.": "Kunnskap opprettet.", "Knowledge deleted successfully.": "Kunnskap slettet.", "Knowledge Description": "", "Knowledge Name": "", "Knowledge Public Sharing": "", "Knowledge reset successfully.": "Tilbakestilling av kunnskap vellykket.", "Knowledge updated successfully": "Kunnskap oppdatert", "Kokoro.js (Browser)": "Kokoro.js (nettleser)", "Kokoro.js Dtype": "Kokoro.js Dtype", "Label": "Etikett", "Landing Page Mode": "Modus for startside", "Language": "Språk", "Language Locales": "", "Last Active": "Sist aktiv", "Last Modified": "<PERSON>st endret", "Last reply": "<PERSON><PERSON> svar", "LDAP": "LDAP", "LDAP server updated": "LDAP-server oppdatert", "Leaderboard": "<PERSON><PERSON><PERSON><PERSON>", "Learn More": "", "Learn more about OpenAPI tool servers.": "", "Leave empty for no compression": "", "Leave empty for unlimited": "La stå tomt for ubegrenset", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{url}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "La stå tomt for å inkludere alle modeller", "Leave empty to use the default prompt, or enter a custom prompt": "La stå tomt for å bruke standard ledetekst, eller angi en tilpasset ledetekst", "Leave model field empty to use the default model.": "La modellfeltet stå tomt for å bruke standard modell.", "lexical": "", "License": "<PERSON><PERSON><PERSON>", "Lift List": "", "Light": "Lys", "Listening...": "Lyt<PERSON> ...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "Språkmodeller kan gjøre feil. Kontroller viktige opplysninger.", "Loader": "", "Loading Kokoro.js...": "Laster <PERSON>.js ...", "Loading...": "Laster...", "Local": "<PERSON><PERSON>", "Local Task Model": "", "Location access not allowed": "Tilgang til lokasjon er ikke tillatt", "Lost": "Tapt", "Low": "", "LTR": "LTR", "Made by Open WebUI Community": "Laget av OpenWebUI-fellesskapet", "Make password visible in the user interface": "", "Make sure to enclose them with": "<PERSON><PERSON><PERSON> for å omslutte dem med", "Make sure to export a workflow.json file as API format from ComfyUI.": "<PERSON><PERSON><PERSON> for å eksportere en workflow.json-fil i API-formatet fra ComfyUI.", "Male": "", "Manage": "Administrer", "Manage Direct Connections": "Behandle direkte koblinger", "Manage Models": "Behandle modeller", "Manage Ollama": "Be<PERSON><PERSON>", "Manage Ollama API Connections": "Behandle API-tilkoblinger for Ollama", "Manage OpenAI API Connections": "Behandle API-tilkoblinger for OpenAPI", "Manage Pipelines": "Behandle pipelines", "Manage Tool Servers": "", "Manage your account information.": "", "March": "mars", "Markdown": "", "Markdown (Header)": "", "Max Speakers": "", "Max Upload Count": "<PERSON><PERSON> antall opp<PERSON>", "Max Upload Size": "<PERSON><PERSON> stø<PERSON>se på opplasting", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Maksimalt tre modeller kan lastes ned samtidig. Prøv igjen senere.", "May": "mai", "Medium": "", "Memories accessible by LLMs will be shown here.": "Språkmodellers tilgjengelige minner vises her.", "Memory": "<PERSON><PERSON>", "Memory added successfully": "<PERSON>ne lagt til", "Memory cleared successfully": "<PERSON><PERSON>ø<PERSON>", "Memory deleted successfully": "<PERSON><PERSON> s<PERSON>t", "Memory updated successfully": "<PERSON><PERSON>", "Merge Responses": "<PERSON><PERSON> svar", "Merged Response": "Sam<PERSON><PERSON><PERSON><PERSON><PERSON> svar", "Message rating should be enabled to use this feature": "V<PERSON><PERSON> av meldinger må være aktivert for å ta i bruk denne funksjonen", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Meldinger du sender etter at du har opprettet lenken, blir ikke delt. Brukere med URL-en vil kunne se den delte chatten.", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "<PERSON><PERSON>", "Model '{{modelName}}' has been successfully downloaded.": "<PERSON><PERSON> {{modelName}} er lastet ned.", "Model '{{modelTag}}' is already in queue for downloading.": "Modellen {{modelTag}} er allerede i nedlastingskøen.", "Model {{modelId}} not found": "Finner ikke modellen {{modelId}}", "Model {{modelName}} is not vision capable": "Modellen {{modelName}} er ikke egnet til visuelle data", "Model {{name}} is now {{status}}": "<PERSON><PERSON> {{name}} er nå {{status}}", "Model {{name}} is now hidden": "", "Model {{name}} is now visible": "", "Model accepts file inputs": "", "Model accepts image inputs": "Modellen godtar bildeinndata", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "<PERSON>len er opprettet!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Modellfilsystembane oppdaget. Kan ikke fortsette fordi modellens kortnavn er påkrevd for oppdatering.", "Model Filtering": "Filtrering av modeller", "Model ID": "Modell-ID", "Model ID is required.": "", "Model IDs": "Modell-ID-er", "Model Name": "<PERSON><PERSON>", "Model name already exists, please choose a different one": "", "Model Name is required.": "", "Model not selected": "Modell ikke valgt", "Model Params": "Modellparametere", "Model Permissions": "Modelltillatelser", "Model unloaded successfully": "", "Model updated successfully": "Modell oppdatert", "Model(s) do not support file upload": "", "Modelfile Content": "Modellfilinnhold", "Models": "Modeller", "Models Access": "Tilgang til modeller", "Models configuration saved successfully": "Kofigurasjon av modeller er lagret", "Models Public Sharing": "", "Mojeek Search API Key": "API-nøekkel for Mojeek Search", "more": "mer", "More": "<PERSON><PERSON>", "More Concise": "", "More Options": "", "Move": "", "Name": "Navn", "Name and ID are required, please fill them out": "", "Name your knowledge base": "Gi kunnskapsbasen et navn", "Native": "Opprinnelig", "New Button": "", "New Chat": "Ny chat", "New Folder": "Ny mappe", "New Function": "", "New Note": "", "New Password": "<PERSON><PERSON><PERSON> passord", "New Tool": "", "new-channel": "ny-kanal", "Next message": "", "No chats found": "", "No chats found for this user.": "", "No chats found.": "", "No content": "", "No content found": "Finner ikke noe innhold", "No content found in file.": "", "No content to speak": "Mangler innhold for tale", "No conversation to save": "", "No distance available": "Ingen avstand tilgjengelig", "No feedbacks found": "<PERSON><PERSON> ingen til<PERSON><PERSON><PERSON>er", "No file selected": "Ingen fil valgt", "No groups with access, add a group to grant access": "Ingen grupper med tilgang. Legg til en gruppe som skal ha tilgang.", "No HTML, CSS, or JavaScript content found.": "<PERSON>er ikke noe HTML, CSS- eller JavaScript-innhold.", "No inference engine with management support found": "Fant ingen konklusjonsmotor med støtte for administrasjon", "No knowledge found": "Finner ingen kunns<PERSON>per", "No memories to clear": "", "No model IDs": "Ingen modell-ID-er", "No models found": "Finner ingen modeller", "No models selected": "Ingen modeller er valgt", "No Notes": "", "No results": "<PERSON>er ingen resultater", "No results found": "<PERSON>er ingen resultater", "No search query generated": "<PERSON><PERSON> søkespørringer er generert", "No source available": "<PERSON>gen kilde <PERSON>", "No suggestion prompts": "Ingen forslagsprompter", "No users were found.": "Finner ingen brukere", "No valves": "", "No valves to update": "Ingen ventiler å oppdatere", "Node Ids": "", "None": "Ingen", "Not factually correct": "Uriktig informasjon", "Not helpful": "<PERSON><PERSON><PERSON> n<PERSON>g", "Note deleted successfully": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Merk: <PERSON><PERSON> du setter en minimumspoengsum, returnerer søket kun dokumenter med en poengsum som er større enn eller lik minimumspoengsummen.", "Notes": "Notater", "Notification Sound": "Lyd for varsler", "Notification Webhook": "Webhook for varsler", "Notifications": "<PERSON><PERSON><PERSON>", "November": "november", "OAuth ID": "OAuth-ID", "October": "oktober", "Off": "Av", "Okay, Let's Go!": "OK, kjør på!", "OLED Dark": "OLED mørk", "Ollama": "Ollama", "Ollama API": "Ollama-API", "Ollama API settings updated": "API-innstillinger for Ollama er oppdatert", "Ollama Version": "Ollama-versjon", "On": "Aktivert", "OneDrive": "OneDrive", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "Bare alfanumeriske tegn og bindestreker er tillatt", "Only alphanumeric characters and hyphens are allowed in the command string.": "Bare alfanumeriske tegn og bindestreker er tillatt i kommandostrengen.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "<PERSON>e samlinger kan redigeres, eller lag en ny kunnskapsbase for å kunne redigere / legge til dokumenter.", "Only markdown files are allowed": "", "Only select users and groups with permission can access": "Bare utvalgte brukere og grupper med tillatelse kan få tilgang", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Oi! Det ser ut som URL-en er ugyldig. Dobbeltsjekk, og prøv på nytt.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Oi! Det er fortsatt filer som lastes opp. Vent til opplastingen er ferdig.", "Oops! There was an error in the previous response.": "Oi! Det er en feil i det forrige svaret.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Oi! Du bruker en ikke-støttet metode (bare frontend). Du må kjøre WebUI fra backend.", "Open file": "<PERSON><PERSON><PERSON> fil", "Open in full screen": "Åpne i fullskjerm", "Open modal to configure connection": "", "Open Modal To Manage Floating Quick Actions": "", "Open new chat": "Å<PERSON>ne ny chat", "Open Sidebar": "", "Open User Profile Menu": "", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "Open WebUI bruker faster-whisper internt.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI bruker SpeechT5 og CMU Arctic-høytalerinnbygginger", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Open WebUI-versjonen (v{{OPEN_WEBUI_VERSION}}) er lavere enn den påkrevde versjonen (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "OpenAI-API", "OpenAI API Config": "API-konfigurasjon for OpenAI", "OpenAI API Key is required.": "API-nøkkel for OpenAI kreves.", "OpenAI API settings updated": "API-innstillinger for OpenAI er oppdatert", "OpenAI URL/Key required.": "URL/nøkkel for OpenAI kreves.", "openapi.json URL or Path": "", "Optional": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "eller", "Ordered List": "", "Organize your users": "Organisere brukerne dine", "Other": "<PERSON><PERSON>", "OUTPUT": "UTDATA", "Output format": "Format på utdata", "Output Format": "", "Overview": "Oversikt", "page": "side", "Paginate": "", "Parameters": "", "Password": "Passord", "Passwords do not match.": "", "Paste Large Text as File": "Lim inn mye tekst som fil", "PDF document (.pdf)": "PDF-dokument (.pdf)", "PDF Extract Images (OCR)": "Uthenting av PDF-bilder (OCR)", "pending": "a<PERSON><PERSON><PERSON>", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "Tilgang avslått ved bruk av medieenheter", "Permission denied when accessing microphone": "Tilgang avslått ved bruk av mikrofonen", "Permission denied when accessing microphone: {{error}}": "Tilgang avslått ved bruk av mikrofonen: {{error}}", "Permissions": "<PERSON><PERSON><PERSON><PERSON>", "Perplexity API Key": "", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "Tilpassing", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "Fest", "Pinned": "Festet", "Pioneer insights": "Nyskapende innsikt", "Pipe": "", "Pipeline deleted successfully": "Pipeline slettet", "Pipeline downloaded successfully": "Pipeline lastet ned", "Pipelines": "Pipelines", "Pipelines are a plugin system with arbitrary code execution —": "Pipelines er et programtilleggssystem med vilkårlig kodekjøring —", "Pipelines Not Detected": "Ingen pipelines oppdaget", "Pipelines Valves": "Pipeline-ventiler", "Plain text (.md)": "", "Plain text (.txt)": "<PERSON> tekst (.txt)", "Playground": "<PERSON><PERSON><PERSON><PERSON>", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "Les gjennom følgende advarsler grundig:", "Please do not close the settings page while loading the model.": "Ikke lukk siden Innstillinger mens du laster inn modellen.", "Please enter a message or attach a file.": "", "Please enter a prompt": "Angi en ledetekst", "Please enter a valid path": "", "Please enter a valid URL": "", "Please fill in all fields.": "Fyll i alle felter", "Please select a model first.": "Velg en modeller først.", "Please select a model.": "Velg en modell.", "Please select a reason": "Velg en årsak", "Please wait until all files are uploaded.": "", "Port": "Port", "Positive attitude": "Positiv holdning", "Prefer not to say": "", "Prefix ID": "Prefiks-ID", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "Prefiks-ID brukes for å unngå konflikter med andre tilkoblinger ved å legge til et prefiks til modell-ID-ene. La det stå tomt for å deaktivere", "Prevent file creation": "", "Preview": "", "Previous 30 days": "Siste 30 dager", "Previous 7 days": "Siste 7 dager", "Previous message": "", "Private": "", "Profile": "Profil", "Prompt": "Ledetekst", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Ledetekst (f.eks. <PERSON> meg noe morsomt om romerriket)", "Prompt Autocompletion": "", "Prompt Content": "Ledetekstinnhold", "Prompt created successfully": "Ledetekst opprettet", "Prompt suggestions": "Forslag til ledetekst", "Prompt updated successfully": "Ledetekst oppdatert", "Prompts": "Ledetekster", "Prompts Access": "Tilgang til ledetekster", "Prompts Public Sharing": "", "Public": "", "Pull \"{{searchValue}}\" from Ollama.com": "Hent {{searchValue}} fra Ollama.com", "Pull a model from Ollama.com": "Hent en modell fra Ollama.com", "Query Generation Prompt": "Ledetekst for genering av spø<PERSON>er", "Quick Actions": "", "RAG Template": "RAG-mal", "Rating": "Vurdering", "Re-rank models by topic similarity": "Ny rangering av modeller etter emnelikhet", "Read": "Les", "Read Aloud": "<PERSON>", "Reason": "", "Reasoning Effort": "Resonneringsinnsats", "Reasoning Tags": "", "Record": "", "Record voice": "Ta opp tale", "Redirecting you to Open WebUI Community": "Omdirigerer deg til OpenWebUI-fellesskapet", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "O<PERSON><PERSON> deg selv som \"Bruker\" (f.eks. \"Bruker lærer spansk\")", "References from": "Hen<PERSON>r fra", "Refused when it shouldn't have": "<PERSON><PERSON><PERSON> n<PERSON>r det ikke burde ha blitt det", "Regenerate": "Generer på nytt", "Regenerate Menu": "", "Reindex": "", "Reindex Knowledge Base Vectors": "", "Release Notes": "Utgivelsesnotater", "Releases": "", "Relevance": "<PERSON><PERSON><PERSON>", "Relevance Threshold": "", "Remember Dismissal": "", "Remove": "<PERSON><PERSON><PERSON>", "Remove {{MODELID}} from list.": "", "Remove file": "", "Remove File": "", "Remove image": "", "Remove Model": "<PERSON><PERSON><PERSON> modell", "Remove this tag from list": "", "Rename": "Gi nytt navn", "Reorder Models": "Sorter modeller på nytt", "Reply in Thread": "<PERSON><PERSON> i tråd", "Reranking Engine": "", "Reranking Model": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Reset": "Tilbakestill", "Reset All Models": "Tilbakestill alle modeller", "Reset Image": "Tilbakestill bilde", "Reset Upload Directory": "Tilbakestill opplastingskatalog", "Reset Vector Storage/Knowledge": "Tilbakestill Vector-lagring/kunnskap", "Reset view": "Tilbakestill visning", "Response": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Svar-var<PERSON> kan ikke aktiveres fordi tilgang til nettstedet er nektet. Gå til nettleserinnstillingene dine for å gi den nødvendige tilgangen.", "Response splitting": "Oppdeling av svar", "Response Watermark": "", "Result": "Resultat", "RESULT": "Resultat", "Retrieval": "", "Retrieval Query Generation": "Generering av spørsmål om henting", "Rich Text Input for Chat": "<PERSON><PERSON> for chat", "RK": "RK", "Role": "<PERSON><PERSON>", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "<PERSON><PERSON><PERSON><PERSON>", "Running": "<PERSON><PERSON><PERSON><PERSON>", "Running...": "<PERSON><PERSON><PERSON><PERSON>...", "Save": "Lagre", "Save & Create": "Lagre og opprett", "Save & Update": "Lagre og oppdater", "Save As Copy": "Lagre som kopi", "Save Chat": "", "Save Tag": "<PERSON><PERSON><PERSON><PERSON>", "Saved": "<PERSON>g<PERSON>", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Lagring av chattelogger direkte til nettleserens lagringsområde støttes ikke lenger. Ta et øyeblikk til å laste ned og slette chatteloggende dine ved å klikke på knappen nedenfor. <PERSON><PERSON><PERSON> be<PERSON><PERSON> deg, du kan enkelt importere chatteloggene dine til backend på nytt via", "Scroll On Branch Change": "", "Search": "<PERSON><PERSON><PERSON>", "Search a model": "<PERSON><PERSON><PERSON> etter en modell", "Search all emojis": "", "Search Base": "Søke etter base", "Search Chats": "<PERSON><PERSON><PERSON> etter chatter", "Search Collection": "<PERSON><PERSON><PERSON> etter samling", "Search Filters": "<PERSON><PERSON><PERSON> etter filtre", "search for archived chats": "", "search for folders": "", "search for pinned chats": "", "search for shared chats": "", "search for tags": "søk etter etiketter", "Search Functions": "<PERSON><PERSON><PERSON> etter <PERSON>", "Search In Models": "", "Search Knowledge": "<PERSON><PERSON><PERSON> etter kun<PERSON>", "Search Models": "<PERSON><PERSON><PERSON> etter modeller", "Search Notes": "", "Search options": "<PERSON><PERSON><PERSON> etter alternativer", "Search Prompts": "<PERSON><PERSON><PERSON> etter <PERSON>", "Search Result Count": "<PERSON><PERSON><PERSON> søkeresultater", "Search the internet": "<PERSON><PERSON><PERSON> på <PERSON>t", "Search Tools": "Søkeverktøy", "SearchApi API Key": "API-nøkkel for SearchApi", "SearchApi Engine": "Motor for SearchApi", "Searched {{count}} sites": "<PERSON><PERSON><PERSON><PERSON> på {{count}} nettsider", "Searching \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON> et<PERSON> \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON> etter kun<PERSON> for \"{{searchQuery}}\"", "Searching the web...": "", "Searxng Query URL": "Searxng forespørsels-URL", "See readme.md for instructions": "Se readme.md for å få instruksjoner", "See what's new": "Se hva som er nytt", "Seed": "Seed", "Select": "", "Select a base model": "Velg en grunnmodell", "Select a base model (e.g. llama3, gpt-4o)": "", "Select a conversation to preview": "", "Select a engine": "Velg en motor", "Select a function": "Velg en funksjon", "Select a group": "Velg en gruppe", "Select a language": "", "Select a mode": "", "Select a model": "Velg en modell", "Select a model (optional)": "", "Select a pipeline": "Velg en pipeline", "Select a pipeline url": "Velg en pipeline-URL", "Select a reranking model engine": "", "Select a role": "", "Select a theme": "", "Select a tool": "Velg et verktøy", "Select a voice": "", "Select an auth method": "Velg en autentiseringsmetode", "Select an embedding model engine": "", "Select an engine": "", "Select an Ollama instance": "Velg en Ollama-forekomst", "Select an output format": "", "Select dtype": "", "Select Engine": "Velg motor", "Select how to split message text for TTS requests": "", "Select Knowledge": "<PERSON><PERSON>g kun<PERSON>", "Select only one model to call": "Velg bare én modell som skal kalles", "Selected model(s) do not support image inputs": "Valgte modell(er) støtter ikke bildeinndata", "semantic": "", "Semantic distance to query": "Semantisk distanse til spørring", "Send": "Send", "Send a Message": "Send en melding", "Send message": "Send melding", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Sender `stream_options: { include_usage: true }` i forespørselen.\nStøttede leverandører returnerer informasjon i svaret om bruk av token når denne parameteren er angitt.", "September": "september", "SerpApi API Key": "Angi API-nøkkel for SerpApi", "SerpApi Engine": "Motor for SerpApi", "Serper API Key": "API-n<PERSON>k<PERSON> for Serper", "Serply API Key": "API-n<PERSON>k<PERSON> for Serply", "Serpstack API Key": "API-n<PERSON><PERSON><PERSON> for Serpstack", "Server connection verified": "Servertilkobling bekreftet", "Session": "", "Set as default": "Angi som standard", "Set CFG Scale": "Angi CFG-skala", "Set Default Model": "Angi standard modell", "Set embedding model": "<PERSON><PERSON> inn<PERSON><PERSON>", "Set embedding model (e.g. {{model}})": "<PERSON><PERSON> (f.eks. {{model}})", "Set Image Size": "<PERSON><PERSON>", "Set reranking model (e.g. {{model}})": "<PERSON><PERSON> modell for omrangering (f.eks. {{model}})", "Set Sampler": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Set Scheduler": "<PERSON><PERSON> planlegger", "Set Steps": "<PERSON><PERSON> steg", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "<PERSON>i antall nivå som skal avlastes til GPU-en. <PERSON><PERSON> du øker denne verdien, kan du for<PERSON>re y<PERSON>sen betyd<PERSON> for modeller som er optimalisert for GPU-akselerasjon, men det kan også føre til økt strøm- og GPU-bruk.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "<PERSON>i antall arbeidstråder som skal brukes til beregning. Dette alternativet kontrollerer hvor mange tråder som brukes til å behandle innkommende forespørsler samtidig. <PERSON><PERSON> du øker denne verdien, kan det forbedre ytelsen under arbeidsbelastninger med høy samtidighet, men det kan også føre til økt forbruk av CPU-ressurser.", "Set Voice": "<PERSON><PERSON> stemme", "Set whisper model": "<PERSON><PERSON> whisper-modell", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets how far back for the model to look back to prevent repetition.": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "", "Sets the size of the context window used to generate the next token.": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Angir hvilke stoppsekvenser som skal brukes. <PERSON><PERSON><PERSON> dette mønsteret forekommer, stopper LLM genereringen av tekst og returnerer. Du kan angi flere stoppmønstre ved å spesifisere flere separate stoppparametere i en modellfil.", "Settings": "Innstillinger", "Settings saved successfully!": "Innstillinger lagret!", "Share": "Del", "Share Chat": "Del chat", "Share to Open WebUI Community": "Del med OpenWebUI-fellesskapet", "Share your background and interests": "", "Sharing Permissions": "", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "Vis", "Show \"What's New\" modal on login": "Vis \"Hva er nytt\"-modal ved innlogging", "Show Admin Details in Account Pending Overlay": "<PERSON>is <PERSON> i ventende kontovisning", "Show All": "", "Show Formatting Toolbar": "", "Show image preview": "", "Show Less": "", "Show Model": "", "Show shortcuts": "<PERSON><PERSON> s<PERSON>", "Show your support!": "Vis din støtte!", "Showcased creativity": "Fremhevet kreativitet", "Sign in": "Logg inn", "Sign in to {{WEBUI_NAME}}": "<PERSON>gg på {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "Logg på {{WEBUI_NAME}} med LDAP", "Sign Out": "Logg ut", "Sign up": "Registrer deg", "Sign up to {{WEBUI_NAME}}": "Registrer deg for {{WEBUI_NAME}}", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to False.": "", "Signing in to {{WEBUI_NAME}}": "<PERSON><PERSON> på {{WEBUI_NAME}}", "Sink List": "", "sk-1234": "sk-1234", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Something went wrong :/": "", "Sonar": "", "Sonar Deep Research": "", "Sonar Pro": "", "Sonar Reasoning": "", "Sonar Reasoning Pro": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "<PERSON><PERSON>", "Speech Playback Speed": "Has<PERSON>ghet på avspilling av tale", "Speech recognition error: {{error}}": "<PERSON>il ved taleg<PERSON><PERSON><PERSON>: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "Motor for Tale-til-tekst", "Start of the channel": "Starten av kanalen", "Start Tag": "", "STDOUT/STDERR": "STDOUT/STDERR", "Stop": "Stopp", "Stop Generating": "", "Stop Sequence": "Stoppsekvens", "Stream Chat Response": "Strømme chat-svar", "Stream Delta Chunk Size": "", "Strikethrough": "", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "STT-modell", "STT Settings": "STT-innstillinger", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "Undertittel (f.eks. om romerriket)", "Success": "<PERSON><PERSON><PERSON>", "Successfully imported {{userCount}} users.": "", "Successfully updated.": "Oppdatert.", "Suggest a change": "", "Suggested": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Support": "Bidra", "Support this plugin:": "Bidra til denne utvidelsen:", "Supported MIME Types": "", "Sync directory": "Synkroniseringsmappe", "System": "System", "System Instructions": "Systeminstruksjoner", "System Prompt": "Systemledetekst", "Tags": "", "Tags Generation": "Genering av etiketter", "Tags Generation Prompt": "Ledetekst for genering av etikett", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "", "Talk to model": "Snakk med modellen", "Tap to interrupt": "Trykk for å avbryte", "Task List": "", "Task Model": "", "Tasks": "Oppgaver", "Tavily API Key": "API-n<PERSON><PERSON><PERSON> for Tavily", "Tavily Extract Depth": "", "Tell us more:": "<PERSON><PERSON> oss mer:", "Temperature": "Temperatur", "Temporary Chat": "<PERSON><PERSON><PERSON>dig chat", "Temporary Chat by Default": "", "Text Splitter": "Oppdeling av tekst", "Text-to-Speech": "", "Text-to-Speech Engine": "Tekst-til-tale-motor", "Thanks for your feedback!": "Takk for tilbakemeldingen!", "The Application Account DN you bind with for search": "Applikasjonskontoens DN du binder deg med for søking", "The base to search for users": "Basen for å søke etter brukere", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Utviklerne bak denne utvidelsen er lidenskapelige frivillige fra fellesskapet. Hvis du finner denne utvidelsen nyttig, vennligst vurder å bidra til utviklingen.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "<PERSON><PERSON><PERSON><PERSON><PERSON> over evalueringer er basert på Elo-rangeringssystemet, og oppdateres i sanntid.", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "LDAP-attributtet som tilsvarer e-posten som brukerne bruker for å logge på.", "The LDAP attribute that maps to the username that users use to sign in.": "LDAP-attributtet som tilsvarer brukernavnet som brukerne bruker for å logge på.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "Ledertavlen er for øyeblikket i betaversjon, og vi kommer kanskje til å justere beregningene etter hvert som vi forbedrer algoritmen.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "Den maksimale filstørrelsen i MB. Hvis en filstørrelse overskrider denne g<PERSON>, blir ikke filen lastet opp.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Maksimalt antall filer som kan brukes samtidig i chatten. Hvis antallet filer overskrider denne g<PERSON>, blir de ikke lastet opp.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The passwords you entered don't quite match. Please double-check and try again.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Poengsummen skal være en verdi mellom 0,0 (0 %) og 1,0 (100 %).", "The stream delta chunk size for the model. Increasing the chunk size will make the model respond with larger pieces of text at once.": "", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "", "The Weight of BM25 Hybrid Search. 0 more lexical, 1 more semantic. Default 0.5": "", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "<PERSON><PERSON>", "Thinking...": "Tenker ...", "This action cannot be undone. Do you wish to continue?": "<PERSON>ne <PERSON>en kan ikke angres. Vil du fortsette?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "", "This chat won't appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "<PERSON><PERSON> sikrer at de verdifulle samtalene dine lagres sikkert i backend-databasen din. Takk!", "This feature is experimental and may be modified or discontinued without notice.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Dette er en eksperimentell funksjon. Det er mulig den ikke fungerer som forventet, og den kan endres når som helst.", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Dette alternativet sletter alle eksisterende filer i samlingen og erstatter dem med nyopplastede filer.", "This response was generated by \"{{model}}\"": "<PERSON>te svaret er generert av \"{{modell}}\"", "This will delete": "<PERSON><PERSON> sletter", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "Dette sletter <strong>{{NAME}}</strong> og <strong>alt innholdet</strong>.", "This will delete all models including custom models": "<PERSON><PERSON> sletter alle modeller, inkludert tilpassede modeller", "This will delete all models including custom models and cannot be undone.": "<PERSON><PERSON> sletter alle modeller, inkludert tilpassede modeller, og kan ikke angres.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Dette tilbakestiller kunnskapsbasen og synkroniserer alle filer. Vil du fortsette?", "Thorough explanation": "<PERSON><PERSON><PERSON><PERSON> forklaring", "Thought for {{DURATION}}": "Tenkte i {{DURATION}}", "Thought for {{DURATION}} seconds": "Tenkte i {{DURATION}} sekunder", "Thought for less than a second": "", "Thread": "<PERSON><PERSON><PERSON><PERSON>", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "Server-URL for Tika kreves.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Tips: <PERSON><PERSON><PERSON><PERSON> flere variabelplasser etter hverandre ved å trykke på TAB-tasten i chat-inntastingsfeltet etter hver erstatning.", "Title": "<PERSON><PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "<PERSON>ittel (f.eks. <PERSON> meg noe morsomt)", "Title Auto-Generation": "Automatisk tittelgenerering", "Title cannot be an empty string.": "<PERSON>ittel kan ikke være en tom streng.", "Title Generation": "Genering av tittel", "Title Generation Prompt": "Ledetekst for tittelgenerering", "TLS": "TLS", "To access the available model names for downloading,": "<PERSON><PERSON> du vil ha tilgang til modellnavn tilgjengelige for nedlasting,", "To access the GGUF models available for downloading,": "<PERSON><PERSON> du vil ha tilgang til GGUF-modellene tilg<PERSON>lige for nedlasting,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "<PERSON><PERSON> du vil ha tilgang til WebUI, må du kontakte administrator. Administratorer kan behandle brukeres status fra Admin-panelet.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "<PERSON>vis du vil legge til kunnskapsbaser her, må du først legge dem til i arbeidsområdet \"Kunnskap\".", "To learn more about available endpoints, visit our documentation.": "<PERSON><PERSON> du vil finne ut mer om tilgjengelige endepunkter, kan du gå til dokumentasjonen vår.", "To learn more about powerful prompt variables, click here": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "For å beskytte personvernet ditt deles bare vurderinger, modell-ID-er, etiketter og metadata fra dine tilbakemeldinger. Chattelogger forblir private og inkluderes ikke.", "To select actions here, add them to the \"Functions\" workspace first.": "<PERSON><PERSON> du vil velge handlinger her, må du først legge dem til i arbeidsområdet \"Funksjoner\".", "To select filters here, add them to the \"Functions\" workspace first.": "<PERSON><PERSON> du vil velge filtre her, må du først legge dem til i arbeidsområdet \"Funksjoner\".", "To select toolkits here, add them to the \"Tools\" workspace first.": "<PERSON><PERSON> du vil velge verktø<PERSON>ett her, må du først legge dem til i arbeidsområdet \"Verktøy\".", "Toast notifications for new updates": "Hurtigmelding-notifikasjon for nye oppdateringer", "Today": "I dag", "Toggle search": "", "Toggle settings": "Veksle innstillinger", "Toggle sidebar": "Veksle sidefelt", "Toggle whether current connection is active.": "", "Token": "Token", "Too verbose": "For omfattende", "Tool created successfully": "Verktøy opprettet", "Tool deleted successfully": "Verkt<PERSON><PERSON> s<PERSON>t", "Tool Description": "Verktøyets beskrivelse", "Tool ID": "Verktøyets ID", "Tool imported successfully": "Verktøy importert", "Tool Name": "Verktøyets navn", "Tool Servers": "", "Tool updated successfully": "Verktøy oppdatert", "Tools": "Verktøy", "Tools Access": "Verktøyets tilgang", "Tools are a function calling system with arbitrary code execution": "Verktøy er et funksjonskallsystem med vilkårlig kodekjøring", "Tools Function Calling Prompt": "Ledetekst for kalling av verktøyfunksjonen", "Tools have a function calling system that allows arbitrary code execution.": "Verktøy inneholder et funksjonskallsystem som tillater vilkårlig kodekjøring.", "Tools Public Sharing": "", "Top K": "Top K", "Top K Reranker": "", "Transformers": "Transformatorer", "Trouble accessing Ollama?": "Problemer med å koble til Ollama?", "Trust Proxy Environment": "Stol på proxy-milj<PERSON><PERSON>", "Try Again": "", "TTS Model": "TTS-modell", "TTS Settings": "TTS-innstillinger", "TTS Voice": "TTS-stemme", "Type": "Type", "Type Hugging Face Resolve (Download) URL": "<PERSON><PERSON>-Resolve-URL for Hugging Face", "Uh-oh! There was an issue with the response.": "Oi! Det oppstod et problem med svaret.", "UI": "UI", "Unarchive All": "Opphev arkiveringen av alle", "Unarchive All Archived Chats": "Opphev arkiveringen av alle arkiverte chatter", "Unarchive Chat": "Opphev arkivering av chat", "Underline": "", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "<PERSON><PERSON><PERSON> opp mysterier", "Unpin": "<PERSON><PERSON><PERSON><PERSON>", "Unravel secrets": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Unsupported file type.": "", "Untagged": "<PERSON>kke merket", "Untitled": "", "Update": "<PERSON><PERSON><PERSON><PERSON>", "Update and Copy Link": "Oppdater og kopier lenke", "Update for the latest features and improvements.": "<PERSON><PERSON><PERSON><PERSON> for å få siste funksjoner og forbedringer.", "Update password": "<PERSON><PERSON><PERSON><PERSON> passord", "Updated": "Oppdatert", "Updated at": "Oppdatert", "Updated At": "Oppdatert", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "Oppgrader til en lisensplan for å få flere funksjoner, inkludert tilpasset temaer og merkevarebygging, og dedikert kundestøtte.", "Upload": "Last opp", "Upload a GGUF model": "Last opp en GGUF-modell", "Upload Audio": "", "Upload directory": "Mappe for opplastinger", "Upload files": "Last opp filer", "Upload Files": "Last opp filer", "Upload Pipeline": "Last opp pipeline", "Upload Progress": "Opplastingsfremdrift", "Upload Progress: {{uploadedFiles}}/{{totalFiles}} ({{percentage}}%)": "", "URL": "URL", "URL is required": "", "URL Mode": "URL-modus", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "Bruk # i ledetekstens inntastingsfelt for å laste inn og inkludere kunnskapene dine.", "Use groups to group your users and assign permissions.": "Bruk grupper til å samle brukere og tildele tillatelser.", "Use LLM": "", "Use no proxy to fetch page contents.": "", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "user": "bruker", "User": "<PERSON><PERSON><PERSON>", "User Groups": "", "User location successfully retrieved.": "Brukerens lokasjon hentet", "User menu": "", "User Webhooks": "", "Username": "Brukernavn", "Users": "<PERSON><PERSON><PERSON>", "Using Entire Document": "", "Using Focused Retrieval": "", "Using the default arena model with all models. Click the plus button to add custom models.": "Bruker standard Arena-modellen med alle modeller. Klikk på plussknappen for å legge til egne modeller.", "Valid time units:": "Gyldige tidsenheter:", "Validate certificate": "", "Valves": "<PERSON><PERSON><PERSON>", "Valves updated": "Ventiler oppdatert", "Valves updated successfully": "Ventilene er oppdatert", "variable": "variabel", "Verify Connection": "", "Verify SSL Certificate": "", "Version": "Versjon", "Version {{selectedVersion}} of {{totalVersions}}": "Version {{selectedVersion}} av {{totalVersions}}", "View Replies": "<PERSON><PERSON> svar", "View Result from **{{NAME}}**": "", "Visibility": "Synlighet", "Vision": "", "Voice": "<PERSON><PERSON><PERSON>", "Voice Input": "Taleinndata", "Voice mode": "", "Warning": "<PERSON><PERSON><PERSON>", "Warning:": "<PERSON><PERSON><PERSON>!", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Advarsel: <PERSON><PERSON> du aktiverer denne <PERSON>, kan brukere laste opp vilkårlig kode på serveren.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Advarsel: <PERSON><PERSON> du oppdaterer eller endrer innbyggingsmodellen din, må du importere alle dokumenter på nytt.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "Advarsel! Jupyter gjør det mulig å kjøre vilkårlig kode, noe som utgjør en alvorlig sikkerhetsrisiko. Utvis ekstrem forsiktighet.", "Web": "Web", "Web API": "Web-API", "Web Loader Engine": "", "Web Search": "Nettsøk", "Web Search Engine": "Nettsøkmotor", "Web Search in Chat": "Nettsøk i chat", "Web Search Query Generation": "<PERSON><PERSON> av s<PERSON>ø<PERSON><PERSON> for nettsøk", "Webhook URL": "Webhook URL", "WebUI Settings": "Innstillinger for WebUI", "WebUI URL": "URL for WebUI", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI vil rette forespø<PERSON><PERSON> til \"{{url}}/api/chat\"", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI vil rette forespø<PERSON><PERSON> til \"{{url}}/chat/completions\"", "What are you trying to achieve?": "Hva prøver du å oppnå?", "What are you working on?": "<PERSON>va jobber du på nå?", "What's New in": "<PERSON>va er nytt i", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "<PERSON><PERSON> denne modusen er aktivert, svarer modellen på alle chattemeldinger i sanntid, og genererer et svar så snart brukeren sender en melding. Denne modusen er nyttig for live chat-applik<PERSON><PERSON>er, men kan påvirke ytelsen på tregere maskinvare.", "wherever you are": "u<PERSON>ett hvor du er", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "<PERSON><PERSON><PERSON> (Lokal)", "Why?": "Hvorfor?", "Widescreen Mode": "Bredskjermmodus", "Width": "", "Won": "<PERSON><PERSON>", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "", "Workspace": "Arbeidsområde", "Workspace Permissions": "<PERSON><PERSON><PERSON><PERSON> for arbeidsområde", "Write": "Skriv", "Write a prompt suggestion (e.g. Who are you?)": "Skriv inn et forslag til ledetekst (f.eks. Hvem er du?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Skriv inn et sammendrag på 50 ord som oppsummerer [em<PERSON> eller nø<PERSON>].", "Write something...": "Skriv inn noe...", "Write your model system prompt content here\ne.g.) You are Mario from Super Mario Bros, acting as an assistant.": "Skriv inn innholdet i modellens systemprompt her\nf.eks.: Du er Mario fra Super Mario Bros og fungerer som assistent.", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "<PERSON> går", "You": "<PERSON>", "You are currently using a trial license. Please contact support to upgrade your license.": "", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Du kan bare chatte med maksimalt {{maxCount}} fil(er) om gangen.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Du kan tilpasse interaksjonene dine med språkmodeller ved å legge til minner gjennom Administrer-knappen nedenfor, slik at de blir mer til nyttige og tilpasset deg.", "You cannot upload an empty file.": "Du kan ikke laste opp en tom fil.", "You do not have permission to upload files.": "Du har ikke tillatelse til å laste opp filer.", "You have no archived conversations.": "Du har ingen arkiverte samtaler.", "You have shared this chat": "Du har delt denne chatten", "You're a helpful assistant.": "Du er en nyttig assistent.", "You're now logged in.": "Du er nå logget inn.", "Your Account": "", "Your account status is currently pending activation.": "Status på kontoen din er for øyeblikket ventende på aktivering.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "<PERSON><PERSON> beløpet går uavkortet til utvikleren av tillegget. Open WebUI mottar ikke deler av beløpet. Den valgte betalingsplattformen kan ha gebyrer.", "Youtube": "Youtube", "Youtube Language": "", "Youtube Proxy URL": ""}