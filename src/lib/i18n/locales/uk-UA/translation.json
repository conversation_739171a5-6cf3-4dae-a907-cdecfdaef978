{"-1 for no limit, or a positive integer for a specific limit": "-1 для без обмежень або додатне ціле число для конкретного обмеження", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' or '-1' для відсутності терміну дії.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(напр. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(напр. `sh webui.sh --api`)", "(latest)": "(остання)", "(leave blank for to use commercial endpoint)": "", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "{{ models }}", "{{COUNT}} Available Tools": "", "{{COUNT}} characters": "", "{{COUNT}} extracted lines": "", "{{COUNT}} hidden lines": "{{COUNT}} прихованих рядків", "{{COUNT}} Replies": "{{COUNT}} Відповіді", "{{COUNT}} words": "", "{{model}} download has been canceled": "", "{{user}}'s Chats": "Чати {{user}}а", "{{webUIName}} Backend Required": "Необхідно підключення бекенду {{webUIName}}", "*Prompt node ID(s) are required for image generation": "*Для генерації зображення потрібно вказати ідентифікатор(и) вузла(ів)", "A new version (v{{LATEST_VERSION}}) is now available.": "Нова версія (v{{LATEST_VERSION}}) зараз доступна.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Модель задач використовується при виконанні таких завдань, як генерація заголовків для чатів та пошукових запитів в Інтернеті", "a user": "користувача", "About": "Про програму", "Accept autocomplete generation / Jump to prompt variable": "Прийняти автоматичне доповнення / Перейти до змінної промта", "Access": "Доступ", "Access Control": "Контроль доступу", "Accessible to all users": "Доступно всім користувачам", "Account": "Обліковий запис", "Account Activation Pending": "Очікування активації облікового запису", "Accurate information": "Точна інформація", "Action": "", "Action not found": "", "Action Required for Chat Log Storage": "Потрібна дія для збереження журналу чату", "Actions": "Дії", "Activate": "Активувати", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Активуйте цю команду, ввівши \"/{{COMMAND}}\" у введення чату.", "Active": "Активний", "Active Users": "Активні користувачі", "Add": "Додати", "Add a model ID": "Додайти ID моделі", "Add a short description about what this model does": "Додайте короткий опис того, що робить ця модель", "Add a tag": "Додайти тег", "Add Arena Model": "Додати модель Arena", "Add Connection": "Додати з'єднання", "Add Content": "Додати вміст", "Add content here": "Додайте вміст сюди", "Add Custom Parameter": "", "Add custom prompt": "Додати користувацьку підказку", "Add Details": "", "Add Files": "Додати файли", "Add Group": "Додати групу", "Add Memory": "Додати пам'ять", "Add Model": "Додати модель", "Add Reaction": "Додати реакцію", "Add Tag": "Додати тег", "Add Tags": "Додати теги", "Add text content": "Додати текстовий вміст", "Add User": "Додати користувача", "Add User Group": "Додати групу користувачів", "Additional Config": "", "Additional configuration options for marker. This should be a JSON string with key-value pairs. For example, '{\"key\": \"value\"}'. Supported keys include: disable_links, keep_pageheader_in_output, keep_pagefooter_in_output, filter_blank_pages, drop_repeated_text, layout_coverage_threshold, merge_threshold, height_tolerance, gap_threshold, image_threshold, min_line_length, level_count, default_level": "", "Adjusting these settings will apply changes universally to all users.": "Зміни в цих налаштуваннях будуть застосовані для всіх користувачів.", "admin": "адмін", "Admin": "А<PERSON><PERSON><PERSON><PERSON>", "Admin Panel": "Адмін-панель", "Admin Settings": "Адмін-панель", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Адміністратори мають доступ до всіх інструментів у будь-який час; користувачам потрібні інструменти, призначені для кожної моделі в робочій області.", "Advanced Parameters": "Розширені параметри", "Advanced Params": "Розширені параметри", "AI": "", "All": "Усі", "All Documents": "Усі документи", "All models deleted successfully": "Усі моделі видалені успішно", "Allow Call": "", "Allow Chat Controls": "Дозволити керування чатом", "Allow Chat Delete": "Дозволити видалення чату", "Allow Chat Deletion": "Дозволити видалення чату", "Allow Chat Edit": "Дозволити редагування чату", "Allow Chat Export": "", "Allow Chat Params": "", "Allow Chat Share": "", "Allow Chat System Prompt": "", "Allow Chat Valves": "", "Allow Continue Response": "", "Allow Delete Messages": "", "Allow File Upload": "Дозволити завантаження файлів", "Allow Multiple Models in Chat": "", "Allow non-local voices": "Дозволити не локальні голоси", "Allow Rate Response": "", "Allow Regenerate Response": "", "Allow Speech to Text": "", "Allow Temporary Chat": "Дозволити тимчасовий чат", "Allow Text to Speech": "", "Allow User Location": "Доступ до місцезнаходження", "Allow Voice Interruption in Call": "Дозволити переривання голосу під час виклику", "Allowed Endpoints": "Дозволені кінцеві точки", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "Вже є обліковий запис?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "Альтернатива top_p, що спрямована на забезпечення балансу між якістю та різноманітністю. Параметр p представляє мінімальну ймовірність для врахування токена відносно ймовірності найбільш ймовірного токена. Наприклад, при p=0.05 і ймовірності найбільш ймовірного токена 0.9, логіти зі значенням менше 0.045 відфільтровуються.", "Always": "Завжди", "Always Collapse Code Blocks": "Завжди згортати блоки коду", "Always Expand Details": "Завжди розгортати деталі", "Always Play Notification Sound": "", "Amazing": "Чудово", "an assistant": "асистента", "An error occurred while fetching the explanation": "", "Analytics": "", "Analyzed": "Проаналізовано", "Analyzing...": "Аналізую...", "and": "та", "and {{COUNT}} more": "та ще {{COUNT}}", "and create a new shared link.": "і створіть нове спільне посилання.", "Android": "", "API": "", "API Base URL": "URL-адреса API", "API Base URL for Datalab Marker service. Defaults to: https://www.datalab.to/api/v1/marker": "", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "Ключ API", "API Key created.": "Ключ API створено.", "API Key Endpoint Restrictions": "Обмеження кінцевої точки ключа API", "API keys": "Ключі API", "API Version": "", "API Version is required": "", "Application DN": "DN застосунку", "Application DN Password": "Пароль DN застосунку", "applies to all users with the \"user\" role": "стосується всіх користувачів з роллю \"користувач\"", "April": "Квітень", "Archive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Archive All Chats": "Архівувати усі чати", "Archived Chats": "Архівовані чати", "archived-chat-export": "експорт-архівованих-чатів", "Are you sure you want to clear all memories? This action cannot be undone.": "Ви впевнені, що хочете очистити усі спогади? Цю дію неможливо скасувати.", "Are you sure you want to delete this channel?": "Ви впевнені, що хочете видалити цей канал?", "Are you sure you want to delete this message?": "Ви впевнені, що хочете видалити це повідомлення?", "Are you sure you want to unarchive all archived chats?": "Ви впевнені, що хочете розархівувати усі архівовані чати?", "Are you sure?": "Ви впевнені?", "Arena Models": "Моделі Arena", "Artifacts": "Артефакти", "Ask": "Запитати", "Ask a question": "Задати питання", "Assistant": "Асис<PERSON><PERSON><PERSON>т", "Attach file from knowledge": "Прикріпити файл із знаннями", "Attention to detail": "Увага до деталей", "Attribute for Mail": "Атрибут для пошти", "Attribute for Username": "Атрибут для імені користувача", "Audio": "Ау<PERSON><PERSON><PERSON>", "August": "Серпень", "Auth": "", "Authenticate": "Автентифікувати", "Authentication": "Аутентифікація", "Auto": "", "Auto-Copy Response to Clipboard": "Автокопіювання відповіді в буфер обміну", "Auto-playback response": "Автоматичне відтворення відповіді", "Autocomplete Generation": "Генерація автозаповнення", "Autocomplete Generation Input Max Length": "Максимальна довжина введення для генерації автозаповнення", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 Рядок авторизації API", "AUTOMATIC1111 Base URL": "URL-адреса AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "Необхідна URL-адреса AUTOMATIC1111.", "Available list": "Список доступності", "Available Tools": "", "available users": "доступні користувачі", "available!": "доступно!", "Away": "Від<PERSON>у<PERSON><PERSON><PERSON>й", "Awful": "Жахливо", "Azure AI Speech": "Мовлення Azure AI", "Azure OpenAI": "Azure OpenAI", "Azure Region": "Регіон Azure", "Back": "Назад", "Bad Response": "Неправи<PERSON>ьна відповідь", "Banners": "Прапори", "Base Model (From)": "Базова модель (від)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "Bearer": "", "before": "до того, як", "Being lazy": "Не поспішати", "Beta": "Beta", "Bing Search V7 Endpoint": "Точка доступу Bing Search V7", "Bing Search V7 Subscription Key": "Ключ підписки Bing Search V7", "Bio": "", "Birth Date": "", "BM25 Weight": "", "Bocha Search API Key": "Ключ API пошуку Bocha", "Bold": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "Підсилення або штрафування конкретних токенів для обмежених відповідей. Значення зміщення будуть обмежені між -100 і 100 (включно). (За замовчуванням: відсутнє)", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "Ключ API пошуку Brave", "Bullet List": "", "Button ID": "", "Button Label": "", "Button Prompt": "", "By {{name}}": "Від {{name}}", "Bypass Embedding and Retrieval": "Минути вбудовування та пошук", "Bypass Web Loader": "", "Cache Base Model List": "", "Calendar": "Календар", "Call": "Виклик", "Call feature is not supported when using Web STT engine": "Функція виклику не підтримується при використанні Web STT (розпізнавання мовлення) рушія", "Camera": "Камера", "Cancel": "Скасувати", "Capabilities": "Можливості", "Capture": "Захоплення", "Capture Audio": "", "Certificate Path": "Шлях до сертифіката", "Change Password": "Змінити пароль", "Channel deleted successfully": "", "Channel Name": "Назва каналу", "Channel updated successfully": "", "Channels": "Канали", "Character": "Перс<PERSON><PERSON><PERSON>", "Character limit for autocomplete generation input": "Ліміт символів для введення при генерації автозаповнення", "Chart new frontiers": "Відкривати нові горизонти", "Chat": "Чат", "Chat Background Image": "Фонове зображення чату", "Chat Bubble UI": "Чат у вигляді бульбашок", "Chat Controls": "Керування чатом", "Chat Conversation": "", "Chat direction": "Напрям чату", "Chat ID": "", "Chat moved successfully": "", "Chat Overview": "Огляд чату", "Chat Permissions": "Дозволи чату", "Chat Tags Auto-Generation": "Автоматична генерація тегів чату", "Chats": "Чати", "Check Again": "Перевірити ще раз", "Check for updates": "Перевірити оновлення", "Checking for updates...": "Перевірка оновлень...", "Choose a model before saving...": "Оберіть модель перед збереженням...", "Chunk Overlap": "Перекриття фрагментів", "Chunk Size": "Розмір фрагменту", "Ciphers": "<PERSON>и<PERSON><PERSON>и", "Citation": "Цитування", "Citations": "", "Clear memory": "Очистити пам'ять", "Clear Memory": "Очистити пам'ять", "click here": "натисніть тут", "Click here for filter guides.": "Натисніть тут для інструкцій із фільтрації", "Click here for help.": "Натисніть тут, щоб отримати допомогу.", "Click here to": "Натисніть тут, щоб", "Click here to download user import template file.": "Натисніть тут, щоб завантажити файл шаблону імпорту користувача.", "Click here to learn more about faster-whisper and see the available models.": "Натисніть тут, щоб дізнатися більше про faster-whisper та переглянути доступні моделі.", "Click here to see available models.": "Натисніть тут, щоб переглянути доступні моделі.", "Click here to select": "Натисніть тут, щоб обрати", "Click here to select a csv file.": "Натисніть тут, щоб обрати csv-файл.", "Click here to select a py file.": "Натисніть тут, щоб обрати py-файл.", "Click here to upload a workflow.json file.": "Натисніть тут, щоб завантажити файл workflow.json.", "click here.": "натисніть тут.", "Click on the user role button to change a user's role.": "Натисніть кнопку ролі користувача, щоб змінити роль користувача.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Відмовлено в дозволі на запис до буфера обміну. Будь ласка, перевірте налаштування вашого браузера, щоб надати необхідний доступ.", "Clone": "Клонувати", "Clone Chat": "Клонувати чат", "Clone of {{TITLE}}": "Клон {{TITLE}}", "Close": "Закрити", "Close Banner": "", "Close Configure Connection Modal": "", "Close modal": "", "Close settings modal": "", "Close Sidebar": "", "CMU ARCTIC speaker embedding name": "", "Code Block": "", "Code execution": "Виконання коду", "Code Execution": "Виконання коду", "Code Execution Engine": "Ру<PERSON><PERSON>й виконання коду", "Code Execution Timeout": "Тайм-аут виконання коду", "Code formatted successfully": "Код успішно відформатовано", "Code Interpreter": "Інтерпретатор коду", "Code Interpreter Engine": "Двигун інтерпретатора коду", "Code Interpreter Prompt Template": "Шаблон запиту інтерпретатора коду", "Collapse": "Згорнути", "Collection": "Колекція", "Color": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ComfyUI": "ComfyUI", "ComfyUI API Key": "ComfyUI API ключ", "ComfyUI Base URL": "URL-адреса ComfyUI", "ComfyUI Base URL is required.": "Необхідно вказати URL-адресу ComfyUI.", "ComfyUI Workflow": "ComfyUI Workflow", "ComfyUI Workflow Nodes": "Вузли Workflow в ComfyUI", "Comma separated Node Ids (e.g. 1 or 1,2)": "", "Command": "Команда", "Comment": "", "Completions": "Завершення", "Compress Images in Channels": "", "Concurrent Requests": "Одночасні запити", "Config imported successfully": "", "Configure": "Налаштувати", "Confirm": "Підтвердити", "Confirm Password": "Підтвердіть пароль", "Confirm your action": "Підтвердіть свою дію", "Confirm your new password": "Підтвердіть свій новий пароль", "Confirm Your Password": "", "Connect to your own OpenAI compatible API endpoints.": "Підключіться до своїх власних API-ендпоінтів, сумісних з OpenAI.", "Connect to your own OpenAPI compatible external tool servers.": "Підключіться до своїх власних зовнішніх серверів інструментів, сумісних з OpenAPI.", "Connection failed": "", "Connection successful": "", "Connection Type": "", "Connections": "З'єднання", "Connections saved successfully": "", "Connections settings updated": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "Обмежує зусилля на міркування для моделей міркування. Діє лише для моделей міркування від конкретних постачальників, які підтримують зусилля міркування.", "Contact Admin for WebUI Access": "Зверніться до адміна для отримання доступу до WebUI", "Content": "Зміст", "Content Extraction Engine": "Ру<PERSON><PERSON>й вилучення контенту", "Continue Response": "Продовжити відповідь", "Continue with {{provider}}": "Продовжити з {{provider}}", "Continue with Email": "Продовжити з електронною поштою", "Continue with LDAP": "Продовжити з LDAP", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Керування розбиттям тексту повідомлення для TTS-запитів. 'Punctuation' розбиває на речення, 'paragraphs' розбиває на абзаци, а 'none' залишає повідомлення як один рядок.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "Контролює повторення послідовностей токенів у згенерованому тексті. Вищий показник (напр., 1.5) сильніше штрафує за повторення, тоді як нижчий показник (напр., 1.1) буде більш м'яким. При значенні 1 ця опція вимкнена.", "Controls": "Керування", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "Контролює баланс між узгодженістю та різноманітністю результату. Нижчий показник призведе до більш зосередженого та узгодженого тексту.", "Conversation saved successfully": "", "Copied": "Скопійовано", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "Скопійовано URL-адресу спільного чату в буфер обміну!", "Copied to clipboard": "Скопійовано в буфер обміну", "Copy": "Копіювати", "Copy Formatted Text": "", "Copy last code block": "Копіювати останній блок коду", "Copy last response": "Копіювати останню відповідь", "Copy link": "", "Copy Link": "Копіювати посилання", "Copy to clipboard": "Копіювати в буфер обміну", "Copying to clipboard was successful!": "Копіювання в буфер обміну виконано успішно!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "CORS має бути правильно налаштований постачальником, щоб дозволити запити з Open WebUI.", "Create": "Створити", "Create a knowledge base": "Створити базу знань", "Create a model": "Створити модель", "Create Account": "Створити обліковий запис", "Create Admin Account": "Створити обліковий запис адміністратора", "Create Channel": "Створити канал", "Create Folder": "", "Create Group": "Створити групу", "Create Knowledge": "Створити знання", "Create new key": "Створити новий ключ", "Create new secret key": "Створити новий секретний ключ", "Create Note": "", "Create your first note by clicking on the plus button below.": "", "Created at": "Створено у", "Created At": "Створено у", "Created by": "Створено", "CSV Import": "Імпорт CSV", "Ctrl+Enter to Send": "Ctrl+Enter для відправки", "Current Model": "Поточна модель", "Current Password": "Поточний пароль", "Custom": "Налаштувати", "Custom description enabled": "", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "Зона небезпеки", "Dark": "Темна", "Database": "База даних", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "DD/MM/YYYY": "", "December": "Гру<PERSON><PERSON><PERSON>ь", "Deepgram": "", "Default": "За замовчуванням", "Default (Open AI)": "За замовчуванням (Open AI)", "Default (SentenceTransformers)": "За замовчуванням (SentenceTransformers)", "Default action buttons will be used.": "", "Default description enabled": "", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "Режим за замовчуванням працює з ширшим діапазоном моделей, викликаючи інструменти один раз перед виконанням. Рідний режим використовує вбудовані можливості виклику інструментів моделі, але вимагає, щоб модель спочатку підтримувала цю функцію.", "Default Model": "Модель за замовчуванням", "Default model updated": "Модель за замовчуванням оновлено", "Default Models": "Моделі за замовчуванням", "Default permissions": "Дозволи за замовчуванням", "Default permissions updated successfully": "Дозволи за замовчуванням успішно оновлено", "Default Prompt Suggestions": "Пропозиції промтів замовчуванням", "Default to 389 or 636 if TLS is enabled": "За замовчуванням використовується 389 або 636, якщо TLS увімкнено.", "Default to ALL": "За замовчуванням — УСІ.", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "За замовчуванням використовувати сегментований пошук для зосередженого та релевантного вилучення контенту, це рекомендується у більшості випадків.", "Default User Role": "Роль користувача за замовчуванням", "Delete": "Видалити", "Delete a model": "Видалити модель", "Delete All Chats": "Видалити усі чати", "Delete All Models": "Видалити усі моделі", "Delete chat": "Видалити чат", "Delete Chat": "Видалити чат", "Delete chat?": "Видалити чат?", "Delete folder?": "Видалити папку?", "Delete function?": "Видалити функцію?", "Delete Message": "Видалити повідомлення", "Delete message?": "Видалити повідомлення?", "Delete note?": "", "Delete prompt?": "Видалити промт?", "delete this link": "видалити це посилання", "Delete tool?": "Видалити інструмент?", "Delete User": "Видалити користувача", "Deleted {{deleteModelTag}}": "Видалено {{deleteModelTag}}", "Deleted {{name}}": "Видалено {{name}}", "Deleted User": "Видалений користувач", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "Опишіть вашу базу знань та цілі", "Description": "<PERSON><PERSON><PERSON><PERSON>", "Detect Artifacts Automatically": "", "Dictate": "", "Didn't fully follow instructions": "Не повністю дотримувалися інструкцій", "Direct": "Пря<PERSON><PERSON>", "Direct Connections": "Прямі з'єднання", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "Прямі з'єднання дозволяють користувачам підключатися до своїх власних API-кінцевих точок, сумісних з OpenAI.", "Direct Tool Servers": "", "Directory selection was cancelled": "", "Disable Code Interpreter": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "Вимкнено", "Discover a function": "Знайдіть функцію", "Discover a model": "Знайдіть модель", "Discover a prompt": "Знайдіть промт", "Discover a tool": "Знайдіть інструмент", "Discover how to use Open WebUI and seek support from the community.": "Діз<PERSON><PERSON><PERSON>есь, як використовувати Open WebUI, та звертайтесь за підтримкою до спільноти.", "Discover wonders": "Відкривайте чудеса", "Discover, download, and explore custom functions": "Знайдіть, завантажте та досліджуйте налаштовані функції", "Discover, download, and explore custom prompts": "Знайдіть, завантажте та досліджуйте налаштовані промти", "Discover, download, and explore custom tools": "Знайдіть, завантажте та досліджуйте налаштовані інструменти", "Discover, download, and explore model presets": "Знайдіть, завантажте та досліджуйте налаштування моделей", "Display": "Відображення", "Display Emoji in Call": "Відображати емодзі у викликах", "Display Multi-model Responses in Tabs": "", "Display the username instead of You in the Chat": "Показувати ім'я користувача замість 'Ви' в чаті", "Displays citations in the response": "Показує посилання у відповіді", "Dive into knowledge": "Зануртесь у знання", "Do not install functions from sources you do not fully trust.": "Не встановлюйте функції з джерел, яким ви не повністю довіряєте.", "Do not install tools from sources you do not fully trust.": "Не встановлюйте інструменти з джерел, яким ви не повністю довіряєте.", "Docling": "<PERSON><PERSON>", "Docling Server URL required.": "Потрібна URL-адреса сервер<PERSON>.", "Document": "Документ", "Document Intelligence": "Інтелект документа", "Document Intelligence endpoint required.": "", "Documentation": "Документація", "Documents": "Документи", "does not make any external connections, and your data stays securely on your locally hosted server.": "не встановлює жодних зовнішніх з'єднань, і ваші дані залишаються в безпеці на вашому локальному сервері.", "Domain Filter List": "Список фільтрів доменів", "don't fetch random pipelines from sources you don't trust.": "Не отримуйте випадкові pipelines із джерел, яким ви не довіряєте.", "Don't have an account?": "Немає облікового запису?", "don't install random functions from sources you don't trust.": "не встановлюйте випадкові функції з джерел, яким ви не довіряєте.", "don't install random tools from sources you don't trust.": "не встановлюйте випадкові інструменти з джерел, яким ви не довіряєте.", "Don't like the style": "Не подобається стиль", "Done": "Готово", "Download": "Завант<PERSON><PERSON><PERSON>ти", "Download & Delete": "Завантажити та видалити", "Download as SVG": "Завантажити як SVG", "Download canceled": "Завантаження скасовано", "Download Database": "Завантажити базу даних", "Drag and drop a file to upload or select a file to view": "Перетягніть файл для завантаження або виберіть файл для перегляду", "Draw": "Малювати", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "напр., '30s','10m'. Дійсні одиниці часу: 'с', 'хв', 'г'.", "e.g. \"json\" or a JSON schema": "напр., \"json\" або схема JSON", "e.g. 60": "напр. 60", "e.g. A filter to remove profanity from text": "напр., фільтр для видалення нецензурної лексики з тексту", "e.g. en": "", "e.g. My Filter": "напр., <PERSON><PERSON><PERSON> фільтр", "e.g. My Tools": "напр., Мої інструменти", "e.g. my_filter": "напр., my_filter", "e.g. my_tools": "напр., my_tools", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "напр., Інструменти для виконання різних операцій", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "e.g., westus (leave blank for eastus)": "", "Edit": "Редагувати", "Edit Arena Model": "Редагувати модель Arena", "Edit Channel": "Редагувати канал", "Edit Connection": "Редагувати з'єднання", "Edit Default Permissions": "Редагувати дозволи за замовчуванням", "Edit Folder": "", "Edit Memory": "Редагувати пам'ять", "Edit User": "Редагувати користувача", "Edit User Group": "Редагувати групу користувачів", "Edited": "", "Editing": "", "Eject": "", "ElevenLabs": "ElevenLabs", "Email": "Ел. пошта", "Embark on adventures": "Вирушайте в пригоди", "Embedding": "Вбудовування", "Embedding Batch Size": "Розмір пакету під час вбудовування", "Embedding Model": "Модель вбудовування", "Embedding Model Engine": "Ру<PERSON><PERSON>й моделі вбудовування ", "Embedding model set to \"{{embedding_model}}\"": "Встановлена модель вбудовування \"{{embedding_model}}\"", "Enable API Key": "Увімкнути ключ API", "Enable autocomplete generation for chat messages": "Увімкнути генерацію автозаповнення для повідомлень чату", "Enable Code Execution": "Увімкнути виконання коду", "Enable Code Interpreter": "Увімкнути інтерпретатор коду", "Enable Community Sharing": "Увімкнути спільний доступ", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Увімкнути блокування пам'яті (mlock), щоб запобігти виведенню даних моделі з оперативної пам'яті. Цей параметр блокує робочий набір сторінок моделі в оперативній пам'яті, гарантуючи, що вони не будуть виведені на диск. Це може допомогти підтримувати продуктивність, уникати помилок сторінок та забезпечувати швидкий доступ до даних.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Увімкнути відображення пам'яті (mmap) для завантаження даних моделі. Цей параметр дозволяє системі використовувати дискове сховище як розширення оперативної пам'яті, трактуючи файли на диску, як ніби вони знаходяться в RAM. Це може покращити продуктивність моделі, дозволяючи швидший доступ до даних. Однак, він може не працювати коректно на всіх системах і може споживати значну кількість дискового простору.", "Enable Message Rating": "Увімкнути оцінку повідомлень", "Enable Mirostat sampling for controlling perplexity.": "Увімкнути вибірку Mirostat для контролю перплексії.", "Enable New Sign Ups": "Дозволити нові реєстрації", "Enable, disable, or customize the reasoning tags used by the model. \"Enabled\" uses default tags, \"Disabled\" turns off reasoning tags, and \"Custom\" lets you specify your own start and end tags.": "", "Enabled": "Увімкнено", "End Tag": "", "Endpoint URL": "", "Enforce Temporary Chat": "Застосувати тимчасовий чат", "Enhance": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Переконайтеся, що ваш CSV-файл містить 4 колонки в такому порядку: Ім'я, <PERSON>ail, Пароль, Роль.", "Enter {{role}} message here": "Введіть повідомлення {{role}} тут", "Enter a detail about yourself for your LLMs to recall": "Введіть відомості про себе для запам'ятовування вашими LLM.", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "Введіть рядок авторизації api (напр, ім'я користувача:пароль)", "Enter Application DN": "Введіть DN застосунку", "Enter Application DN Password": "Введіть пароль DN застосунку", "Enter Bing Search V7 Endpoint": "Введіть точку доступу Bing Search V7", "Enter Bing Search V7 Subscription Key": "Введіть ключ підписки Bing Search V7", "Enter Bocha Search API Key": "Введіть ключ API Bocha Search", "Enter Brave Search API Key": "Введіть ключ API для пошуку Brave", "Enter certificate path": "Введіть шлях до сертифіката", "Enter CFG Scale (e.g. 7.0)": "Введіть масштаб CFG (напр., 7.0)", "Enter Chunk Overlap": "Введіть перекриття фрагменту", "Enter Chunk Size": "Введіть розмір фрагменту", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "Введіть пари \"токен:значення_зміщення\", розділені комами (напр.: 5432:100, 413:-100)", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter coordinates (e.g. 51.505, -0.09)": "", "Enter Datalab Marker API Base URL": "", "Enter Datalab Marker API Key": "", "Enter description": "Введіть опис", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "Введіть URL-адресу сервер<PERSON>", "Enter Document Intelligence Endpoint": "Введіть кінцеву точку Інтелекту документа", "Enter Document Intelligence Key": "Введіть ключ Інтелекту документа", "Enter domains separated by commas (e.g., example.com,site.org)": "Введіть домени, розділені комами (наприклад, example.com, site.org)", "Enter Exa API Key": "Введіть ключ API Exa", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter folder name": "", "Enter Github Raw URL": "Введіть Raw URL-адре<PERSON><PERSON> Github", "Enter Google PSE API Key": "Введіть ключ API Google PSE", "Enter Google PSE Engine Id": "Введіть Google PSE Engine Id", "Enter hex color (e.g. #FF0000)": "", "Enter ID": "", "Enter Image Size (e.g. 512x512)": "Введіть розмір зображення (напр., 512x512)", "Enter Jina API Key": "Введіть ключ API Jina", "Enter JSON config (e.g., {\"disable_links\": true})": "", "Enter Jupyter Password": "Введіть пароль Jupyter", "Enter Jupyter Token": "Введіть токе<PERSON>", "Enter Jupyter URL": "Введіть URL Jupyter", "Enter Kagi Search API Key": "Введіть ключ API Kagi Search", "Enter Key Behavior": "Введіть поведінку клавіші", "Enter language codes": "Введіть мовні коди", "Enter Mistral API Key": "", "Enter Model ID": "Введіть ID моделі", "Enter model tag (e.g. {{modelTag}})": "Введіть тег моделі (напр., {{modelTag}})", "Enter Mojeek Search API Key": "Введіть API ключ для пошуку Mojeek", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "Введіть кількість кроків (напр., 50)", "Enter Perplexity API Key": "Введіть ключ API для Perplexity", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "Введіть URL проксі (напр., **************************:port)", "Enter reasoning effort": "Введіть зусилля на міркування", "Enter Sampler (e.g. Euler a)": "Введіть семплер (напр., Euler a)", "Enter Scheduler (e.g. Karras)": "Введіть планувальник (напр., Karras)", "Enter Score": "Введіть бал", "Enter SearchApi API Key": "Введіть ключ API для SearchApi", "Enter SearchApi Engine": "Введіть SearchApi рушія", "Enter Searxng Query URL": "Введіть URL-адресу запиту Searxng", "Enter Seed": "Введіть насіння", "Enter SerpApi API Key": "Введіть ключ API для SerpApi", "Enter SerpApi Engine": "Введіть рушій SerpApi", "Enter Serper API Key": "Введіть ключ API Serper", "Enter Serply API Key": "Введіть ключ API Serply", "Enter Serpstack API Key": "Введіть ключ API Serpstack", "Enter server host": "Введіть хост сервера", "Enter server label": "Введіть мітку сервера", "Enter server port": "Введіть порт сервера", "Enter Sougou Search API sID": "", "Enter Sougou Search API SK": "", "Enter stop sequence": "Введіть символ зупинки", "Enter system prompt": "Введіть системний промт", "Enter system prompt here": "", "Enter Tavily API Key": "Введіть ключ <PERSON>ly", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "Введіть публічний URL вашого WebUI. Цей URL буде використовуватися для генерування посилань у сповіщеннях.", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "Введіть URL-адресу сервера Tika", "Enter timeout in seconds": "Введіть тайм-аут у секундах", "Enter to Send": "Введіть для відправки", "Enter Top K": "Введіть Top K", "Enter Top K Reranker": "Введіть Top K Реранкер", "Enter URL (e.g. http://127.0.0.1:7860/)": "Введіть URL-адресу (напр., http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Введіть URL-адресу (напр., http://localhost:11434)", "Enter value": "", "Enter value (true/false)": "", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your code here...": "Введіть свій код тут...", "Enter your current password": "Введіть ваш поточний пароль", "Enter Your Email": "Введіть вашу ел. пошту", "Enter Your Full Name": "Введіть ваше ім'я", "Enter your gender": "", "Enter your message": "Введіть повідомлення ", "Enter your name": "", "Enter Your Name": "", "Enter your new password": "Введіть ваш новий пароль", "Enter Your Password": "Введіть ваш пароль", "Enter Your Role": "Введіть вашу роль", "Enter Your Username": "Введіть своє ім'я користувача", "Enter your webhook URL": "Введіть URL вашого вебхука", "Error": "Помилка", "ERROR": "ПОМИЛКА", "Error accessing directory": "", "Error accessing Google Drive: {{error}}": "Помилка доступу до Google Drive: {{error}}", "Error accessing media devices.": "", "Error starting recording.": "", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "Помилка завантаження файлу: {{error}}", "Error: A model with the ID '{{modelId}}' already exists. Please select a different ID to proceed.": "", "Error: Model ID cannot be empty. Please enter a valid ID to proceed.": "", "Evaluations": "Оцінювання", "Everyone": "", "Exa API Key": "Exa API ключ", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Приклад: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Приклад: У<PERSON>І", "Example: mail": "Приклад: пошта", "Example: ou=users,dc=foo,dc=example": "Приклад: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "Приклад: sAMAccountName або uid або userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "Перевищено кількість місць у вашій ліцензії. Будь ласка, зверніться до підтримки для збільшення кількості місць.", "Exclude": "Виключити", "Execute code for analysis": "Виконати код для аналізу", "Executing **{{NAME}}**...": "", "Expand": "Розгорнути", "Experimental": "Експериментальне", "Explain": "Пояснити", "Explore the cosmos": "Досліджуйте космос", "Export": "Експорт", "Export All Archived Chats": "Експорт всіх архівованих чатів", "Export All Chats (All Users)": "Експорт всіх чатів (всіх користувачів)", "Export chat (.json)": "Експорт чату (.json)", "Export Chats": "Експорт чатів", "Export Config to JSON File": "Експорт конфігурації у файл JSON", "Export Functions": "Експорт функцій ", "Export Models": "Експорт моделей", "Export Presets": "Експорт пресетів", "Export Prompt Suggestions": "", "Export Prompts": "Експорт промтів", "Export to CSV": "Експорт в CSV", "Export Tools": "Експорт інструментів", "Export Users": "", "External": "Зовнішній", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "", "External Web Loader URL": "", "External Web Search API Key": "", "External Web Search URL": "", "Fade Effect for Streaming Text": "", "Failed to add file.": "Не вдалося додати файл.", "Failed to connect to {{URL}} OpenAPI tool server": "Не вдалося підключитися до серверу інструментів OpenAPI {{URL}}", "Failed to copy link": "", "Failed to create API Key.": "Не вдалося створити API ключ.", "Failed to delete note": "", "Failed to extract content from the file: {{error}}": "", "Failed to extract content from the file.": "", "Failed to fetch models": "Не вдалося отримати моделі", "Failed to generate title": "", "Failed to load chat preview": "", "Failed to load file content.": "", "Failed to move chat": "", "Failed to read clipboard contents": "Не вдалося прочитати вміст буфера обміну", "Failed to save connections": "", "Failed to save conversation": "Не вдалося зберегти розмову", "Failed to save models configuration": "Не вдалося зберегти конфігурацію моделей", "Failed to update settings": "Не вдалося оновити налаштування", "Failed to upload file.": "Не вдалося завантажити файл.", "Features": "Особливості", "Features Permissions": "Дозволи функцій", "February": "Лю<PERSON><PERSON>", "Feedback Details": "", "Feedback History": "Історія відгуків", "Feedbacks": "Відгуки", "Feel free to add specific details": "Не соромтеся додавати конкретні деталі", "Female": "", "File": "<PERSON>а<PERSON><PERSON>", "File added successfully.": "Файл успішно додано.", "File content updated successfully.": "Вміст файлу успішно оновлено.", "File Mode": "Файловий режим", "File not found.": "Файл не знайдено.", "File removed successfully.": "Файл успішно видалено.", "File size should not exceed {{maxSize}} MB.": "Розмір файлу не повинен перевищувати {{maxSize}} МБ.", "File Upload": "", "File uploaded successfully": "Файл успішно завантажено", "Files": "Файли", "Filter": "", "Filter is now globally disabled": "Фільтр глобально вимкнено", "Filter is now globally enabled": "Фільтр увімкнено глобально", "Filters": "Фільтри", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Виявлено підробку відбитків: Неможливо використовувати ініціали як аватар. Повернення до зображення профілю за замовчуванням.", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Floating Quick Actions": "", "Focus chat input": "Фокус вводу чату", "Folder deleted successfully": "Папку успішно видалено", "Folder Name": "", "Folder name cannot be empty.": "Назва папки не може бути порожньою.", "Folder name updated successfully": "Назву папки успішно оновлено", "Folder updated successfully": "", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "Бездоганно дотримувався інструкцій", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "Прокладайте нові шляхи", "Form": "Форма", "Format Lines": "", "Format the lines in the output. Defaults to False. If set to True, the lines will be formatted to detect inline math and styles.": "", "Format your variables using brackets like this:": "Форматуйте свої змінні, використовуючи фігурні дужки таким чином:", "Formatting may be inconsistent from source.": "", "Forwards system user session credentials to authenticate": "", "Full Context Mode": "Режим повного контексту", "Function": "Функція", "Function Calling": "Виклик функцій", "Function created successfully": "Функцію успішно створено", "Function deleted successfully": "Функцію успішно видалено", "Function Description": "<PERSON><PERSON><PERSON><PERSON> функції", "Function ID": "ID функції", "Function imported successfully": "", "Function is now globally disabled": "Функція зараз глобально вимкнена", "Function is now globally enabled": "Функція зараз глобально увімкнена ", "Function Name": "Назва функції", "Function updated successfully": "Функцію успішно оновлено", "Functions": "Функції", "Functions allow arbitrary code execution.": "Функції дозволяють виконання довільного коду.", "Functions imported successfully": "Функції успішно імпортовано", "Gemini": "Gemini", "Gemini API Config": "Конфігурація Gemini API", "Gemini API Key is required.": "Потр<PERSON>б<PERSON>н ключ API Gemini.", "Gender": "", "General": "Загальні", "Generate": "", "Generate an image": "Згенерувати зображення", "Generate Image": "Створити зображення", "Generate prompt pair": "Згенерувати пару промтів", "Generating search query": "Сформувати пошуковий запит", "Generating...": "", "Get information on {{name}} in the UI": "", "Get started": "Почати", "Get started with {{WEBUI_NAME}}": "Почати з {{WEBUI_NAME}}", "Global": "Глоб.", "Good Response": "<PERSON>а<PERSON><PERSON> відповідь", "Google Drive": "Google Drive", "Google PSE API Key": "Ключ API Google PSE", "Google PSE Engine Id": "Id рушія Google PSE", "Gravatar": "", "Group": "Гру<PERSON>а", "Group created successfully": "Групу успішно створено", "Group deleted successfully": "Групу успішно видалено", "Group Description": "О<PERSON><PERSON>с групи", "Group Name": "Назва групи", "Group updated successfully": "Групу успішно оновлено", "Groups": "Гру<PERSON>и", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "Тактильний зворотній зв'язок", "Height": "", "Hello, {{name}}": "Привіт, {{name}}", "Help": "Допоможіть", "Help us create the best community leaderboard by sharing your feedback history!": "Допоможіть нам створити найкращу таблицю лідерів спільноти, поділившись історією своїх відгуків!", "Hex Color": "Шістнадцятковий колір", "Hex Color - Leave empty for default color": "Шістнадцятковий колір — залиште порожнім для кольору за замовчуванням", "Hide": "Приховати", "Hide from Sidebar": "", "Hide Model": "Приховати модель", "High": "", "High Contrast Mode": "", "Home": "Головна", "Host": "Хо<PERSON>т", "How can I help you today?": "Чим я можу допомогти вам сьогодні?", "How would you rate this response?": "Як би ви оцінили цю відповідь?", "HTML": "", "Hybrid Search": "Гібридний пошук", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Я підтверджую, що прочитав і розумію наслідки своїх дій. Я усвідомлюю ризики, пов'язані з виконанням довільного коду, і перевірив надійність джерела.", "ID": "ID", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "Запаліть цікавість", "Image": "Зображення", "Image Compression": "Стиснення зображень", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "Генерація зображень", "Image Generation (Experimental)": "Генерування зображень (експериментально)", "Image Generation Engine": "Механізм генерації зображень", "Image Max Compression Size": "Максимальний розмір стиснення зображення", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "Генерація підказок для зображень", "Image Prompt Generation Prompt": "Підказка для генерації зображень", "Image Settings": "Налаштування зображення", "Images": "Зображення", "Import": "", "Import Chats": "Імпорт чатів", "Import Config from JSON File": "Імпорт конфігурації з файлу JSON", "Import From Link": "", "Import Functions": "Імпорт функцій ", "Import Models": "Імпорт моделей", "Import Notes": "", "Import Presets": "Імпорт пресетів", "Import Prompt Suggestions": "", "Import Prompts": "Імпорт промтів", "Import Tools": "Імпорт інструментів", "Important Update": "Важливе оновлення", "Include": "Включити", "Include `--api-auth` flag when running stable-diffusion-webui": "Включіть прапорець `--api-auth` під час запуску stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "Включіть прапор `--api` при запуску stable-diffusion-webui", "Includes SharePoint": "Містить SharePoint", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "Впливає на те, як швидко алгоритм реагує на відгуки згенерованого тексту. Нижча швидкість навчання призведе до повільніших коригувань, тоді як вища швидкість навчання зробить алгоритм більш чутливим.", "Info": "Інфо", "Initials": "", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "Вставте весь вміст як контекст для всебічної обробки, це рекомендується для складних запитів.", "Input": "", "Input commands": "Команди вводу", "Input Key (e.g. text, unet_name, steps)": "", "Input Variables": "", "Insert": "", "Insert Follow-Up Prompt to Input": "", "Insert Prompt as Rich Text": "", "Install from Github URL": "Встановіть з URL-адре<PERSON><PERSON>ub", "Instant Auto-Send After Voice Transcription": "Миттєва автома<PERSON>ична відправка після транскрипції голосу", "Integration": "Інтеграція", "Interface": "Інтерфейс", "Invalid file content": "", "Invalid file format.": "Неправильний формат файлу.", "Invalid JSON file": "", "Invalid JSON format for ComfyUI Workflow.": "", "Invalid JSON format in Additional Config": "", "Invalid Tag": "Недійсний тег", "is typing...": "друкує...", "Italic": "", "January": "Січень", "Jina API Key": "Ключ API для Jina", "join our Discord for help.": "приєднуйтеся до нашого Discord для допомоги.", "JSON": "JSON", "JSON Preview": "Перегляд JSON", "July": "Липень", "June": "Червень", "Jupyter Auth": "Аутентифікація Jupyter", "Jupyter URL": "Jupyter URL", "JWT Expiration": "Термін дії JWT", "JWT Token": "Токен JWT", "Kagi Search API Key": "Kagi Search API ключ", "Keep Follow-Up Prompts in Chat": "", "Keep in Sidebar": "", "Key": "<PERSON><PERSON><PERSON><PERSON>", "Key is required": "", "Keyboard shortcuts": "Клавіатурні скорочення", "Knowledge": "Знання", "Knowledge Access": "Доступ до знань", "Knowledge Base": "", "Knowledge created successfully.": "Знання успішно створено.", "Knowledge deleted successfully.": "Знання успішно видалено.", "Knowledge Description": "", "Knowledge Name": "", "Knowledge Public Sharing": "Публічний обмін знаннями", "Knowledge reset successfully.": "Знання успішно скинуто.", "Knowledge updated successfully": "Знання успішно оновлено", "Kokoro.js (Browser)": "Kokoro.js (Браузер)", "Kokoro.js Dtype": "Kokoro.js Dtype", "Label": "Мітка", "Landing Page Mode": "Режим головної сторінки", "Language": "Мова", "Language Locales": "", "Last Active": "Остання активність", "Last Modified": "Востаннє змінено", "Last reply": "Остання відповідь", "LDAP": "LDAP", "LDAP server updated": "Сервер LDAP оновлено", "Leaderboard": "Таблиця лідерів", "Learn More": "", "Learn more about OpenAPI tool servers.": "", "Leave empty for no compression": "", "Leave empty for unlimited": "Залиште порожнім для необмеженого розміру", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "Залиште порожнім, щоб включити всі моделі з кінцевої точки \"{{url}}/api/tags\"", "Leave empty to include all models from \"{{url}}/models\" endpoint": "Залиште порожнім, щоб включити всі моделі з кінцевої точки \"{{url}}/models\"", "Leave empty to include all models or select specific models": "Залиште порожнім, щоб включити усі моделі, або виберіть конкретні моделі.", "Leave empty to use the default prompt, or enter a custom prompt": "Залиште порожнім для використання стандартного запиту, або введіть власний запит", "Leave model field empty to use the default model.": "Залиште поле моделі порожнім, щоб використовувати модель за замовчуванням.", "lexical": "", "License": "Ліцензія", "Lift List": "", "Light": "Світла", "Listening...": "Слухаю...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "LLMs можуть помилятися. Перевірте важливу інформацію.", "Loader": "Зава<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ч", "Loading Kokoro.js...": "Завантаження Kokoro.js...", "Loading...": "Завантаження...", "Local": "Локальний", "Local Task Model": "", "Location access not allowed": "Доступ до місцезнаходження не дозволено", "Lost": "Втрачене", "Low": "", "LTR": "LTR", "Made by Open WebUI Community": "Зроблено спільнотою OpenWebUI", "Make password visible in the user interface": "", "Make sure to enclose them with": "Переконайтеся, що вони закриті", "Make sure to export a workflow.json file as API format from ComfyUI.": "Обов'язково експортуйте файл workflow.json у форматі API з ComfyUI.", "Male": "", "Manage": "Керувати", "Manage Direct Connections": "Керування прямими з'єднаннями", "Manage Models": "Керувати моделями", "Manage Ollama": "Керува<PERSON><PERSON>", "Manage Ollama API Connections": "Керувати з'єднаннями Ollama API", "Manage OpenAI API Connections": "Керувати з'єднаннями OpenAI API", "Manage Pipelines": "Керування конвеєрами", "Manage Tool Servers": "Керувати серверами інструментів", "Manage your account information.": "", "March": "Березень", "Markdown": "", "Markdown (Header)": "", "Max Speakers": "", "Max Upload Count": "Макс. кількість завантажень", "Max Upload Size": "Макс. розмір завантаження", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Максимум 3 моделі можна завантажити одночасно. Будь ласка, спробуйте пізніше.", "May": "Травень", "Medium": "", "Memories accessible by LLMs will be shown here.": "Пам'ять, яка доступна LLM, буде показана тут.", "Memory": "Пам'ять", "Memory added successfully": "Пам'ять додано успішно", "Memory cleared successfully": "Пам'ять успішно очищено", "Memory deleted successfully": "Пам'ять успішно видалено", "Memory updated successfully": "Пам'ять успішно оновлено", "Merge Responses": "Об'єднати відповіді", "Merged Response": "Об'єднана відповідь", "Message rating should be enabled to use this feature": "Оцінювання повідомлень має бути увімкнено для використання цієї функції.", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Повідомлення, які ви надішлете після створення посилання, не будуть доступні для інших. Користувачі, які мають URL, зможуть переглядати спільний чат.", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "Модель", "Model '{{modelName}}' has been successfully downloaded.": "Модель '{{modelName}}' успішно завантажено.", "Model '{{modelTag}}' is already in queue for downloading.": "Модель '{{modelTag}}' вже знаходиться в черзі на завантаження.", "Model {{modelId}} not found": "Модель {{modelId}} не знайдено", "Model {{modelName}} is not vision capable": "Модель {{modelName}} не здатна бачити", "Model {{name}} is now {{status}}": "Модель {{name}} тепер має {{status}}", "Model {{name}} is now hidden": "Модель {{name}} тепер схована", "Model {{name}} is now visible": "Модель {{name}} тепер видима", "Model accepts file inputs": "", "Model accepts image inputs": "Модель приймає зображеня", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "Модель створено успішно!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Виявлено шлях до файлової системи моделі. Для оновлення потрібно вказати коротке ім'я моделі, не вдасться продовжити.", "Model Filtering": "Фільтрація моделей", "Model ID": "ID моделі", "Model ID is required.": "", "Model IDs": "ID моделей", "Model Name": "Назва моделі", "Model name already exists, please choose a different one": "", "Model Name is required.": "", "Model not selected": "Модель не вибрана", "Model Params": "Параметри моделі", "Model Permissions": "Дозволи моделей", "Model unloaded successfully": "", "Model updated successfully": "Модель успішно оновлено", "Model(s) do not support file upload": "", "Modelfile Content": "Вміст файлу моделі", "Models": "Моделі", "Models Access": "Доступ до моделей", "Models configuration saved successfully": "Конфігурацію моделей успішно збережено", "Models Public Sharing": "Публічний обмін моделями", "Mojeek Search API Key": "API ключ для пошуку Mojeek", "more": "більше", "More": "Більше", "More Concise": "", "More Options": "", "Move": "", "Name": "Ім'я", "Name and ID are required, please fill them out": "", "Name your knowledge base": "Назвіть вашу базу знань", "Native": "Р<PERSON><PERSON><PERSON><PERSON>", "New Button": "", "New Chat": "<PERSON>овий чат", "New Folder": "Нова папка", "New Function": "", "New Note": "", "New Password": "Новий пароль", "New Tool": "", "new-channel": "новий-канал", "Next message": "", "No chats found": "", "No chats found for this user.": "", "No chats found.": "", "No content": "", "No content found": "Контент не знайдено.", "No content found in file.": "", "No content to speak": "Нема чого говорити", "No conversation to save": "", "No distance available": "Відстань недоступна", "No feedbacks found": "Відгуків не знайдено", "No file selected": "Файл не обрано", "No groups with access, add a group to grant access": "Немає груп з доступом, додайте групу для надання доступу", "No HTML, CSS, or JavaScript content found.": "HTML, CSS або JavaScript контент не знайдено.", "No inference engine with management support found": "Не знайдено двигуна висновків з підтримкою керування", "No knowledge found": "Знання не знайдено.", "No memories to clear": "Немає спогадів для очищення", "No model IDs": "Немає ID моделей", "No models found": "Моделей не знайдено", "No models selected": "Моделі не вибрано", "No Notes": "", "No results": "Не знайдено жодного результату", "No results found": "Не знайдено жодного результату", "No search query generated": "Пошуковий запит не сформовано", "No source available": "Джерело не доступне", "No suggestion prompts": "Немає запропонованих підказок", "No users were found.": "Користувачів не знайдено.", "No valves": "", "No valves to update": "Немає клапанів для оновлення", "Node Ids": "", "None": "Нема", "Not factually correct": "Не відповідає дійсності", "Not helpful": "Не корисно", "Note deleted successfully": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Примітка: Якщо ви встановите мінімальну кількість балів, пошук поверне лише документи з кількістю балів, більшою або рівною мінімальній кількості балів.", "Notes": "Примітки", "Notification Sound": "Звук сповіщення", "Notification Webhook": "Вебхук для сповіщень", "Notifications": "Сповіщення", "November": "Листопад", "OAuth ID": "OAuth ID", "October": "Жовтень", "Off": "Вимк", "Okay, Let's Go!": "Гаразд, давайте почнемо!", "OLED Dark": "Темний OLED", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "Налаштування Ollama API оновлено", "Ollama Version": "Версія Ollama", "On": "Увімк", "OneDrive": "OneDrive", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "Дозволені тільки алфавітно-цифрові символи та дефіси", "Only alphanumeric characters and hyphens are allowed in the command string.": "У рядку команди дозволено використовувати лише алфавітно-цифрові символи та дефіси.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Редагувати можна лише колекції, створіть нову базу знань, щоб редагувати або додавати документи.", "Only markdown files are allowed": "", "Only select users and groups with permission can access": "Тільки вибрані користувачі та групи з дозволом можуть отримати доступ", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Упс! Схоже, що URL-адреса невірна. Будь ласка, перевірте ще раз та спробуйте ще раз.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Упс! Деякі файли все ще завантажуються. Будь ласка, зачекайте, поки завантаження завершиться.", "Oops! There was an error in the previous response.": "Упс! Сталася помилка в попередній відповіді.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Упс! Ви використовуєте непідтримуваний метод (тільки для фронтенду). Будь ласка, обслуговуйте WebUI з бекенду.", "Open file": "Відкрити файл", "Open in full screen": "Відкрити на весь екран", "Open modal to configure connection": "", "Open Modal To Manage Floating Quick Actions": "", "Open new chat": "Відкрити новий чат", "Open Sidebar": "", "Open User Profile Menu": "", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "Open WebUI використовує faster-whisper внутрішньо.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI використовує вбудовування голосів SpeechT5 та CMU Arctic.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Open WebUI версія (v{{OPEN_WEBUI_VERSION}}) нижча за необхідну версію (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "Конфігурація OpenAI API", "OpenAI API Key is required.": "Потрібен ключ OpenAI API.", "OpenAI API settings updated": "Налаштування OpenAI API оновлено", "OpenAI URL/Key required.": "Потрібен OpenAI URL/ключ.", "openapi.json URL or Path": "", "Optional": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "або", "Ordered List": "", "Organize your users": "Організуйте своїх користувачів", "Other": "Інше", "OUTPUT": "ВИХІД", "Output format": "Формат відповіді", "Output Format": "", "Overview": "Огляд", "page": "сторінка", "Paginate": "", "Parameters": "", "Password": "Пароль", "Passwords do not match.": "", "Paste Large Text as File": "Вставити великий текст як файл", "PDF document (.pdf)": "PDF документ (.pdf)", "PDF Extract Images (OCR)": "Розпізнавання зображень з PDF (OCR)", "pending": "на розгляді", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "Відмовлено в доступі до медіапристроїв", "Permission denied when accessing microphone": "Відмовлено у доступі до мікрофона", "Permission denied when accessing microphone: {{error}}": "Доступ до мікрофона заборонено: {{error}}", "Permissions": "Дозволи", "Perplexity API Key": "Ключ API для Perplexity", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "Персоналізація", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "Зачепити", "Pinned": "Зачеплено", "Pioneer insights": "Прокладайте нові шляхи до знань", "Pipe": "", "Pipeline deleted successfully": "Конвеєр успішно видалено", "Pipeline downloaded successfully": "Конвеєр успішно завантажено", "Pipelines": "Конвеєри", "Pipelines are a plugin system with arbitrary code execution —": "Pipelines — це система плагінів із довільним виконанням коду —", "Pipelines Not Detected": "Конвеєрів не знайдено", "Pipelines Valves": "Клапани конвеєрів", "Plain text (.md)": "", "Plain text (.txt)": "Простий текст (.txt)", "Playground": "Май<PERSON><PERSON><PERSON><PERSON>ик", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "Будь ласка, уважно ознайомтеся з наступними попередженнями:", "Please do not close the settings page while loading the model.": "Будь ласка, не закривайте сторінку налаштувань під час завантаження моделі.", "Please enter a message or attach a file.": "", "Please enter a prompt": "Будь ласка, введіть підказку", "Please enter a valid path": "", "Please enter a valid URL": "", "Please fill in all fields.": "Будь ласка, заповніть усі поля.", "Please select a model first.": "Будь ласка, спочатку виберіть модель.", "Please select a model.": "Будь ласка, виберіть модель.", "Please select a reason": "Будь ласка, виберіть причину", "Please wait until all files are uploaded.": "", "Port": "Порт", "Positive attitude": "Позитивне ставлення", "Prefer not to say": "", "Prefix ID": "ID префікса", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "ID префікса використовується для уникнення конфліктів з іншими підключеннями шляхом додавання префікса до ID моделей — залиште порожнім, щоб вимкнути", "Prevent file creation": "", "Preview": "", "Previous 30 days": "Попередні 30 днів", "Previous 7 days": "Попередні 7 днів", "Previous message": "", "Private": "Приватний", "Profile": "Профіль", "Prompt": "Підказка", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Підказка (напр., розкажіть мені цікавий факт про Римську імперію)", "Prompt Autocompletion": "Автозавершення підказок", "Prompt Content": "Зміст промту", "Prompt created successfully": "Підказку успішно створено", "Prompt suggestions": "Швидкі промти", "Prompt updated successfully": "Підказку успішно оновлено", "Prompts": "Промти", "Prompts Access": "Доступ до підказок", "Prompts Public Sharing": "Публічний обмін промтами", "Public": "Публічний", "Pull \"{{searchValue}}\" from Ollama.com": "Завантажити \"{{searchValue}}\" з Ollama.com", "Pull a model from Ollama.com": "Завантажити модель з Ollama.com", "Query Generation Prompt": "Підказка для генерації запиту", "Quick Actions": "", "RAG Template": "Шаблон RAG", "Rating": "Оцінка", "Re-rank models by topic similarity": "Перестановка моделей за схожістю тем", "Read": "Читати", "Read Aloud": "Читати вголос", "Reason": "", "Reasoning Effort": "Зусилля на міркування", "Reasoning Tags": "", "Record": "", "Record voice": "Записати голос", "Redirecting you to Open WebUI Community": "Перенаправляємо вас до спільноти OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "Зменшує ймовірність генерування нісенітниць. Вищий показник (напр., 100) забезпечить більше різноманітних відповідей, тоді як нижчий показник (напр., 10) буде більш обережним.", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Називайте себе \"Користувач\" (напр., \"Користувач вивчає іспанську мову\")", "References from": "Посилання з", "Refused when it shouldn't have": "Від<PERSON><PERSON>ив, коли не мав би", "Regenerate": "Регенерувати", "Regenerate Menu": "", "Reindex": "", "Reindex Knowledge Base Vectors": "", "Release Notes": "Нотатки до випуску", "Releases": "", "Relevance": "Актуальність", "Relevance Threshold": "", "Remember Dismissal": "", "Remove": "Видалити", "Remove {{MODELID}} from list.": "", "Remove file": "", "Remove File": "", "Remove image": "", "Remove Model": "Видалити модель", "Remove this tag from list": "", "Rename": "Переназвати", "Reorder Models": "Переставити моделі", "Reply in Thread": "Відповісти в потоці", "Reranking Engine": "", "Reranking Model": "Модель переранжування", "Reset": "Скидання", "Reset All Models": "Скинути усі моделі", "Reset Image": "Скинути зображення", "Reset Upload Directory": "Скинути каталог завантажень", "Reset Vector Storage/Knowledge": "Скинути векторне сховище/Знання", "Reset view": "Скинути вигляд", "Response": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Сповіщення про відповіді не можуть бути активовані, оскільки вам було відмовлено в доступі до веб-сайту. Будь ласка, відвідайте налаштування вашого браузера, щоб надати необхідний доступ.", "Response splitting": "Розбиття відповіді", "Response Watermark": "", "Result": "Результат", "RESULT": "Результат", "Retrieval": "По<PERSON><PERSON>к", "Retrieval Query Generation": "Генерація запиту для отримання даних", "Rich Text Input for Chat": "Ввід тексту з форматуванням для чату", "RK": "RK", "Role": "Роль", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "Запустити", "Running": "Виконується", "Running...": "Виконується...", "Save": "Зберегти", "Save & Create": "Зберегти та створити", "Save & Update": "Зберегти та оновити", "Save As Copy": "Зберегти як копію", "Save Chat": "", "Save Tag": "Зберегти тег", "Saved": "Збережено", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Збереження журналів чату безпосередньо в сховище вашого браузера більше не підтримується. Будь ласка, завантажте та видаліть журнали чату, натиснувши кнопку нижче. Не хвилюйтеся, ви можете легко повторно імпортувати журнали чату до бекенду через", "Scroll On Branch Change": "", "Search": "По<PERSON><PERSON>к", "Search a model": "Шукати модель", "Search all emojis": "", "Search Base": "База пошуку", "Search Chats": "Пошук в чатах", "Search Collection": "Шукати колекцію", "Search Filters": "Фільтри пошуку", "search for archived chats": "", "search for folders": "", "search for pinned chats": "", "search for shared chats": "", "search for tags": "шукати теги", "Search Functions": "Пошук функцій", "Search In Models": "", "Search Knowledge": "Шукати знання", "Search Models": "Пошук моделей", "Search Notes": "", "Search options": "Опц<PERSON>ї пошуку", "Search Prompts": "Пошук промтів", "Search Result Count": "Кількість результатів пошуку", "Search the internet": "Шукати в інтернеті", "Search Tools": "Пошуку інструментів", "SearchApi API Key": "Ключ API для SearchApi", "SearchApi Engine": "Руш<PERSON>й SearchApi", "Searched {{count}} sites": "Шукалося {{count}} сайтів", "Searching \"{{searchQuery}}\"": "<PERSON>у<PERSON><PERSON><PERSON> \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "Пошу<PERSON> знань для \"{{searchQuery}}\"", "Searching the web...": "", "Searxng Query URL": "Searxng Query URL", "See readme.md for instructions": "Див. readme.md для інструкцій", "See what's new": "Подивіться, що нового", "Seed": "Сід", "Select": "", "Select a base model": "Обрати базову модель", "Select a base model (e.g. llama3, gpt-4o)": "", "Select a conversation to preview": "", "Select a engine": "Оберіть рушій", "Select a function": "Оберіть функцію", "Select a group": "Вибрати групу", "Select a language": "", "Select a mode": "", "Select a model": "Оберіть модель", "Select a model (optional)": "", "Select a pipeline": "Оберіть конвеєр", "Select a pipeline url": "Оберіть адресу конвеєра", "Select a reranking model engine": "", "Select a role": "", "Select a theme": "", "Select a tool": "Оберіть інструмент", "Select a voice": "", "Select an auth method": "Оберіть метод аутентифікації.", "Select an embedding model engine": "", "Select an engine": "", "Select an Ollama instance": "Виберіть екземп<PERSON>я<PERSON>", "Select an output format": "", "Select dtype": "", "Select Engine": "Виберіть двигун", "Select how to split message text for TTS requests": "", "Select Knowledge": "Вибрати знання", "Select only one model to call": "Оберіть лише одну модель для виклику", "Selected model(s) do not support image inputs": "Вибрані модель(і) не підтримують вхідні зображення", "semantic": "", "Semantic distance to query": "Семан<PERSON><PERSON><PERSON>на відстань до запиту", "Send": "Надіслати", "Send a Message": "Надіслати повідомлення", "Send message": "Надіслати повідомлення", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Відправляє `stream_options: { include_usage: true }` у запиті.\nПідтримувані постачальники повернуть інформацію про використання токену у відповіді, якщо вона встановлена.", "September": "Вересень", "SerpApi API Key": "Ключ API SerpApi", "SerpApi Engine": "<PERSON><PERSON><PERSON><PERSON><PERSON> SerpApi", "Serper API Key": "Ключ API Serper", "Serply API Key": "Ключ API Serply", "Serpstack API Key": "Ключ API Serpstack", "Server connection verified": "З'єднання з сервером підтверджено", "Session": "", "Set as default": "Встановити за замовчуванням", "Set CFG Scale": "Встановити масштаб CFG", "Set Default Model": "Встановити модель за замовчуванням", "Set embedding model": "Встановити модель вбудовування", "Set embedding model (e.g. {{model}})": "Встановити модель вбудовування (напр, {{model}})", "Set Image Size": "Встановити розмір зображення", "Set reranking model (e.g. {{model}})": "Встановити модель переранжування (напр., {{model}})", "Set Sampler": "Встановити семплер", "Set Scheduler": "Встановити планувальник", "Set Steps": "Встановити кроки", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "Встановіть кількість шарів, які будуть передані на графічний процесор (GPU). Збільшення цього значення може значно покращити продуктивність для моделей, оптимізованих для прискорення на GPU, але також може споживати більше енергії та ресурсів GPU.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "Встановити кількість робочих потоків, що використовуються для обробки інформації. Ця опція керує кількістю потоків, що використовуються для обробки надходження запитів одночасно. Збільшення цього значення може підвищити продуктивність при великій одночасності робіт, але також може споживати більше ресурсів CPU.", "Set Voice": "Встановити голос", "Set whisper model": "Встановити модель whisper", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Встановлює фіксоване зміщення проти токенів, які з'явилися хоча б один раз. Вищий показник (напр., 1.5) сильніше штрафує за повторення, тоді як нижчий показник (напр., 0.9) буде більш м'яким. При значенні 0, ця опція вимкнена.", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Встановлює масштабоване зміщення проти токенів для штрафування повторів, залежно від того, скільки разів вони з'являлися. Вищий показник (напр., 1.5) сильніше штрафує за повторення, тоді як нижчий показник (напр., 0.9) буде більш м'яким. При значенні 0, ця опція вимкнена.", "Sets how far back for the model to look back to prevent repetition.": "Встановлює, на скільки кроків назад модель повинна звертати увагу, щоб запобігти повторенням.", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "Встановлює початкове значення випадкового числа, яке використовується для генерації. Встановлення конкретного числа забезпечить однаковий текст для того ж запиту.", "Sets the size of the context window used to generate the next token.": "Встановлює розмір вікна контексту, яке використовується для генерації наступного токена.", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Встановлює послідовності зупинки, які будуть використовуватися. Коли зустрічається така послідовність, LLM припиняє генерацію тексту і повертає результат. Можна встановити кілька послідовностей зупинки, вказавши кілька окремих параметрів зупинки у файлі моделі.", "Settings": "Налаштування", "Settings saved successfully!": "Налаштування успішно збережено!", "Share": "Поділити<PERSON>я", "Share Chat": "Поділитися чатом", "Share to Open WebUI Community": "Поділитися зі спільнотою OpenWebUI", "Share your background and interests": "", "Sharing Permissions": "Дозволи на обмін", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "Показати", "Show \"What's New\" modal on login": "Показати модальне вікно \"Що нового\" під час входу", "Show Admin Details in Account Pending Overlay": "Відобразити дані адміна у вікні очікування облікового запису", "Show All": "", "Show Formatting Toolbar": "", "Show image preview": "", "Show Less": "", "Show Model": "Показати модель", "Show shortcuts": "Показати клавіатурні скорочення", "Show your support!": "Підтримайте нас!", "Showcased creativity": "Продемонстрований креатив", "Sign in": "Увійти", "Sign in to {{WEBUI_NAME}}": "Увійти в {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "Увійти до {{WEBUI_NAME}} за допомогою LDAP", "Sign Out": "Вийти", "Sign up": "Зареєструватися", "Sign up to {{WEBUI_NAME}}": "Зареєструватися в {{WEBUI_NAME}}", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to False.": "", "Signing in to {{WEBUI_NAME}}": "Увійти в {{WEBUI_NAME}}", "Sink List": "", "sk-1234": "sk-1234", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Something went wrong :/": "", "Sonar": "", "Sonar Deep Research": "", "Sonar Pro": "", "Sonar Reasoning": "", "Sonar Reasoning Pro": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "Д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Speech Playback Speed": "Швидкість відтворення мовлення", "Speech recognition error: {{error}}": "Помилка розпізнавання мови: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "Система розпізнавання мови", "Start of the channel": "Початок каналу", "Start Tag": "", "STDOUT/STDERR": "STDOUT/STDERR", "Stop": "Зупинити", "Stop Generating": "", "Stop Sequence": "Символ зупинки", "Stream Chat Response": "Відповідь стрім-чату", "Stream Delta Chunk Size": "", "Strikethrough": "", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "Модель STT ", "STT Settings": "Налаштування STT", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "Підзаголовок (напр., про Римську імперію)", "Success": "Успіх", "Successfully imported {{userCount}} users.": "", "Successfully updated.": "Успішно оновлено.", "Suggest a change": "", "Suggested": "Запропоновано", "Support": "Підтримати", "Support this plugin:": "Підтримайте цей плагін:", "Supported MIME Types": "", "Sync directory": "Синхронізувати каталог", "System": "Система", "System Instructions": "Системні інструкції", "System Prompt": "Системний промт", "Tags": "Теги", "Tags Generation": "Генерація тегів", "Tags Generation Prompt": "Підказка для генерації тегів", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "Вибірка без хвоста використовується для зменшення впливу менш ймовірних токенів на результат. Вищий показник (напр., 2.0) зменшить вплив сильніше, тоді як значення 1.0 вимикає цю опцію.", "Talk to model": "Спілкуватися з моделлю", "Tap to interrupt": "Нати<PERSON>ніть, щоб перервати", "Task List": "", "Task Model": "", "Tasks": "Завдання", "Tavily API Key": "Ключ <PERSON> Tavily", "Tavily Extract Depth": "", "Tell us more:": "Розкажи нам більше:", "Temperature": "Температура", "Temporary Chat": "Тимчасовий чат", "Temporary Chat by Default": "", "Text Splitter": "Роздільник тексту", "Text-to-Speech": "", "Text-to-Speech Engine": "Система синтезу мови", "Thanks for your feedback!": "Дякуємо за ваш відгук!", "The Application Account DN you bind with for search": "DN облікового запису застосунку, з яким ви здійснюєте прив'язку для пошуку", "The base to search for users": "База для пошуку користувачів", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "Розмір пакету визначає, скільки текстових запитів обробляється одночасно. Більший розмір пакету може підвищити продуктивність і швидкість моделі, але також вимагає більше пам'яті.", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Розробники цього плагіна - пристрасні волонтери зі спільноти. Якщо ви вважаєте цей плагін корисним, будь ласка, зробіть свій внесок у його розвиток.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "Таблиця лідерів оцінки базується на системі рейтингу Ело і оновлюється в реальному часі.", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "LDAP-атр<PERSON><PERSON><PERSON><PERSON>, який відповідає за пошту, яку користувачі використовують для входу.", "The LDAP attribute that maps to the username that users use to sign in.": "LDAP-атр<PERSON><PERSON><PERSON><PERSON>, який відповідає за ім'я користувача, яке використовують користувачі для входу.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "Таблиця лідерів наразі в бета-версії, і ми можемо коригувати розрахунки рейтингів у міру вдосконалення алгоритму.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "Максимальний розмір файлу в МБ. Якщо розмір файлу перевищує цей ліміт, файл не буде завантажено.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Максима<PERSON>ьна кількість файлів, які можна використати одночасно в чаті. Якщо кількість файлів перевищує цей ліміт, файли не будуть завантажені.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The passwords you entered don't quite match. Please double-check and try again.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Оцінка повинна бути в діапазоні від 0.0 (0%) до 1.0 (100%).", "The stream delta chunk size for the model. Increasing the chunk size will make the model respond with larger pieces of text at once.": "", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "Температура моделі. Збільшення температури зробить відповіді моделі більш креативними.", "The Weight of BM25 Hybrid Search. 0 more lexical, 1 more semantic. Default 0.5": "", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "Тема", "Thinking...": "Думаю...", "This action cannot be undone. Do you wish to continue?": "Цю дію не можна скасувати. Ви бажаєте продовжити?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "Цей канал був створений {{createdAt}}. Це самий початок каналу {{channelName}}.", "This chat won't appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Це забезпечує збереження ваших цінних розмов у безпечному бекенд-сховищі. Дякуємо!", "This feature is experimental and may be modified or discontinued without notice.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Це експериментальна функція, вона може працювати не так, як очікувалося, і може бути змінена в будь-який час.", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "Ця опція контролює, скільки токенів зберігається при оновленні контексту. Наприклад, якщо встановити значення 2, останні 2 токени контексту розмови будуть збережені. Збереження контексту допомагає підтримувати послідовність розмови, але може зменшити здатність реагувати на нові теми.", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "Ця опція встановлює максимальну кількість токенів, які модель може згенерувати у своїй відповіді. Збільшення цього ліміту дозволяє моделі надавати довші відповіді, але також може підвищити ймовірність генерації непотрібного або нерелевантного контенту.", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Цей варіант видалить усі існуючі файли в колекції та замінить їх новими завантаженими файлами.", "This response was generated by \"{{model}}\"": "Цю відповідь згенеровано за допомогою \"{{model}}\"", "This will delete": "Це призведе до видалення", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "Це видалить <strong>{{NAME}}</strong> та <strong>усі його вмісти</strong>.", "This will delete all models including custom models": "Це видалить усі моделі, включаючи користувацькі моделі", "This will delete all models including custom models and cannot be undone.": "Це видалить усі моделі, включаючи користувацькі моделі, і не може бути скасовано.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Це скине базу знань і синхронізує усі файли. Ви бажаєте продовжити?", "Thorough explanation": "Детальне пояснення", "Thought for {{DURATION}}": "Думка для {{DURATION}}", "Thought for {{DURATION}} seconds": "Думав протягом {{DURATION}} секунд.", "Thought for less than a second": "", "Thread": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "Потрібна URL-адреса сервера Tika.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Порада: Оновіть кілька слотів змінних послідовно, натискаючи клавішу табуляції у вікні чату після кожної заміни.", "Title": "Заголовок", "Title (e.g. Tell me a fun fact)": "Заголовок (напр., Розкажіть мені цікавий факт)", "Title Auto-Generation": "Автогенерація заголовків", "Title cannot be an empty string.": "Заголовок не може бути порожнім рядком.", "Title Generation": "Генерація заголовка", "Title Generation Prompt": "Промт для генерування заголовків", "TLS": "TLS", "To access the available model names for downloading,": "Щоб отримати доступ до назв доступних для завантаження моделей,", "To access the GGUF models available for downloading,": "Щоб отримати доступ до моделей GGUF, які можна завантажити,,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Щоб отримати доступ до веб-інтерфейсу, зверніться до адміністратора. Адміністратори можуть керувати статусами користувачів з Панелі адміністратора.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Щоб прикріпити базу знань тут, спочатку додайте їх до робочого простору \"Знання\".", "To learn more about available endpoints, visit our documentation.": "Щоб дізнатися більше про доступні кінцеві точки, відвідайте нашу документацію.", "To learn more about powerful prompt variables, click here": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Для захисту вашої конфіденційності з вашими відгуками діляться лише оцінками, ID моделей, тегами та метаданими — ваші журнали чату залишаються приватними і не включаються.", "To select actions here, add them to the \"Functions\" workspace first.": "Щоб вибрати дії тут, спочатку додайте їх до робочої області \"Функції\".", "To select filters here, add them to the \"Functions\" workspace first.": "Щоб обрати фільтри тут, спочатку додайте їх до робочої області \"Функції\".", "To select toolkits here, add them to the \"Tools\" workspace first.": "Щоб обрати тут набори інструментів, спочатку додайте їх до робочої області \"Інструменти\".", "Toast notifications for new updates": "Сповіщення Toast про нові оновлення", "Today": "Сьогодні", "Toggle search": "", "Toggle settings": "Переключити налаштування", "Toggle sidebar": "Переключити бокову панель", "Toggle whether current connection is active.": "", "Token": "Токен", "Too verbose": "Занадто докладно", "Tool created successfully": "Інструмент успішно створено", "Tool deleted successfully": "Інструмент успішно видалено", "Tool Description": "Опис інструменту", "Tool ID": "ID інструменту", "Tool imported successfully": "Інструмент успішно імпортовано", "Tool Name": "Назва інструменту", "Tool Servers": "", "Tool updated successfully": "Інструмент успішно оновлено", "Tools": "Інструменти", "Tools Access": "Доступ до інструментів", "Tools are a function calling system with arbitrary code execution": "Інструменти - це система виклику функцій з довільним виконанням коду", "Tools Function Calling Prompt": "Підказка для виклику функцій інструментів", "Tools have a function calling system that allows arbitrary code execution.": "Інструменти мають систему виклику функцій, яка дозволяє виконання довільного коду.", "Tools Public Sharing": "Публічний обмін інструментами", "Top K": "Top K", "Top K Reranker": "Top K Реранкер", "Transformers": "Трансформери", "Trouble accessing Ollama?": "Проблеми з доступом до Ollama?", "Trust Proxy Environment": "Довіряти середовищу проксі", "Try Again": "", "TTS Model": "Модель TTS", "TTS Settings": "Налаштування TTS", "TTS Voice": "Голос TTS", "Type": "Тип", "Type Hugging Face Resolve (Download) URL": "Введіть URL ресурсу Hugging Face Resolve (завантаження)", "Uh-oh! There was an issue with the response.": "Ой-ой! Сталася проблема з відповіддю.", "UI": "Користувацький інтерфейс", "Unarchive All": "Розархівувати все", "Unarchive All Archived Chats": "Розархівувати усі архівовані чати", "Unarchive Chat": "Розархівувати чат", "Underline": "", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "Розкрийте таємниці", "Unpin": "Відчепити", "Unravel secrets": "Розплутуйте секрети", "Unsupported file type.": "", "Untagged": "Без тегів", "Untitled": "", "Update": "Оновлення", "Update and Copy Link": "Оновлення та копіювання посилання", "Update for the latest features and improvements.": "Оновіть програми для нових функцій та покращень.", "Update password": "Оновити пароль", "Updated": "Оновлено", "Updated at": "Оновлено на", "Updated At": "Оновлено на", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "Оновіть до ліцензованого плану для розширених можливостей, включаючи кастомізацію теми та брендування, а також спеціалізовану підтримку.", "Upload": "Завант<PERSON><PERSON><PERSON>ти", "Upload a GGUF model": "Завантажити GGUF модель", "Upload Audio": "", "Upload directory": "Завантажити каталог", "Upload files": "Завантажити файли", "Upload Files": "Завантажити файли", "Upload Pipeline": "Завантажити конвеєр", "Upload Progress": "Прогрес завантаження", "Upload Progress: {{uploadedFiles}}/{{totalFiles}} ({{percentage}}%)": "", "URL": "URL", "URL is required": "", "URL Mode": "Режим URL-адреси", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "Використовуйте '#' у полі введення підказки, щоб завантажити та включити свої знання.", "Use groups to group your users and assign permissions.": "Використовуйте групи, щоб об'єднувати користувачів і призначати дозволи.", "Use LLM": "", "Use no proxy to fetch page contents.": "", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "user": "користувач", "User": "Користув<PERSON><PERSON>", "User Groups": "", "User location successfully retrieved.": "Місцезнаходження користувача успішно знайдено.", "User menu": "", "User Webhooks": "Вебхуки користувача", "Username": "Ім'я користувача", "Users": "Користувачі", "Using Entire Document": "", "Using Focused Retrieval": "", "Using the default arena model with all models. Click the plus button to add custom models.": "Використовуючи модель арени за замовчуванням з усіма моделями. Натисніть кнопку плюс, щоб додати користувацькі моделі.", "Valid time units:": "Дійсні одиниці часу:", "Validate certificate": "", "Valves": "Кла<PERSON>ани", "Valves updated": "Клапани оновлено", "Valves updated successfully": "Клапани успішно оновлено", "variable": "змінна", "Verify Connection": "Перевірити з'єднання", "Verify SSL Certificate": "", "Version": "Версія", "Version {{selectedVersion}} of {{totalVersions}}": "Версія {{selectedVersion}} з {{totalVersions}}", "View Replies": "Переглянути відповіді", "View Result from **{{NAME}}**": "", "Visibility": "Видимість", "Vision": "", "Voice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Voice Input": "Голосове введення", "Voice mode": "", "Warning": "Увага!", "Warning:": "Увага:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Попередження: Увімкнення цього дозволить користувачам завантажувати довільний код на сервер.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Попередження: Як<PERSON>о ви оновлюєте або змінюєте модель вбудовування, вам потрібно буде повторно імпортувати усі документи.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "Попередження: Виконання коду в Jupyter дозволяє виконувати任 будь-який код, що становить серйозні ризики для безпеки — дійте з крайньою обережністю.", "Web": "<PERSON>е<PERSON>", "Web API": "Веб-API", "Web Loader Engine": "", "Web Search": "Веб-Пошук", "Web Search Engine": "Веб-пошукова система", "Web Search in Chat": "Пошук в інтернеті в чаті", "Web Search Query Generation": "Генерація запиту для пошуку в мережі", "Webhook URL": "Webhook URL", "WebUI Settings": "Налаштування WebUI", "WebUI URL": "WebUI URL", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI надсилатиме запити до \"{{url}}/api/chat\"", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI надсилатиме запити до \"{{url}}/chat/completions\"", "What are you trying to achieve?": "Чого ви прагнете досягти?", "What are you working on?": "Над чим ти працюєш?", "What's New in": "Що нового в", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "Коли активовано, модель буде відповідати на кожне повідомлення чату в режимі реального часу, генеруючи відповідь, як тільки користувач надішле повідомлення. Цей режим корисний для застосувань життєвих вітань чатів, але може позначитися на продуктивності на повільнішому апаратному забезпеченні.", "wherever you are": "де б ви не були", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "<PERSON>hisper (Локально)", "Why?": "Чому?", "Widescreen Mode": "Широкоекранний режим", "Width": "", "Won": "Переможець", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "Працює разом із top-k. Вищий показник (напр., 0.95) призведе до більш різноманітного тексту, тоді як нижчий показник (напр., 0.5) згенерує більш сфокусований та консервативний текст.", "Workspace": "Робочий простір", "Workspace Permissions": "Дозволи робочого простору.", "Write": "Писати", "Write a prompt suggestion (e.g. Who are you?)": "Напишіть промт (напр., Хто ти?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Напишіть стислий зміст у 50 слів, який узагальнює [тема або ключове слово].", "Write something...": "Напишіть щось...", "Write your model system prompt content here\ne.g.) You are Mario from Super Mario Bros, acting as an assistant.": "Напишіть тут зміст системного підказу (system prompt) вашої моделі\nнапр.: Ви — Маріо з Super Mario Bros і дієте як асистент.", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "Вчора", "You": "Ви", "You are currently using a trial license. Please contact support to upgrade your license.": "Ви наразі використовуєте пробну ліцензію. Будь ласка, зверніться до підтримки для оновлення вашої ліцензії.", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Ви можете спілкуватися лише з максимальною кількістю {{maxCount}} файлів одночасно.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Ви можете налаштувати ваші взаємодії з мовними моделями, додавши спогади через кнопку 'Керувати' внизу, що зробить їх більш корисними та персоналізованими для вас.", "You cannot upload an empty file.": "Ви не можете завантажити порожній файл.", "You do not have permission to upload files.": "У вас немає дозволу завантажувати файли.", "You have no archived conversations.": "У вас немає архівованих розмов.", "You have shared this chat": "Ви поділилися цим чатом", "You're a helpful assistant.": "Ви корисний асистент.", "You're now logged in.": "Ви увійшли в систему.", "Your Account": "", "Your account status is currently pending activation.": "Статус вашого облікового запису наразі очікує на активацію.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Весь ваш внесок піде безпосередньо розробнику плагіна; Open WebUI не бере жодних відсотків. Од<PERSON><PERSON>, обрана платформа фінансування може мати свої власні збори.", "Youtube": "Youtube", "Youtube Language": "Мова YouTube", "Youtube Proxy URL": "URL проксі-сервера YouTube"}