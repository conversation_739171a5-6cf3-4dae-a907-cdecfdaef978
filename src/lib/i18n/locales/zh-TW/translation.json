{"-1 for no limit, or a positive integer for a specific limit": "-1 表示無限制，正整數表示特定限制", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s'、'm'、'h'、'd'、'w' ， '-1' 表示無到期時間。", "(e.g. `sh webui.sh --api --api-auth username_password`)": "（例如：`sh webui.sh --api --api-auth username_password`）", "(e.g. `sh webui.sh --api`)": "（例如：`sh webui.sh --api`）", "(latest)": "（最新版）", "(leave blank for to use commercial endpoint)": "（留空以使用商業端點）", "[Last] dddd [at] h:mm A": "[上次] dddd [於] h:mm A", "[Today at] h:mm A": "[今天] h:mm A", "[Yesterday at] h:mm A": "[昨天] h:mm A", "{{ models }}": "{{ models }}", "{{COUNT}} Available Tools": "{{COUNT}} 個可用工具", "{{COUNT}} characters": "{{COUNT}} 個字元", "{{COUNT}} extracted lines": "", "{{COUNT}} hidden lines": "已隱藏 {{COUNT}} 行", "{{COUNT}} Replies": "{{COUNT}} 回覆", "{{COUNT}} words": "{{COUNT}} 個詞", "{{model}} download has been canceled": "已取消模型 {{model}} 的下載", "{{user}}'s Chats": "{{user}} 的對話", "{{webUIName}} Backend Required": "需要提供 {{webUIName}} 後端", "*Prompt node ID(s) are required for image generation": "* 圖片生成需要提示詞節點 ID", "A new version (v{{LATEST_VERSION}}) is now available.": "新版本 (v{{LATEST_VERSION}}) 已釋出。", "A task model is used when performing tasks such as generating titles for chats and web search queries": "執行「產生對話標題」和「網頁搜尋查詢生成」等任務時使用的任務模型", "a user": "使用者", "About": "關於", "Accept autocomplete generation / Jump to prompt variable": "接受自動完成生成／跳轉至提示變數", "Access": "存取", "Access Control": "存取控制", "Accessible to all users": "所有使用者皆可存取", "Account": "帳號", "Account Activation Pending": "帳號待啟用", "Accurate information": "準確資訊", "Action": "操作", "Action not found": "找不到對應的操作項目", "Action Required for Chat Log Storage": "需要操作以儲存對話紀錄", "Actions": "動作", "Activate": "啟用", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "在對話輸入框中輸入 \"/{{COMMAND}}\" 來啟用此命令。", "Active": "線上", "Active Users": "活躍使用者", "Add": "新增", "Add a model ID": "新增模型 ID", "Add a short description about what this model does": "新增這個模型的簡短描述", "Add a tag": "新增標籤", "Add Arena Model": "新增競技場模型", "Add Connection": "新增連線", "Add Content": "新增內容", "Add content here": "在此新增內容", "Add Custom Parameter": "新增自訂參數", "Add custom prompt": "新增自訂提示詞", "Add Details": "豐富細節", "Add Files": "新增檔案", "Add Group": "新增群組", "Add Memory": "新增記憶", "Add Model": "新增模型", "Add Reaction": "新增動作", "Add Tag": "新增標籤", "Add Tags": "新增標籤", "Add text content": "新增文字內容", "Add User": "新增使用者", "Add User Group": "新增使用者群組", "Additional Config": "額外設定", "Additional configuration options for marker. This should be a JSON string with key-value pairs. For example, '{\"key\": \"value\"}'. Supported keys include: disable_links, keep_pageheader_in_output, keep_pagefooter_in_output, filter_blank_pages, drop_repeated_text, layout_coverage_threshold, merge_threshold, height_tolerance, gap_threshold, image_threshold, min_line_length, level_count, default_level": "Datalab Marker 的額外設定選項，可以填寫一個包含鍵值對的 JSON 字串。例如：{\"key\": \"value\"}。支援的鍵包括：disable_links、keep_pageheader_in_output、keep_pagefooter_in_output、filter_blank_pages、drop_repeated_text、layout_coverage_threshold、merge_threshold、height_tolerance、gap_threshold、image_threshold、min_line_length、level_count 和 default_level。", "Adjusting these settings will apply changes universally to all users.": "調整這些設定將會影響所有使用者。", "admin": "管理員", "Admin": "管理員", "Admin Panel": "管理員控制台", "Admin Settings": "管理員設定", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "管理員可以隨時使用所有工具；使用者則需在工作區中為每個模型分配工具。", "Advanced Parameters": "進階參數", "Advanced Params": "進階參數", "AI": "AI", "All": "全部", "All Documents": "所有文件", "All models deleted successfully": "成功刪除所有模型", "Allow Call": "允許通話", "Allow Chat Controls": "允許控制對話", "Allow Chat Delete": "允許刪除對話", "Allow Chat Deletion": "允許刪除對話紀錄", "Allow Chat Edit": "允許編輯對話", "Allow Chat Export": "允許匯出對話", "Allow Chat Params": "允許設定模型進階參數", "Allow Chat Share": "允許分享對話", "Allow Chat System Prompt": "允許設定對話系統提示詞", "Allow Chat Valves": "允許修改工具和函數的設定項目（Valves）", "Allow Continue Response": "允許繼續回應", "Allow Delete Messages": "允許刪除訊息", "Allow File Upload": "允許上傳檔案", "Allow Multiple Models in Chat": "允許在對話中使用多個模型", "Allow non-local voices": "允許非本機語音", "Allow Rate Response": "允許為回應評分", "Allow Regenerate Response": "允許重新產生回應", "Allow Speech to Text": "允許語音轉文字", "Allow Temporary Chat": "允許臨時對話", "Allow Text to Speech": "允許文字轉語音", "Allow User Location": "允許使用者位置", "Allow Voice Interruption in Call": "允許在通話中打斷語音", "Allowed Endpoints": "允許的端點", "Allowed File Extensions": "允許的檔案副檔名", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "允許上傳的檔案副檔名。多個副檔名請用逗號分隔，留空則允許所有檔案類型。", "Already have an account?": "已經有帳號了嗎？", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "top_p 的替代方案，用於確保品質與多樣性之間的平衡。參數 p 代表一個 token 被考慮的最低機率，相對於最有可能 token 的機率。例如，當 p=0.05 且最有可能 token 的機率為 0.9 時，機率小於 0.045 的 logits 將被過濾掉。", "Always": "總是", "Always Collapse Code Blocks": "總是摺疊程式碼區塊", "Always Expand Details": "總是展開詳細資訊", "Always Play Notification Sound": "總是播放通知音效", "Amazing": "很棒", "an assistant": "助理", "An error occurred while fetching the explanation": "取得說明時發生錯誤", "Analytics": "分析", "Analyzed": "分析完畢", "Analyzing...": "正在分析...", "and": "和", "and {{COUNT}} more": "和另外 {{COUNT}} 個", "and create a new shared link.": "並建立新的共用連結。", "Android": "Android", "API": "API", "API Base URL": "API 基底 URL", "API Base URL for Datalab Marker service. Defaults to: https://www.datalab.to/api/v1/marker": "Datalab Marker API 服務的請求 URL。預設為：https://www.datalab.to/api/v1/marker", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "在圖片描述中使用視覺語言模型的 API 詳情。此參數不可與 picture_description_local 同時使用。", "API Key": "API 金鑰", "API Key created.": "API 金鑰已建立。", "API Key Endpoint Restrictions": "API 金鑰端點限制", "API keys": "API 金鑰", "API Version": "API 版本", "API Version is required": "API 版本為必填項目", "Application DN": "應用程式 DN", "Application DN Password": "應用程式 DN 密碼", "applies to all users with the \"user\" role": "適用於所有具有「使用者」角色的使用者", "April": "4 月", "Archive": "封存", "Archive All Chats": "封存所有對話紀錄", "Archived Chats": "封存的對話紀錄", "archived-chat-export": "archived-chat-export", "Are you sure you want to clear all memories? This action cannot be undone.": "您確定要清除所有記憶嗎？此操作無法復原。", "Are you sure you want to delete this channel?": "您確定要刪除此頻道嗎？", "Are you sure you want to delete this message?": "您確定要刪除此訊息嗎？", "Are you sure you want to unarchive all archived chats?": "您確定要解除封存所有封存的對話記錄嗎？", "Are you sure?": "您確定嗎？", "Arena Models": "競技場模型", "Artifacts": "Artifacts", "Ask": "提問", "Ask a question": "提出問題", "Assistant": "助理", "Attach file from knowledge": "從知識庫附加檔案", "Attention to detail": "注重細節", "Attribute for Mail": "使用者郵箱屬性", "Attribute for Username": "使用者名稱屬性", "Audio": "音訊", "August": "8 月", "Auth": "驗證", "Authenticate": "驗證", "Authentication": "驗證", "Auto": "自動", "Auto-Copy Response to Clipboard": "自動將回應複製到剪貼簿", "Auto-playback response": "自動播放回應", "Autocomplete Generation": "自動完成生成", "Autocomplete Generation Input Max Length": "自動完成輸入最大長度", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 API 驗證字串", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 基底 URL", "AUTOMATIC1111 Base URL is required.": "需要提供 AUTOMATIC1111 基底 URL。", "Available list": "可用清單", "Available Tools": "可用工具", "available users": "可用名額", "available!": "可用！", "Away": "離開", "Awful": "糟糕", "Azure AI Speech": "Azure AI 語音", "Azure OpenAI": "Azure OpenAI", "Azure Region": "Azure 區域", "Back": "返回", "Bad Response": "回應不佳", "Banners": "橫幅", "Base Model (From)": "基礎模型（來自）", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "基礎模型清單快取僅在啟動或儲存設定時獲取基礎模型從而加快存取速度，但可能不會顯示最近的基礎模型變更。", "Bearer": "Bearer", "before": "之前", "Being lazy": "懶惰模式", "Beta": "測試", "Bing Search V7 Endpoint": "Bing 搜尋 V7 端點", "Bing Search V7 Subscription Key": "Bing 搜尋 V7 訂閱金鑰", "Bio": "個人簡介", "Birth Date": "生日", "BM25 Weight": "BM25 混合搜尋權重", "Bocha Search API Key": "Bocha 搜尋 API 金鑰", "Bold": "粗體", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "針對受限的回應，增強或懲罰特定 tokens。偏差值將限制在 -100 到 100 (含)。 (預設：none)", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "必須同時提供 Docling OCR 引擎與語言設定，或兩者皆留空。", "Brave Search API Key": "Brave 搜尋 API 金鑰", "Bullet List": "無序清單", "Button ID": "按鈕 ID", "Button Label": "按鈕文字", "Button Prompt": "點擊按鈕後執行的提示詞", "By {{name}}": "由 {{name}} 製作", "Bypass Embedding and Retrieval": "繞過嵌入與檢索", "Bypass Web Loader": "繞過網頁載入器", "Cache Base Model List": "快取基礎模型清單", "Calendar": "日曆", "Call": "通話", "Call feature is not supported when using Web STT engine": "使用網頁語音辨識 (Web STT) 引擎時不支援通話功能", "Camera": "相機", "Cancel": "取消", "Capabilities": "功能", "Capture": "相機", "Capture Audio": "錄製音訊", "Certificate Path": "憑證路徑", "Change Password": "修改密碼", "Channel deleted successfully": "成功刪除頻道", "Channel Name": "頻道名稱", "Channel updated successfully": "成功更新頻道", "Channels": "頻道", "Character": "角色", "Character limit for autocomplete generation input": "自動完成生成輸入的字元限制", "Chart new frontiers": "探索新領域", "Chat": "對話", "Chat Background Image": "對話背景圖片", "Chat Bubble UI": "對話氣泡介面", "Chat Controls": "對話控制選項", "Chat Conversation": "對話內容", "Chat direction": "對話方向", "Chat ID": "對話 ID", "Chat moved successfully": "對話已移動", "Chat Overview": "對話概覽", "Chat Permissions": "對話權限", "Chat Tags Auto-Generation": "自動生成對話標籤", "Chats": "對話", "Check Again": "再次檢查", "Check for updates": "檢查更新", "Checking for updates...": "正在檢查更新...", "Choose a model before saving...": "儲存前請選擇一個模型...", "Chunk Overlap": "區塊重疊", "Chunk Size": "區塊大小", "Ciphers": "加密方式", "Citation": "引用", "Citations": "引用文獻", "Clear memory": "清除記憶", "Clear Memory": "清除記憶", "click here": "點選此處", "Click here for filter guides.": "點選此處檢視篩選器指南。", "Click here for help.": "點選此處取得協助。", "Click here to": "點選此處", "Click here to download user import template file.": "點選此處下載使用者匯入範本檔案。", "Click here to learn more about faster-whisper and see the available models.": "點選此處了解更多關於 faster-whisper 的資訊並檢視可用的模型。", "Click here to see available models.": "點選此處以檢視可用的模型", "Click here to select": "點選此處選擇", "Click here to select a csv file.": "點選此處選擇 CSV 檔案。", "Click here to select a py file.": "點選此處選擇 Python 檔案。", "Click here to upload a workflow.json file.": "點選此處上傳 workflow.json 檔案。", "click here.": "點選此處。", "Click on the user role button to change a user's role.": "點選使用者角色按鈕變更使用者的角色。", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "剪貼簿寫入權限遭拒。請檢查您的瀏覽器設定，授予必要的存取權限。", "Clone": "複製", "Clone Chat": "複製對話", "Clone of {{TITLE}}": "{{TITLE}} 的副本", "Close": "關閉", "Close Banner": "關閉橫幅", "Close Configure Connection Modal": "關閉外部連線設定彈出視窗", "Close modal": "關閉模型", "Close settings modal": "關閉設定模型", "Close Sidebar": "收起側邊欄", "CMU ARCTIC speaker embedding name": "CMU ARCTIC 朗讀者嵌入名稱（Speaker Embedding Name）", "Code Block": "程式碼區塊", "Code execution": "程式碼執行", "Code Execution": "程式碼執行", "Code Execution Engine": "程式碼執行引擎", "Code Execution Timeout": "程式執行超時", "Code formatted successfully": "成功格式化程式碼", "Code Interpreter": "程式碼直譯器", "Code Interpreter Engine": "程式碼直譯器引擎", "Code Interpreter Prompt Template": "程式碼直譯器提示詞範本", "Collapse": "摺疊", "Collection": "收藏", "Color": "顏色", "ComfyUI": "ComfyUI", "ComfyUI API Key": "ComfyUI API 金鑰", "ComfyUI Base URL": "ComfyUI 基底 URL", "ComfyUI Base URL is required.": "需要提供 ComfyUI 基底 URL。", "ComfyUI Workflow": "ComfyUI 工作流程", "ComfyUI Workflow Nodes": "ComfyUI 工作流程節點", "Comma separated Node Ids (e.g. 1 or 1,2)": "使用英文逗號分隔的節點 ID（例如 1 或 1,2）", "Command": "命令", "Comment": "註解", "Completions": "自動完成", "Compress Images in Channels": "壓縮頻道中的圖片", "Concurrent Requests": "平行請求", "Config imported successfully": "成功匯入設定", "Configure": "設定", "Confirm": "確認", "Confirm Password": "確認密碼", "Confirm your action": "確認您的操作", "Confirm your new password": "確認您的新密碼", "Confirm Your Password": "確認您的密碼", "Connect to your own OpenAI compatible API endpoints.": "連線至您自有或其他與 OpenAI API 相容的端點。", "Connect to your own OpenAPI compatible external tool servers.": "連線至您自有或其他與 OpenAPI 相容的外部工具伺服器。", "Connection failed": "連線失敗", "Connection successful": "連線成功", "Connection Type": "連線類型", "Connections": "連線", "Connections saved successfully": "連線已成功儲存", "Connections settings updated": "連線設定已更新", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "限制推理模型的推理程度。僅適用於特定供應商支援推理程度的推理模型。", "Contact Admin for WebUI Access": "請聯絡管理員以取得 WebUI 存取權限", "Content": "內容", "Content Extraction Engine": "內容擷取引擎", "Continue Response": "繼續回應", "Continue with {{provider}}": "使用 {{provider}} 繼續", "Continue with Email": "使用 Email 繼續", "Continue with LDAP": "使用 LDAP 繼續", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "控制文字轉語音（TTS）請求中如何分割訊息文字。「標點符號」分割為句子，「段落」分割為段落，「無」則保持訊息為單一字串。", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "控制生成文字中 token 序列的重複程度。較高的值（例如：1.5）會更強烈地懲罰重複，而較低的值（例如：1.1）會更寬容。設為 1 時，此功能將停用。", "Controls": "控制選項", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "控制輸出結果的連貫性與多樣性之間的平衡。數值越低會生成更集中且連貫的文字。", "Conversation saved successfully": "對話已儲存", "Copied": "已複製", "Copied link to clipboard": "已複製連結至剪貼簿", "Copied shared chat URL to clipboard!": "已複製共用對話 URL 到剪貼簿！", "Copied to clipboard": "已複製到剪貼簿", "Copy": "複製", "Copy Formatted Text": "複製格式化文字", "Copy last code block": "複製最後一個程式碼區塊", "Copy last response": "複製最後一個回應", "Copy link": "複製連結", "Copy Link": "複製連結", "Copy to clipboard": "複製到剪貼簿", "Copying to clipboard was successful!": "成功複製到剪貼簿！", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "CORS 必須由供應商正確設定，以允許來自 Open WebUI 的請求。", "Create": "建立", "Create a knowledge base": "建立知識", "Create a model": "建立模型", "Create Account": "建立帳號", "Create Admin Account": "建立管理員賬號", "Create Channel": "建立頻道", "Create Folder": "建立分組", "Create Group": "建立群組", "Create Knowledge": "建立知識", "Create new key": "建立新的金鑰", "Create new secret key": "建立新的金鑰", "Create Note": "新增筆記", "Create your first note by clicking on the plus button below.": "點選下方加號按鈕建立您的第一則筆記。", "Created at": "建立於", "Created At": "建立於", "Created by": "建立者", "CSV Import": "CSV 匯入", "Ctrl+Enter to Send": "使用 Ctrl+Enter 傳送", "Current Model": "目前模型", "Current Password": "目前密碼", "Custom": "自訂", "Custom description enabled": "自訂描述已啟用", "Custom Parameter Name": "自訂參數名稱", "Custom Parameter Value": "自訂參數值", "Danger Zone": "危險區域", "Dark": "深色", "Database": "資料庫", "Datalab Marker API": "Datalab Marker API", "Datalab Marker API Key required.": "需要 Datalab Marker API 金鑰。", "DD/MM/YYYY": "DD/MM/YYYY", "December": "12 月", "Deepgram": "Deepgram", "Default": "預設", "Default (Open AI)": "預設 (OpenAI)", "Default (SentenceTransformers)": "預設 (SentenceTransformers)", "Default action buttons will be used.": "已啟用預設的快捷操作按鈕。", "Default description enabled": "預設描述已啟用", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "預設模式透過在執行前呼叫工具一次，來與更廣泛的模型相容。原生模式則利用模型內建的工具呼叫能力，但需要模型本身就支援此功能。", "Default Model": "預設模型", "Default model updated": "預設模型已更新", "Default Models": "預設模型", "Default permissions": "預設權限", "Default permissions updated successfully": "成功更新預設權限", "Default Prompt Suggestions": "預設提示詞建議", "Default to 389 or 636 if TLS is enabled": "如果啟用了 TLS 則預設為 389 或 636", "Default to ALL": "預設到所有", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "預設使用分段檢索以提取聚焦且相關的內容，建議用於大多數情況。", "Default User Role": "預設使用者角色", "Delete": "刪除", "Delete a model": "刪除模型", "Delete All Chats": "刪除所有對話紀錄", "Delete All Models": "刪除所有模型", "Delete chat": "刪除對話紀錄", "Delete Chat": "刪除對話紀錄", "Delete chat?": "刪除對話紀錄？", "Delete folder?": "刪除資料夾？", "Delete function?": "刪除函式？", "Delete Message": "刪除訊息", "Delete message?": "刪除訊息？", "Delete note?": "刪除筆記？", "Delete prompt?": "刪除提示詞？", "delete this link": "刪除此連結", "Delete tool?": "刪除工具？", "Delete User": "刪除使用者", "Deleted {{deleteModelTag}}": "已刪除 {{deleteModelTag}}", "Deleted {{name}}": "已刪除 {{name}}", "Deleted User": "刪除使用者？", "Deployment names are required for Azure OpenAI": "需要提供 Azure OpenAI 部署名稱", "Describe Pictures in Documents": "文件中的圖片描述", "Describe your knowledge base and objectives": "描述您的知識庫和目標", "Description": "描述", "Detect Artifacts Automatically": "自動偵測 Artifacts", "Dictate": "語音輸入", "Didn't fully follow instructions": "未完全遵循指示", "Direct": "直接", "Direct Connections": "直接連線", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "直接連線允許使用者連線至自有或其他與 OpenAI API 相容的端點。", "Direct Tool Servers": "直連工具伺服器", "Directory selection was cancelled": "已取消選擇目錄", "Disable Code Interpreter": "停用程式碼解譯器", "Disable Image Extraction": "停用圖片擷取", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "停用從 PDF 擷取圖片。若啟用「使用 LLM」，圖片將自動新增說明。預設為 False。", "Disabled": "已停用", "Discover a function": "發掘函式", "Discover a model": "發掘模型", "Discover a prompt": "發掘提示詞", "Discover a tool": "發掘工具", "Discover how to use Open WebUI and seek support from the community.": "探索如何使用 Open WebUI 並從社群尋求支援。", "Discover wonders": "發掘奇蹟", "Discover, download, and explore custom functions": "發掘、下載及探索自訂函式", "Discover, download, and explore custom prompts": "發掘、下載及探索自訂提示詞", "Discover, download, and explore custom tools": "發掘、下載及探索自訂工具", "Discover, download, and explore model presets": "發掘、下載及探索模型預設集", "Display": "顯示", "Display Emoji in Call": "在通話中顯示表情符號", "Display Multi-model Responses in Tabs": "以標籤頁的形式展示多個模型的回應", "Display the username instead of You in the Chat": "在對話中顯示使用者名稱，而非「您」", "Displays citations in the response": "在回應中顯示引用", "Dive into knowledge": "挖掘知識", "Do not install functions from sources you do not fully trust.": "請勿從您無法完全信任的來源安裝函式。", "Do not install tools from sources you do not fully trust.": "請勿從您無法完全信任的來源安裝工具。", "Docling": "<PERSON><PERSON>", "Docling Server URL required.": "需要提供 Docling 伺服器 URL。", "Document": "文件", "Document Intelligence": "Document Intelligence", "Document Intelligence endpoint required.": "需要提供 Document Intelligence 端點。", "Documentation": "說明文件", "Documents": "文件", "does not make any external connections, and your data stays securely on your locally hosted server.": "不會建立任何外部連線，而且您的資料會安全地儲存在您本機伺服器上。", "Domain Filter List": "網域篩選列表", "don't fetch random pipelines from sources you don't trust.": "請勿從您無法信任的來源擷取任意管線。", "Don't have an account?": "還沒註冊帳號嗎？", "don't install random functions from sources you don't trust.": "請勿從您無法信任的來源安裝隨機函式。", "don't install random tools from sources you don't trust.": "請勿從您無法信任的來源安裝隨機工具。", "Don't like the style": "不喜歡這個樣式", "Done": "完成", "Download": "下載", "Download & Delete": "下載並刪除", "Download as SVG": "以 SVG 格式下載", "Download canceled": "已取消下載", "Download Database": "下載資料庫", "Drag and drop a file to upload or select a file to view": "拖放檔案以上傳或選擇檔案以檢視", "Draw": "繪製", "Drop any files here to upload": "拖曳檔案至此處進行上傳", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "例如：'30s'、'10m'。有效的時間單位為 's'、'm'、'h'。", "e.g. \"json\" or a JSON schema": "範例：\"json\" 或一個 JSON schema", "e.g. 60": "例如：60", "e.g. A filter to remove profanity from text": "例如：用來移除不雅詞彙的過濾器", "e.g. en": "例如：en", "e.g. My Filter": "例如：我的篩選器", "e.g. My Tools": "例如：我的工具", "e.g. my_filter": "例如：my_filter", "e.g. my_tools": "例如：my_tools", "e.g. pdf, docx, txt": "例如：pdf, docx, txt", "e.g. Tools for performing various operations": "例如：用於執行各種操作的工具", "e.g., 3, 4, 5 (leave blank for default)": "例如：3、4、5（留空使用預設值）", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "例如：audio/wav,audio/mpeg,video/*（留空使用預設值）", "e.g., en-US,ja-JP (leave blank for auto-detect)": "例如：en-US, ja-JP（留空以自動偵測）", "e.g., westus (leave blank for eastus)": "例如：westus（留空則使用 eastus）", "Edit": "編輯", "Edit Arena Model": "編輯競技場模型", "Edit Channel": "編輯頻道", "Edit Connection": "編輯連線", "Edit Default Permissions": "編輯預設權限", "Edit Folder": "編輯分組", "Edit Memory": "編輯記憶", "Edit User": "編輯使用者", "Edit User Group": "編輯使用者群組", "Edited": "已編輯", "Editing": "編輯中", "Eject": "卸載", "ElevenLabs": "ElevenLabs", "Email": "Email", "Embark on adventures": "展開探險之旅", "Embedding": "嵌入", "Embedding Batch Size": "嵌入批次大小", "Embedding Model": "嵌入模型", "Embedding Model Engine": "嵌入模型引擎", "Embedding model set to \"{{embedding_model}}\"": "嵌入模型已設定為 \"{{embedding_model}}\"", "Enable API Key": "啟用 API 金鑰", "Enable autocomplete generation for chat messages": "啟用對話訊息的自動完成", "Enable Code Execution": "啟用程式碼執行", "Enable Code Interpreter": "啟用程式碼直譯器", "Enable Community Sharing": "啟用社群分享", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "啟用記憶體鎖定（mlock）以防止模型資料被換出 RAM。此選項會將模型的工作頁面集鎖定在 RAM 中，確保它們不會被換出到磁碟。這可以透過避免頁面錯誤和確保快速資料存取來維持效能。", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "啟用記憶體配置圖（mmap）以載入模型資料。此選項允許系統使用磁碟儲存作為 RAM 的延伸，透過將磁碟檔案視為在 RAM 中來處理。這可以透過允許更快的資料存取來改善模型效能。然而，它可能無法在所有系統上正常運作，並且可能會消耗大量磁碟空間。", "Enable Message Rating": "啟用訊息評分", "Enable Mirostat sampling for controlling perplexity.": "啟用 Mirostat 取樣以控制 perplexity。", "Enable New Sign Ups": "允許新使用者註冊", "Enable, disable, or customize the reasoning tags used by the model. \"Enabled\" uses default tags, \"Disabled\" turns off reasoning tags, and \"Custom\" lets you specify your own start and end tags.": "", "Enabled": "已啟用", "End Tag": "", "Endpoint URL": "端點 URL", "Enforce Temporary Chat": "強制使用臨時對話", "Enhance": "增強", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "請確認您的 CSV 檔案包含以下 4 個欄位，並按照此順序排列：姓名、電子郵件、密碼、角色。", "Enter {{role}} message here": "在此輸入 {{role}} 訊息", "Enter a detail about yourself for your LLMs to recall": "輸入有關您的詳細資訊，讓您的大型語言模型可以回想起來", "Enter a title for the pending user info overlay. Leave empty for default.": "為待處理的使用者訊息覆蓋層輸入標題。留空以使用預設值。", "Enter a watermark for the response. Leave empty for none.": "請輸入回應浮水印內容，留空表示不使用浮水印。", "Enter api auth string (e.g. username:password)": "輸入 API 驗證字串（例如：username:password）", "Enter Application DN": "輸入應用程式 DN", "Enter Application DN Password": "輸入應用程式 DN 密碼", "Enter Bing Search V7 Endpoint": "輸入 Bing 搜尋 V7 端點", "Enter Bing Search V7 Subscription Key": "輸入 Bing 搜尋 V7 訂閱金鑰", "Enter Bocha Search API Key": "輸入 Bocha 搜尋 API 金鑰", "Enter Brave Search API Key": "輸入 Brave 搜尋 API 金鑰", "Enter certificate path": "輸入憑證路徑", "Enter CFG Scale (e.g. 7.0)": "輸入 CFG 比例（例如：7.0）", "Enter Chunk Overlap": "輸入區塊重疊", "Enter Chunk Size": "輸入區塊大小", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "輸入逗號分隔的 \"token:bias_value\" 配對 (範例：5432:100, 413:-100)", "Enter Config in JSON format": "輸入 JSON 格式的設定", "Enter content for the pending user info overlay. Leave empty for default.": "為待處理的使用者訊息覆蓋層輸入內容。留空以使用預設值。", "Enter coordinates (e.g. 51.505, -0.09)": "輸入座標經緯度（例如：51.505, -0.09）", "Enter Datalab Marker API Base URL": "輸入 Datalab Marker API 請求 URL", "Enter Datalab Marker API Key": "輸入 Datalab Marker API 金鑰", "Enter description": "輸入描述", "Enter Docling OCR Engine": "輸入 Docling OCR 引擎", "Enter Docling OCR Language(s)": "輸入 Docling OCR 語言", "Enter Docling Server URL": "請輸入 Docling 伺服器 URL", "Enter Document Intelligence Endpoint": "輸入 Document Intelligence 端點", "Enter Document Intelligence Key": "輸入 Document Intelligence 金鑰", "Enter domains separated by commas (e.g., example.com,site.org)": "輸入網域，以逗號分隔（例如：example.com, site.org）", "Enter Exa API Key": "輸入 Exa API 金鑰", "Enter External Document Loader API Key": "請輸入外部文件載入器 API 金鑰", "Enter External Document Loader URL": "請輸入外部文件載入器 URL", "Enter External Web Loader API Key": "輸入外部網頁載入器 API 金鑰", "Enter External Web Loader URL": "輸入外部網頁載入器 URL", "Enter External Web Search API Key": "輸入外部網路搜尋 API 金鑰", "Enter External Web Search URL": "輸入外部網路搜尋 URL", "Enter Firecrawl API Base URL": "輸入 Firecrawl API 基底 URL", "Enter Firecrawl API Key": "輸入 Firecrawl API 金鑰", "Enter folder name": "輸入分組名稱", "Enter Github Raw URL": "輸入 GitHub Raw URL", "Enter Google PSE API Key": "輸入 Google PSE API 金鑰", "Enter Google PSE Engine Id": "輸入 Google PSE 引擎 ID", "Enter hex color (e.g. #FF0000)": "輸入十六進制色彩（例如：#FF0000）", "Enter ID": "輸入 ID", "Enter Image Size (e.g. 512x512)": "輸入圖片尺寸（例如：512x512）", "Enter Jina API Key": "輸入 Jina API 金鑰", "Enter JSON config (e.g., {\"disable_links\": true})": "輸入 JSON 設定（例如：{\"disable_links\": true}）", "Enter Jupyter Password": "輸入 Ju<PERSON>ter 密碼", "Enter Jupyter Token": "輸入 <PERSON><PERSON><PERSON>", "Enter Jupyter URL": "輸入 Jupyter URL", "Enter Kagi Search API Key": "輸入 Kagi 搜尋 API 金鑰", "Enter Key Behavior": "Enter 鍵行為", "Enter language codes": "輸入語言代碼", "Enter Mistral API Key": "輸入 Mistral API 金鑰", "Enter Model ID": "輸入模型 ID", "Enter model tag (e.g. {{modelTag}})": "輸入模型標籤（例如：{{modelTag}}）", "Enter Mojeek Search API Key": "輸入 Mojeek 搜尋 API 金鑰", "Enter name": "請輸入名稱", "Enter New Password": "輸入新密碼", "Enter Number of Steps (e.g. 50)": "輸入步驟數（例如：50）", "Enter Perplexity API Key": "輸入 Perplexity API 金鑰", "Enter Playwright Timeout": "輸入 Playwright 逾時時間（毫秒）", "Enter Playwright WebSocket URL": "輸入 Playwright WebSocket URL", "Enter proxy URL (e.g. **************************:port)": "輸入代理程式 URL（例如：**************************:port）", "Enter reasoning effort": "輸入推理程度", "Enter Sampler (e.g. Euler a)": "輸入取樣器（例如：Euler a）", "Enter Scheduler (e.g. Karras)": "輸入排程器（例如：Karras）", "Enter Score": "輸入分數", "Enter SearchApi API Key": "輸入 SearchApi API 金鑰", "Enter SearchApi Engine": "輸入 SearchApi 引擎", "Enter Searxng Query URL": "輸入 SearXNG 查詢 URL", "Enter Seed": "輸入種子值", "Enter SerpApi API Key": "輸入 SerpApi API 金鑰", "Enter SerpApi Engine": "輸入 SerpApi 引擎", "Enter Serper API Key": "輸入 Serper API 金鑰", "Enter Serply API Key": "輸入 Serply API 金鑰", "Enter Serpstack API Key": "輸入 Serpstack API 金鑰", "Enter server host": "輸入伺服器主機", "Enter server label": "輸入伺服器標籤", "Enter server port": "輸入伺服器連接埠", "Enter Sougou Search API sID": "輸入 搜狗搜尋 API sID", "Enter Sougou Search API SK": "輸入 搜狗搜尋 API SK", "Enter stop sequence": "輸入停止序列", "Enter system prompt": "輸入系統提示詞", "Enter system prompt here": "在此輸入系統提示詞", "Enter Tavily API Key": "輸入 Tavily API 金鑰", "Enter Tavily Extract Depth": "輸入 Tavily 提取深度", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "請輸入您 WebUI 的公開 URL。此 URL 將用於在通知中產生連結。", "Enter the URL of the function to import": "請輸入要匯入函式的 URL", "Enter the URL to import": "輸入欲匯入的 URL", "Enter Tika Server URL": "輸入 Tika 伺服器 URL", "Enter timeout in seconds": "請以秒為單位輸入超時時間", "Enter to Send": "使用 Enter 傳送", "Enter Top K": "輸入 Top K 值", "Enter Top K Reranker": "輸入 Top K Reranker", "Enter URL (e.g. http://127.0.0.1:7860/)": "輸入 URL（例如：http://127.0.0.1:7860/）", "Enter URL (e.g. http://localhost:11434)": "輸入 URL（例如：http://localhost:11434）", "Enter value": "輸入數值", "Enter value (true/false)": "輸入布林值（true 或 false）", "Enter Yacy Password": "輸入 Yacy 密碼", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "輸入 Yacy URL（例如：http://yacy.example.com:8090）", "Enter Yacy Username": "輸入 Yacy 使用者名稱", "Enter your code here...": "在此輸入您的程式碼…", "Enter your current password": "輸入您的目前密碼", "Enter Your Email": "輸入您的電子郵件", "Enter Your Full Name": "輸入您的全名", "Enter your gender": "輸入您的性別", "Enter your message": "輸入您的訊息", "Enter your name": "輸入你的名稱", "Enter Your Name": "輸入您的姓名", "Enter your new password": "輸入您的新密碼", "Enter Your Password": "輸入您的密碼", "Enter Your Role": "輸入您的角色", "Enter Your Username": "輸入您的使用者名稱", "Enter your webhook URL": "輸入您的 webhook URL", "Error": "錯誤", "ERROR": "錯誤", "Error accessing directory": "存取目錄時發生錯誤", "Error accessing Google Drive: {{error}}": "存取 Google Drive 時發生錯誤：{{error}}", "Error accessing media devices.": "存取媒體裝置時發生錯誤。", "Error starting recording.": "啟動錄製時發生錯誤。", "Error unloading model: {{error}}": "卸載模型錯誤：{{error}}", "Error uploading file: {{error}}": "上傳檔案時發生錯誤：{{error}}", "Error: A model with the ID '{{modelId}}' already exists. Please select a different ID to proceed.": "錯誤：ID 為「{{modelId}}」的模型已存在。請選擇不同的模型 ID。", "Error: Model ID cannot be empty. Please enter a valid ID to proceed.": "錯誤：模型 ID 不能為空。請輸入有效的模型 ID。", "Evaluations": "評估", "Everyone": "所有人", "Exa API Key": "Exa API 金鑰", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "範例：(&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "範例：ALL", "Example: mail": "範例：mail", "Example: ou=users,dc=foo,dc=example": "範例：ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "範例：sAMAccountName 或 uid 或 userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "您的授權名額已超過上限。請聯絡支援以增加授權名額。", "Exclude": "排除", "Execute code for analysis": "執行程式碼以進行分析", "Executing **{{NAME}}**...": "正在執行 **{{NAME}}** ...", "Expand": "展開", "Experimental": "實驗性功能", "Explain": "解釋", "Explore the cosmos": "探索宇宙", "Export": "匯出", "Export All Archived Chats": "匯出所有已封存的對話", "Export All Chats (All Users)": "匯出所有對話紀錄（所有使用者）", "Export chat (.json)": "匯出對話紀錄（.json）", "Export Chats": "匯出對話紀錄", "Export Config to JSON File": "將設定匯出為 JSON 檔案", "Export Functions": "匯出函式", "Export Models": "匯出模型", "Export Presets": "匯出預設集", "Export Prompt Suggestions": "匯出提示建議", "Export Prompts": "匯出提示詞", "Export to CSV": "匯出為 CSV", "Export Tools": "匯出工具", "Export Users": "匯出所有使用者資訊", "External": "外部", "External Document Loader URL required.": "需要提供外部文件載入器 URL。", "External Task Model": "外部任務模型", "External Web Loader API Key": "外部網頁載入器 API 金鑰", "External Web Loader URL": "外部網頁載入器 URL", "External Web Search API Key": "外部網路搜尋 API 金鑰", "External Web Search URL": "外部網路搜尋 URL", "Fade Effect for Streaming Text": "串流文字淡入效果", "Failed to add file.": "新增檔案失敗。", "Failed to connect to {{URL}} OpenAPI tool server": "無法連線至 {{URL}} OpenAPI 工具伺服器", "Failed to copy link": "複製連結失敗", "Failed to create API Key.": "建立 API 金鑰失敗。", "Failed to delete note": "刪除筆記失敗", "Failed to extract content from the file: {{error}}": "檔案內容擷取失敗：{{error}}", "Failed to extract content from the file.": "檔案內容擷取失敗", "Failed to fetch models": "取得模型失敗", "Failed to generate title": "產生標題失敗", "Failed to load chat preview": "對話預覽載入失敗", "Failed to load file content.": "載入檔案內容失敗。", "Failed to move chat": "移動對話失敗", "Failed to read clipboard contents": "讀取剪貼簿內容失敗", "Failed to save connections": "儲存連線失敗", "Failed to save conversation": "儲存對話失敗", "Failed to save models configuration": "儲存模型設定失敗", "Failed to update settings": "更新設定失敗", "Failed to upload file.": "上傳檔案失敗。", "Features": "功能", "Features Permissions": "功能權限", "February": "2 月", "Feedback Details": "回饋詳情", "Feedback History": "回饋歷史", "Feedbacks": "回饋", "Feel free to add specific details": "歡迎自由新增特定細節", "Female": "女性", "File": "檔案", "File added successfully.": "成功新增檔案。", "File content updated successfully.": "成功更新檔案內容。", "File Mode": "檔案模式", "File not found.": "未找到檔案。", "File removed successfully.": "成功移除檔案。", "File size should not exceed {{maxSize}} MB.": "檔案大小不應超過 {{maxSize}} MB。", "File Upload": "檔案上傳", "File uploaded successfully": "成功上傳檔案", "Files": "檔案", "Filter": "篩選", "Filter is now globally disabled": "篩選器已全域停用", "Filter is now globally enabled": "篩選器已全域啟用", "Filters": "篩選器", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "偵測到指紋偽造：無法使用姓名縮寫作為大頭貼。將預設為預設個人檔案圖片。", "Firecrawl API Base URL": "Firecrawl API 基底 URL", "Firecrawl API Key": "Firecrawl API 金鑰", "Floating Quick Actions": "浮動快速操作", "Focus chat input": "聚焦對話輸入", "Folder deleted successfully": "成功刪除資料夾", "Folder Name": "分組名稱", "Folder name cannot be empty.": "資料夾名稱不能為空。", "Folder name updated successfully": "成功更新資料夾名稱", "Folder updated successfully": "分組更新成功", "Follow up": "跟進", "Follow Up Generation": "跟進內容生成", "Follow Up Generation Prompt": "跟進內容生成提示詞", "Follow-Up Auto-Generation": "跟進內容自動生成", "Followed instructions perfectly": "完全遵循指示", "Force OCR": "強制執行 OCR", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "強制對 PDF 所有頁面執行 OCR。若原始 PDF 文字品質良好，此功能可能降低準確度。預設為 False。", "Forge new paths": "開創新路徑", "Form": "表單", "Format Lines": "行內容格式化", "Format the lines in the output. Defaults to False. If set to True, the lines will be formatted to detect inline math and styles.": "對輸出中的文字行進行格式處理。預設為 False。設定為 True 時，將會格式化這些文字行，以偵測並識別行內數學公式和樣式。", "Format your variables using brackets like this:": "使用方括號格式化您的變數，如下所示：", "Formatting may be inconsistent from source.": "", "Forwards system user session credentials to authenticate": "轉發系統使用者 session 憑證以進行驗證", "Full Context Mode": "完整上下文模式", "Function": "函式", "Function Calling": "函式呼叫", "Function created successfully": "成功建立函式", "Function deleted successfully": "成功刪除函式", "Function Description": "函式描述", "Function ID": "函式 ID", "Function imported successfully": "成功匯入函式", "Function is now globally disabled": "已全域停用函式", "Function is now globally enabled": "已全域啟用函式", "Function Name": "函式名稱", "Function updated successfully": "成功更新函式", "Functions": "函式", "Functions allow arbitrary code execution.": "函式允許執行任意程式碼。", "Functions imported successfully": "成功匯入函式", "Gemini": "Gemini", "Gemini API Config": "Gemini API 設定", "Gemini API Key is required.": "需要提供 Gemini API 金鑰。", "Gender": "性別", "General": "一般", "Generate": "生成", "Generate an image": "生成圖片", "Generate Image": "生成圖片", "Generate prompt pair": "生成提示配對", "Generating search query": "正在生成搜尋查詢", "Generating...": "正在生成...", "Get information on {{name}} in the UI": "在介面中取得 {{name}} 的資訊", "Get started": "開始使用", "Get started with {{WEBUI_NAME}}": "開始使用 {{WEBUI_NAME}}", "Global": "全域", "Good Response": "回應良好", "Google Drive": "Google Drive", "Google PSE API Key": "Google PSE API 金鑰", "Google PSE Engine Id": "Google PSE 引擎 ID", "Gravatar": "Gravatar 大頭貼", "Group": "群組", "Group created successfully": "成功建立群組", "Group deleted successfully": "成功刪除群組", "Group Description": "群組描述", "Group Name": "群組名稱", "Group updated successfully": "成功更新群組", "Groups": "群組", "H1": "一級標題", "H2": "二級標題", "H3": "三級標題", "Haptic Feedback": "觸覺回饋", "Height": "高度", "Hello, {{name}}": "您好，{{name}}", "Help": "說明", "Help us create the best community leaderboard by sharing your feedback history!": "透過分享您的回饋歷史，幫助我們建立最佳的社群排行榜！", "Hex Color": "Hex 顔色", "Hex Color - Leave empty for default color": "Hex 顔色 —— 留空以使用預設顔色", "Hide": "隱藏", "Hide from Sidebar": "從側邊欄隱藏", "Hide Model": "隱藏模型", "High": "高", "High Contrast Mode": "高對比模式", "Home": "首頁", "Host": "主機", "How can I help you today?": "今天我能為您做些什麼？", "How would you rate this response?": "您如何評價此回應？", "HTML": "HTML", "Hybrid Search": "混合搜尋", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "我確認已閱讀並理解我的操作所帶來的影響。我了解執行任意程式碼的相關風險，並已驗證來源的可信度。", "ID": "ID", "iframe Sandbox Allow Forms": "iframe 沙盒允許表單", "iframe Sandbox Allow Same Origin": "iframe 沙盒允許同源", "Ignite curiosity": "點燃好奇心", "Image": "圖片", "Image Compression": "圖片壓縮", "Image Compression Height": "圖片壓縮高度", "Image Compression Width": "圖片壓縮寬度", "Image Generation": "圖片生成", "Image Generation (Experimental)": "圖片生成（實驗性功能）", "Image Generation Engine": "圖片生成引擎", "Image Max Compression Size": "圖片最大壓縮大小", "Image Max Compression Size height": "圖片最大壓縮高度", "Image Max Compression Size width": "圖片最大壓縮寬度", "Image Prompt Generation": "圖片提示詞生成", "Image Prompt Generation Prompt": "生成圖片提示詞的提示詞", "Image Settings": "圖片設定", "Images": "圖片", "Import": "匯入", "Import Chats": "匯入對話紀錄", "Import Config from JSON File": "從 JSON 檔案匯入設定", "Import From Link": "從連結匯入", "Import Functions": "匯入函式", "Import Models": "匯入模型", "Import Notes": "匯入筆記", "Import Presets": "匯入預設集", "Import Prompt Suggestions": "匯入提示建議", "Import Prompts": "匯入提示詞", "Import Tools": "匯入工具", "Important Update": "重要更新", "Include": "包含", "Include `--api-auth` flag when running stable-diffusion-webui": "執行 stable-diffusion-webui 時包含 `--api-auth` 參數", "Include `--api` flag when running stable-diffusion-webui": "執行 stable-diffusion-webui 時包含 `--api` 參數", "Includes SharePoint": "包含 SharePoint", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "影響演算法對生成文字回饋的反應速度。較低的學習率會導致調整速度較慢，而較高的學習率會使演算法反應更靈敏。", "Info": "資訊", "Initials": "姓名縮寫", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "將完整內容注入為上下文以進行全面處理，建議用於複雜查詢。", "Input": "輸入", "Input commands": "輸入命令", "Input Key (e.g. text, unet_name, steps)": "輸入鍵名（例如：text, unet_name, steps）", "Input Variables": "插入變數", "Insert": "插入", "Insert Follow-Up Prompt to Input": "插入追問提示詞到輸入框", "Insert Prompt as Rich Text": "以富文字格式插入提示詞", "Install from Github URL": "從 GitHub URL 安裝", "Instant Auto-Send After Voice Transcription": "語音轉錄後立即自動傳送", "Integration": "整合", "Interface": "介面", "Invalid file content": "檔案內容無效", "Invalid file format.": "檔案格式無效。", "Invalid JSON file": "JSON 檔案無效", "Invalid JSON format for ComfyUI Workflow.": "ComfyUI 工作流程的 JSON 格式無效", "Invalid JSON format in Additional Config": "額外設定中的 JSON 格式無效", "Invalid Tag": "無效標籤", "is typing...": "正在輸入...", "Italic": "斜體", "January": "1 月", "Jina API Key": "Jina API 金鑰", "join our Discord for help.": "加入我們的 Discord 以取得協助。", "JSON": "JSON", "JSON Preview": "JSON 預覽", "July": "7 月", "June": "6 月", "Jupyter Auth": "<PERSON><PERSON><PERSON> 驗證", "Jupyter URL": "Jupyter URL", "JWT Expiration": "JWT 過期時間", "JWT Token": "JWT Token", "Kagi Search API Key": "Kagi 搜尋 API 金鑰", "Keep Follow-Up Prompts in Chat": "保留追問提示詞在對話中", "Keep in Sidebar": "保留在側邊欄", "Key": "金鑰", "Key is required": "金鑰為必填項目", "Keyboard shortcuts": "鍵盤快捷鍵", "Knowledge": "知識", "Knowledge Access": "知識存取", "Knowledge Base": "知識庫", "Knowledge created successfully.": "成功建立知識。", "Knowledge deleted successfully.": "成功刪除知識。", "Knowledge Description": "知識庫描述", "Knowledge Name": "知識庫名稱", "Knowledge Public Sharing": "知識公開分享", "Knowledge reset successfully.": "成功重設知識。", "Knowledge updated successfully": "成功更新知識", "Kokoro.js (Browser)": "Kokoro.js (瀏覽器)", "Kokoro.js Dtype": "Kokoro.js Dtype", "Label": "標籤", "Landing Page Mode": "首頁模式", "Language": "語言", "Language Locales": "語言區域設定", "Last Active": "最近活動時間", "Last Modified": "上次修改時間", "Last reply": "上次回覆", "LDAP": "LDAP", "LDAP server updated": "LDAP 伺服器已更新", "Leaderboard": "排行榜", "Learn More": "了解更多", "Learn more about OpenAPI tool servers.": "進一步了解 OpenAPI 工具伺服器。", "Leave empty for no compression": "留空則不壓縮", "Leave empty for unlimited": "留空表示無限制", "Leave empty to include all models from \"{{url}}\" endpoint": "留空以包含來自「{{url}}」端點的所有模型", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "留空以包含來自 \"{{url}}/api/tags\" 端點的所有模型。", "Leave empty to include all models from \"{{url}}/models\" endpoint": "留空以包含來自 \"{{url}}/models\" 端點的所有模型。", "Leave empty to include all models or select specific models": "留空以包含所有模型或選擇特定模型", "Leave empty to use the default prompt, or enter a custom prompt": "留空以使用預設提示詞，或輸入自訂提示詞", "Leave model field empty to use the default model.": "留空模型欄位以使用預設模型。", "lexical": "關鍵詞", "License": "授權", "Lift List": "上移清單", "Light": "淺色", "Listening...": "正在聆聽...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "大型語言模型可能會犯錯。請自行驗證重要資訊。", "Loader": "載入工具", "Loading Kokoro.js...": "Kokoro.js 載入中...", "Loading...": "正在載入...", "Local": "本機", "Local Task Model": "本機任務模型", "Location access not allowed": "位置存取未獲允許", "Lost": "落敗", "Low": "低", "LTR": "從左到右", "Made by Open WebUI Community": "由 Open WebUI 社群製作", "Make password visible in the user interface": "在使用者介面中顯示密碼", "Make sure to enclose them with": "請務必將它們放在", "Make sure to export a workflow.json file as API format from ComfyUI.": "請確保從 ComfyUI 匯出 workflow.json 檔案為 API 格式。", "Male": "男性", "Manage": "管理", "Manage Direct Connections": "管理直接連線", "Manage Models": "管理模型", "Manage Ollama": "管理 <PERSON><PERSON>ma", "Manage Ollama API Connections": "管理 Ollama API 連線", "Manage OpenAI API Connections": "管理 OpenAI API 連線", "Manage Pipelines": "管理管線", "Manage Tool Servers": "管理工具伺服器", "Manage your account information.": "管理您的帳號資訊。", "March": "3 月", "Markdown": "<PERSON><PERSON>", "Markdown (Header)": "Markdown（標題）", "Max Speakers": "最大發言者數量", "Max Upload Count": "最大上傳數量", "Max Upload Size": "最大上傳大小", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "最多同時下載 3 個模型。請稍後再試。", "May": "5 月", "Medium": "中", "Memories accessible by LLMs will be shown here.": "可被大型語言模型存取的記憶將顯示在此。", "Memory": "記憶", "Memory added successfully": "成功新增記憶", "Memory cleared successfully": "成功清除記憶", "Memory deleted successfully": "成功刪除記憶", "Memory updated successfully": "成功更新記憶", "Merge Responses": "合併回應", "Merged Response": "整合回應結果", "Message rating should be enabled to use this feature": "需要啟用訊息評分才能使用此功能", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "建立連結後傳送的訊息不會被分享。擁有網址的使用者可檢視分享的對話內容。", "Microsoft OneDrive": "Microsoft OneDrive", "Microsoft OneDrive (personal)": "Microsoft OneDrive（個人版）", "Microsoft OneDrive (work/school)": "Microsoft OneDrive（公司版／學校版）", "Mistral OCR": "Mistral OCR", "Mistral OCR API Key required.": "需要提供 Mistral OCR API 金鑰。", "Model": "模型", "Model '{{modelName}}' has been successfully downloaded.": "模型「{{modelName}}」已成功下載。", "Model '{{modelTag}}' is already in queue for downloading.": "模型「{{modelTag}}」已在下載佇列中。", "Model {{modelId}} not found": "未找到模型 {{modelId}}", "Model {{modelName}} is not vision capable": "模型 {{modelName}} 不具備視覺能力", "Model {{name}} is now {{status}}": "模型 {{name}} 現在狀態為 {{status}}", "Model {{name}} is now hidden": "模型 {{name}} 已隱藏", "Model {{name}} is now visible": "模型 {{name}} 已顯示", "Model accepts file inputs": "模型支援檔案輸入", "Model accepts image inputs": "模型接受影像輸入", "Model can execute code and perform calculations": "模型可執行程式碼並進行運算", "Model can generate images based on text prompts": "模型可基於文字提示詞生成影像", "Model can search the web for information": "模型可透過網路搜尋資訊", "Model created successfully!": "成功建立模型！", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "偵測到模型檔案系統路徑。更新需要模型簡稱，因此無法繼續。", "Model Filtering": "模型篩選", "Model ID": "模型 ID", "Model ID is required.": "模型 ID 是必填項。", "Model IDs": "模型 IDs", "Model Name": "模型名稱", "Model name already exists, please choose a different one": "模型名稱已存在，請使用不同的名稱", "Model Name is required.": "模型名稱是必填項。", "Model not selected": "未選取模型", "Model Params": "模型參數", "Model Permissions": "模型權限", "Model unloaded successfully": "成功卸載模型", "Model updated successfully": "成功更新模型", "Model(s) do not support file upload": "模型不支援檔案上傳", "Modelfile Content": "模型檔案內容", "Models": "模型", "Models Access": "模型存取", "Models configuration saved successfully": "成功儲存模型設定", "Models Public Sharing": "模型公開分享", "Mojeek Search API Key": "Mojeek 搜尋 API 金鑰", "more": "更多", "More": "更多", "More Concise": "精煉表達", "More Options": "更多選項", "Move": "移動", "Name": "名稱", "Name and ID are required, please fill them out": "名稱和 ID 為必填項目，請填寫", "Name your knowledge base": "命名您的知識庫", "Native": "原生", "New Button": "新按鈕", "New Chat": "新增對話", "New Folder": "新增資料夾", "New Function": "新增函式", "New Note": "新增筆記", "New Password": "新密碼", "New Tool": "新增工具", "new-channel": "new-channel", "Next message": "下一條訊息", "No chats found": "未找到對話記錄", "No chats found for this user.": "未找到此使用者的對話記錄。", "No chats found.": "未找到對話記錄。", "No content": "無內容", "No content found": "未找到內容", "No content found in file.": "檔案中未找到內容。", "No content to speak": "無可朗讀的內容", "No conversation to save": "沒有可儲存的對話", "No distance available": "無可用距離", "No feedbacks found": "未找到回饋", "No file selected": "未選取檔案", "No groups with access, add a group to grant access": "沒有具有存取權限的群組，新增群組以授予存取權限", "No HTML, CSS, or JavaScript content found.": "未找到 HTML、CSS 或 JavaScript 內容。", "No inference engine with management support found": "未找到支援管理功能的推理引擎", "No knowledge found": "未找到知識", "No memories to clear": "沒有記憶可清除", "No model IDs": "沒有模型 ID", "No models found": "未找到模型", "No models selected": "未選取模型", "No Notes": "尚無筆記", "No results": "沒有結果", "No results found": "未找到任何結果", "No search query generated": "未產生搜尋查詢", "No source available": "無可用來源", "No suggestion prompts": "沒有建議提示詞", "No users were found.": "未找到任何使用者", "No valves": "沒有設定項目", "No valves to update": "設定項目可更新", "Node Ids": "節點 ID", "None": "無", "Not factually correct": "與事實不符", "Not helpful": "沒有幫助", "Note deleted successfully": "已成功刪除筆記", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "注意：如果您設定了最低分數，則搜尋只會回傳分數大於或等於最低分數的檔案。", "Notes": "筆記", "Notification Sound": "通知聲音", "Notification Webhook": "通知 Webhook", "Notifications": "通知", "November": "11 月", "OAuth ID": "OAuth ID", "October": "10 月", "Off": "關閉", "Okay, Let's Go!": "好的，我們開始吧！", "OLED Dark": "OLED 深色", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "Ollama API 設定已更新", "Ollama Version": "Ollama 版本", "On": "開啟", "OneDrive": "OneDrive", "Only active when \"Paste Large Text as File\" setting is toggled on.": "僅在啟用「貼上大文字為檔案」功能時生效。", "Only active when the chat input is in focus and an LLM is generating a response.": "僅在對話輸入框啟用且大語言模型正在產生回應時生效。", "Only alphanumeric characters and hyphens are allowed": "只允許使用英文字母、數字和連字號", "Only alphanumeric characters and hyphens are allowed in the command string.": "命令字串中只允許使用英文字母、數字和連字號。", "Only collections can be edited, create a new knowledge base to edit/add documents.": "只能編輯集合，請建立新的知識以編輯或新增檔案。", "Only markdown files are allowed": "僅允許 Markdown 檔案", "Only select users and groups with permission can access": "只有具有權限的選定使用者和群組可以存取", "Oops! Looks like the URL is invalid. Please double-check and try again.": "哎呀！這個 URL 似乎無效。請仔細檢查並再試一次。", "Oops! There are files still uploading. Please wait for the upload to complete.": "哎呀！還有檔案正在上傳。請等候上傳完畢。", "Oops! There was an error in the previous response.": "哎呀！之前的回應有一處錯誤。", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "哎呀！您使用了不支援的方法（僅限前端）。請從後端提供 WebUI。", "Open file": "開啟檔案", "Open in full screen": "全螢幕開啟", "Open modal to configure connection": "開啟外部連線設定彈出視窗", "Open Modal To Manage Floating Quick Actions": "開啟管理浮動快速操作的彈出視窗", "Open new chat": "開啟新的對話", "Open Sidebar": "展開側邊欄", "Open User Profile Menu": "開啟個人資料選單", "Open WebUI can use tools provided by any OpenAPI server.": "Open WebUI 可使用任何 OpenAPI 伺服器提供的工具。", "Open WebUI uses faster-whisper internally.": "Open WebUI 使用內部 faster-whisper。", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI 使用 SpeechT5 和 CMU Arctic 說話者嵌入。", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Open WebUI 版本 (v{{OPEN_WEBUI_VERSION}}) 低於所需版本 (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API 設定", "OpenAI API Key is required.": "需要提供 OpenAI API 金鑰。", "OpenAI API settings updated": "OpenAI API 設定已更新", "OpenAI URL/Key required.": "需要提供 OpenAI URL 或金鑰。", "openapi.json URL or Path": "openapi.json URL 或路徑", "Optional": "選填", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "在圖片描述功能中本地執行視覺語言模型的選項。此參數指向託管在 Hugging Face 上的模型。此參數不可與 picture_description_api 同時使用。", "or": "或", "Ordered List": "有序清單", "Organize your users": "組織您的使用者", "Other": "其他", "OUTPUT": "輸出", "Output format": "輸出格式", "Output Format": "輸出格式", "Overview": "概覽", "page": "頁面", "Paginate": "啟用分頁", "Parameters": "參數", "Password": "密碼", "Passwords do not match.": "兩次輸入的密碼不一致。", "Paste Large Text as File": "將大型文字以檔案貼上", "PDF document (.pdf)": "PDF 檔案 (.pdf)", "PDF Extract Images (OCR)": "PDF 影像擷取（OCR 光學文字辨識）", "pending": "待處理", "Pending": "待處理", "Pending User Overlay Content": "待處理的使用者訊息覆蓋層內容", "Pending User Overlay Title": "待處理的使用者訊息覆蓋層標題", "Permission denied when accessing media devices": "存取媒體裝置時權限遭拒", "Permission denied when accessing microphone": "存取麥克風時權限遭拒", "Permission denied when accessing microphone: {{error}}": "存取麥克風時權限遭拒：{{error}}", "Permissions": "權限", "Perplexity API Key": "Perplexity API 金鑰", "Perplexity Model": "Perplexity 模型", "Perplexity Search Context Usage": "Perplexity 搜尋上下文使用量", "Personalization": "個人化", "Picture Description API Config": "圖片描述 API 設定", "Picture Description Local Config": "圖片描述本地設定", "Picture Description Mode": "圖片描述模式", "Pin": "釘選", "Pinned": "已釘選", "Pioneer insights": "先驅見解", "Pipe": "<PERSON><PERSON>", "Pipeline deleted successfully": "成功刪除管線", "Pipeline downloaded successfully": "成功下載管線", "Pipelines": "管線", "Pipelines are a plugin system with arbitrary code execution —": "管線是具任意程式碼執行風險的外掛系統 —", "Pipelines Not Detected": "未偵測到管線", "Pipelines Valves": "管線設定項目", "Plain text (.md)": "純文字 (.md)", "Plain text (.txt)": "純文字 (.txt)", "Playground": "遊樂場", "Playwright Timeout (ms)": "Playwright 逾時時間（毫秒）", "Playwright WebSocket URL": "Playwright WebSocket URL", "Please carefully review the following warnings:": "請仔細閱讀以下警告：", "Please do not close the settings page while loading the model.": "載入模型時，請勿關閉設定頁面。", "Please enter a message or attach a file.": "請輸入訊息或附加檔案。", "Please enter a prompt": "請輸入提示詞", "Please enter a valid path": "請輸入有效路徑", "Please enter a valid URL": "請輸入有效 URL", "Please fill in all fields.": "請填寫所有欄位。", "Please select a model first.": "請先選擇模型。", "Please select a model.": "請選擇一個模型。", "Please select a reason": "請選擇原因", "Please wait until all files are uploaded.": "請等待所有檔案上傳完畢。", "Port": "連接埠", "Positive attitude": "積極的態度", "Prefer not to say": "不想透露", "Prefix ID": "前置 ID", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "前置 ID 用於透過為模型 ID 新增字首以避免與其他連線衝突 - 留空以停用", "Prevent file creation": "阻止檔案建立", "Preview": "預覽", "Previous 30 days": "過去 30 天", "Previous 7 days": "過去 7 天", "Previous message": "過去訊息", "Private": "私有", "Profile": "個人檔案", "Prompt": "提示詞", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "提示詞（例如：告訴我關於羅馬帝國的一些趣事）", "Prompt Autocompletion": "提示詞自動完成", "Prompt Content": "提示詞內容", "Prompt created successfully": "成功建立提示詞", "Prompt suggestions": "提示詞建議", "Prompt updated successfully": "成功更新提示詞", "Prompts": "提示詞", "Prompts Access": "提示詞存取", "Prompts Public Sharing": "提示詞公開分享", "Public": "公開", "Pull \"{{searchValue}}\" from Ollama.com": "從 Ollama.com 下載「{{searchValue}}」", "Pull a model from Ollama.com": "從 Ollama.com 下載模型", "Query Generation Prompt": "查詢生成提示詞", "Quick Actions": "快速操作", "RAG Template": "RAG 範本", "Rating": "評分", "Re-rank models by topic similarity": "根據主題相似度重新排序模型", "Read": "讀取", "Read Aloud": "大聲朗讀", "Reason": "原因", "Reasoning Effort": "推理程度", "Reasoning Tags": "", "Record": "錄製", "Record voice": "錄音", "Redirecting you to Open WebUI Community": "正在將您重導向至 Open WebUI 社群", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "降低產生無意義內容的機率。較高的值（例如：100）會產生更多樣化的答案，而較低的值（例如：10）會更保守。", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "以「使用者」稱呼自己（例如：「使用者正在學習西班牙文」）", "References from": "引用來源", "Refused when it shouldn't have": "不應拒絕時拒絕了", "Regenerate": "重新產生回應", "Regenerate Menu": "重新產生前顯示選單", "Reindex": "重新索引", "Reindex Knowledge Base Vectors": "重新索引知識庫向量", "Release Notes": "版本資訊", "Releases": "版本資訊", "Relevance": "相關性", "Relevance Threshold": "相關性閾值", "Remember Dismissal": "記住關閉狀態", "Remove": "移除", "Remove {{MODELID}} from list.": "從清單中移除 {{MODELID}}", "Remove file": "移除檔案", "Remove File": "移除檔案", "Remove image": "移除圖片", "Remove Model": "移除模型", "Remove this tag from list": "從清單中移除此標籤", "Rename": "重新命名", "Reorder Models": "重新排序模型", "Reply in Thread": "在討論串中回覆", "Reranking Engine": "重新排序引擎", "Reranking Model": "重新排序模型", "Reset": "重設", "Reset All Models": "重設所有模型", "Reset Image": "重設圖片", "Reset Upload Directory": "重設上傳目錄", "Reset Vector Storage/Knowledge": "重設向量儲存或知識", "Reset view": "重設檢視", "Response": "回應", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "無法啟用回應通知，因為網站權限已遭拒。請前往瀏覽器設定以授予必要存取權限。", "Response splitting": "回應分割", "Response Watermark": "回應浮水印", "Result": "結果", "RESULT": "結果", "Retrieval": "檢索", "Retrieval Query Generation": "檢索查詢生成", "Rich Text Input for Chat": "使用富文字輸入對話", "RK": "RK", "Role": "角色", "Rosé Pine": "玫瑰松", "Rosé Pine Dawn": "黎明玫瑰松", "RTL": "從右到左", "Run": "執行", "Running": "正在執行", "Running...": "正在執行...", "Save": "儲存", "Save & Create": "儲存並建立", "Save & Update": "儲存並更新", "Save As Copy": "另存為副本", "Save Chat": "儲存對話", "Save Tag": "儲存標籤", "Saved": "已儲存", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "不再支援直接將對話紀錄儲存到您的瀏覽器儲存空間。請點選下方按鈕來下載並刪除您的對話紀錄。別擔心，您可以透過以下方式輕鬆地將對話紀錄重新匯入後端", "Scroll On Branch Change": "切換分支時自動捲動", "Search": "搜尋", "Search a model": "搜尋模型", "Search all emojis": "搜尋 Emoji 表情符號", "Search Base": "搜尋基礎", "Search Chats": "搜尋對話", "Search Collection": "搜尋集合", "Search Filters": "搜尋篩選器", "search for archived chats": "搜尋已封存的聊天", "search for folders": "搜尋分組", "search for pinned chats": "搜尋已釘選的聊天", "search for shared chats": "搜尋已分享的聊天", "search for tags": "搜尋標籤", "Search Functions": "搜尋函式", "Search In Models": "在模型中搜尋", "Search Knowledge": "搜尋知識庫", "Search Models": "搜尋模型", "Search Notes": "搜尋筆記", "Search options": "搜尋選項", "Search Prompts": "搜尋提示詞", "Search Result Count": "搜尋結果數量", "Search the internet": "搜尋網際網路", "Search Tools": "搜尋工具", "SearchApi API Key": "SearchApi API 金鑰", "SearchApi Engine": "SearchApi 引擎", "Searched {{count}} sites": "搜尋到 {{count}} 個網站", "Searching \"{{searchQuery}}\"": "正在搜尋「{{searchQuery}}」", "Searching Knowledge for \"{{searchQuery}}\"": "正在搜尋知識庫中的「{{searchQuery}}」", "Searching the web...": "正在搜尋網路...", "Searxng Query URL": "Searxng 查詢 URL", "See readme.md for instructions": "檢視 readme.md 以取得說明", "See what's new": "檢視新功能", "Seed": "種子值", "Select": "選擇", "Select a base model": "選擇基礎模型", "Select a base model (e.g. llama3, gpt-4o)": "選擇基礎模型（例如：llama3, gpt-4o）", "Select a conversation to preview": "選擇對話進行預覽", "Select a engine": "選擇引擎", "Select a function": "選擇函式", "Select a group": "選擇群組", "Select a language": "選擇語言", "Select a mode": "選擇模式", "Select a model": "選擇模型", "Select a model (optional)": "選擇模型（可選）", "Select a pipeline": "選擇管線", "Select a pipeline url": "選擇管線 URL", "Select a reranking model engine": "選擇重新排序模型引擎", "Select a role": "選擇角色", "Select a theme": "選擇主題", "Select a tool": "選擇工具", "Select a voice": "選擇語音", "Select an auth method": "選擇驗證方式", "Select an embedding model engine": "選擇嵌入模型引擎", "Select an engine": "選擇引擎", "Select an Ollama instance": "選擇一個 Ollama 執行個體", "Select an output format": "選擇輸出格式", "Select dtype": "選擇資料類型（Dtype）", "Select Engine": "選擇引擎", "Select how to split message text for TTS requests": "選擇如何拆分訊息文字以用於 TTS 請求", "Select Knowledge": "選擇知識庫", "Select only one model to call": "僅選擇一個模型來呼叫", "Selected model(s) do not support image inputs": "選取的模型不支援圖片輸入", "semantic": "語義", "Semantic distance to query": "與查詢的語義距離", "Send": "傳送", "Send a Message": "傳送訊息", "Send message": "傳送訊息", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "在請求中傳送 `stream_options: { include_usage: true }`。\n設定後，支援的提供者將在回應中回傳權杖使用資訊。", "September": "9 月", "SerpApi API Key": "SerpApi API 金鑰", "SerpApi Engine": "SerpApi 引擎", "Serper API Key": "Serper API 金鑰", "Serply API Key": "Serply API 金鑰", "Serpstack API Key": "Serpstack API 金鑰", "Server connection verified": "伺服器連線已驗證", "Session": "Session", "Set as default": "設為預設", "Set CFG Scale": "設定 CFG 比例", "Set Default Model": "設定預設模型", "Set embedding model": "設定嵌入模型", "Set embedding model (e.g. {{model}})": "設定嵌入模型（例如：{{model}}）", "Set Image Size": "設定圖片尺寸", "Set reranking model (e.g. {{model}})": "設定重新排序模型（例如：{{model}}）", "Set Sampler": "設定取樣器", "Set Scheduler": "設定排程器", "Set Steps": "設定步數", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "設定要卸載至 GPU 的層數。增加此數值可以顯著提升針對 GPU 加速最佳化的模型的效能，但也可能消耗更多電力和 GPU 資源。", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "設定用於計算的工作執行緒數量。此選項控制使用多少執行緒來同時處理傳入的請求。增加此值可以在高併發工作負載下提升效能，但也可能消耗更多 CPU 資源。", "Set Voice": "設定語音", "Set whisper model": "設定 whisper 模型", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "對至少出現過一次的 token 設定統一的偏差值。較高的值（例如：1.5）會更強烈地懲罰重複，而較低的值（例如：0.9）會更寬容。設為 0 時，此功能將停用。", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "根據 token 出現的次數，設定一個縮放偏差值來懲罰重複。較高的值（例如：1.5）會更強烈地懲罰重複，而較低的值（例如：0.9）會更寬容。設為 0 時，此功能將停用。", "Sets how far back for the model to look back to prevent repetition.": "設定模型要回溯多遠來防止重複。", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "設定用於生成的隨機數種子。將其設定為特定數字將使模型針對相同的提示生成相同的文字。", "Sets the size of the context window used to generate the next token.": "設定用於生成下一個 token 的上下文視窗大小。", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "設定要使用的停止序列。當遇到此模式時，大型語言模型將停止生成文字並返回。可以在模型檔案中指定多個單獨的停止參數來設定多個停止模式。", "Settings": "設定", "Settings saved successfully!": "設定已成功儲存！", "Share": "分享", "Share Chat": "分享對話", "Share to Open WebUI Community": "分享到 Open WebUI 社群", "Share your background and interests": "分享您的背景與興趣", "Sharing Permissions": "分享權限設定", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "帶星號 (*) 的快捷鍵受場景限制，僅在特定條件下生效。", "Show": "顯示", "Show \"What's New\" modal on login": "登入時顯示「新功能」對話框", "Show Admin Details in Account Pending Overlay": "在帳號待審覆蓋層中顯示管理員詳細資訊", "Show All": "顯示全部", "Show Formatting Toolbar": "顯示文字格式工具列", "Show image preview": "顯示圖片預覽", "Show Less": "顯示較少", "Show Model": "顯示模型", "Show shortcuts": "顯示快捷鍵", "Show your support!": "表達您的支持！", "Showcased creativity": "展現創意", "Sign in": "登入", "Sign in to {{WEBUI_NAME}}": "登入 {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "以 LDAP 登入 {{WEBUI_NAME}}", "Sign Out": "登出", "Sign up": "註冊", "Sign up to {{WEBUI_NAME}}": "註冊 {{WEBUI_NAME}}", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to False.": "借助大語言模型（LLM）提升表格、表單、行內數學公式及版面偵測的準確性，但會增加回應時間。預設值：False。", "Signing in to {{WEBUI_NAME}}": "正在登入 {{WEBUI_NAME}}", "Sink List": "下移清單", "sk-1234": "sk-1234", "Skip Cache": "略過快取", "Skip the cache and re-run the inference. Defaults to False.": "略過快取並重新執行推理。預設為 False。", "Something went wrong :/": "發生錯誤 :/", "Sonar": "Sonar", "Sonar Deep Research": "Sonar Deep Research", "Sonar Pro": "Sonar Pro", "Sonar Reasoning": "Sonar Reasoning", "Sonar Reasoning Pro": "Sonar Reasoning Pro", "Sougou Search API sID": "搜狗搜尋 API sID", "Sougou Search API SK": "搜狗搜尋 API SK", "Source": "來源", "Speech Playback Speed": "語音播放速度", "Speech recognition error: {{error}}": "語音辨識錯誤：{{error}}", "Speech-to-Text": "語音轉文字 (STT) ", "Speech-to-Text Engine": "語音轉文字 (STT) 引擎", "Start of the channel": "頻道起點", "Start Tag": "", "STDOUT/STDERR": "STDOUT/STDERR", "Stop": "停止", "Stop Generating": "停止生成", "Stop Sequence": "停止序列", "Stream Chat Response": "串流式對話回應", "Stream Delta Chunk Size": "串流增量輸出的分塊大小（Stream Delta Chunk Size）", "Strikethrough": "刪除線", "Strip Existing OCR": "移除現有 OCR 文字", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "移除 PDF 中現有的 OCR 文字並重新執行 OCR。若啟用「強制執行 OCR」則忽略此選項。預設為 False。", "STT Model": "語音轉文字 (STT) 模型", "STT Settings": "語音轉文字 (STT) 設定", "Stylized PDF Export": "風格化 PDF 匯出", "Subtitle (e.g. about the Roman Empire)": "副標題（例如：關於羅馬帝國）", "Success": "成功", "Successfully imported {{userCount}} users.": "成功匯入 {{userCount}} 個使用者", "Successfully updated.": "更新成功。", "Suggest a change": "提出修改建議", "Suggested": "建議", "Support": "支援", "Support this plugin:": "支持這個外掛：", "Supported MIME Types": "支援的 MIME 類型", "Sync directory": "同步目錄", "System": "系統", "System Instructions": "系統指令", "System Prompt": "系統提示詞", "Tags": "標籤", "Tags Generation": "標籤生成", "Tags Generation Prompt": "標籤生成提示詞", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "尾部自由取樣用於減少輸出結果中較低機率 token 的影響。較高的值（例如：2.0）會減少更多影響，而值為 1.0 時會停用此設定。", "Talk to model": "與模型對話", "Tap to interrupt": "點選以中斷", "Task List": "工作清單", "Task Model": "任務模型", "Tasks": "任務", "Tavily API Key": "Tavily API 金鑰", "Tavily Extract Depth": "Tavily 提取深度", "Tell us more:": "告訴我們更多：", "Temperature": "溫度", "Temporary Chat": "臨時對話", "Temporary Chat by Default": "預設使用臨時對話", "Text Splitter": "文字分割器", "Text-to-Speech": "文字轉語音", "Text-to-Speech Engine": "文字轉語音引擎", "Thanks for your feedback!": "感謝您的回饋！", "The Application Account DN you bind with for search": "您綁定用於搜尋的應用程式帳號 DN", "The base to search for users": "搜尋使用者的基礎", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "批次大小決定一次處理多少文字請求。較高的批次大小可以提高模型的效能和速度，但也需要更多記憶體。", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "這個外掛背後的開發者是來自社群的熱情志願者。如果您覺得這個外掛很有幫助，請考慮為其開發做出貢獻。", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "評估排行榜基於 Elo 評分系統，並即時更新。", "The format to return a response in. Format can be json or a JSON schema.": "回應回傳格式。可為 json 或 JSON schema。", "The height in pixels to compress images to. Leave empty for no compression.": "圖片壓縮高度（像素）。留空則不壓縮。", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "輸入音訊的語言。以 ISO-639-1 格式（例如：en）提供輸入語言將提高準確性和減少延遲。留空則自動偵測語言。", "The LDAP attribute that maps to the mail that users use to sign in.": "對映到使用者用於登入的使用者郵箱的 LDAP 屬性。", "The LDAP attribute that maps to the username that users use to sign in.": "對映到使用者用於登入的使用者名稱的 LDAP 屬性。", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "排行榜目前處於測試階段，我們可能會在改進演算法時調整評分計算方式。", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "檔案大小上限（MB）。如果檔案大小超過此限制，檔案將不會被上傳。", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "對話中一次可使用的最大檔案數量。如果檔案數量超過此限制，檔案將不會被上傳。", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "文字輸出格式，可選擇「json」、「markdown」或「html」。預設為「markdown」。", "The passwords you entered don't quite match. Please double-check and try again.": "兩次輸入的密碼不一致。請再次檢查並重試。", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "分數應該是介於 0.0（0%）和 1.0（100%）之間的值。", "The stream delta chunk size for the model. Increasing the chunk size will make the model respond with larger pieces of text at once.": "模型在串流輸出時每次傳送的增量文字塊大小。數值越大，模型每次回傳的文字會更多。", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "模型的溫度。提高溫度會使模型更具創造性地回答。", "The Weight of BM25 Hybrid Search. 0 more lexical, 1 more semantic. Default 0.5": "BM25 混合檢索權重（輸入靠近 0 的數字會更傾向於關鍵詞搜尋，反之輸入靠近 1 的數字會更傾向於全語義搜尋，預設為 0.5）", "The width in pixels to compress images to. Leave empty for no compression.": "圖片壓縮寬度（像素）。留空則不壓縮。", "Theme": "主題", "Thinking...": "正在思考...", "This action cannot be undone. Do you wish to continue?": "此操作無法復原。您確定要繼續進行嗎？", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "此頻道建立於 {{createdAt}}。這是 {{channelName}} 頻道的起點。", "This chat won't appear in history and your messages will not be saved.": "此對話不會出現在歷史記錄中，且您的訊息將不被儲存。", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "這確保您寶貴的對話會安全地儲存到您的後端資料庫。謝謝！", "This feature is experimental and may be modified or discontinued without notice.": "此功能為實驗性功能，可能會在未經通知的情況下修改或停用。", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "這是一個實驗性功能，它可能無法如預期運作，並且可能會隨時變更。", "This model is not publicly available. Please select another model.": "此模型未開放公眾使用，請選擇其他模型。", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "此選項控制模型請求後在記憶體中保持載入狀態的時長（預設：5 分鐘）", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "此選項控制在重新整理上下文時保留多少 token。例如，如果設定為 2，則會保留對話上下文的最後 2 個 token。保留上下文有助於保持對話的連貫性，但也可能降低對新主題的回應能力。", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "此選項用於啟用或停用 Ollama 的推理功能，該功能允許模型在產生回應前進行思考。啟用後，模型需要花些時間處理對話上下文，從而產生更縝密的回應。", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "此選項設定模型在其回應中可以生成的最大 token 數量。增加此限制允許模型提供更長的答案，但也可能增加產生無用或不相關內容的可能性。", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "此選項將刪除集合中的所有現有檔案，並用新上傳的檔案取代它們。", "This response was generated by \"{{model}}\"": "此回應由「{{model}}」產生", "This will delete": "這將會刪除", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "這將會刪除 <strong>{{NAME}}</strong> 和<strong>其所有內容</strong>。", "This will delete all models including custom models": "這將刪除所有模型，包括自訂模型", "This will delete all models including custom models and cannot be undone.": "這將刪除所有模型，包括自訂模型，且無法復原。", "This will reset the knowledge base and sync all files. Do you wish to continue?": "這將重設知識庫並同步所有檔案。您確定要繼續嗎？", "Thorough explanation": "詳細解釋", "Thought for {{DURATION}}": "思考時間 {{DURATION}}", "Thought for {{DURATION}} seconds": "思考時間 {{DURATION}} 秒", "Thought for less than a second": "思考用時小於 1 秒", "Thread": "討論串", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "需要提供 Tika 伺服器 URL。", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "提示：在每次替換後按下對話輸入框中的 Tab 鍵，即可連續更新多個變數欄位。", "Title": "標題", "Title (e.g. Tell me a fun fact)": "標題（例如：告訴我一個有趣的事實）", "Title Auto-Generation": "自動產生標題", "Title cannot be an empty string.": "標題不能是空字串。", "Title Generation": "產生標題", "Title Generation Prompt": "產生標題的提示詞", "TLS": "TLS", "To access the available model names for downloading,": "若要存取可供下載的模型名稱，", "To access the GGUF models available for downloading,": "若要存取可供下載的 GGUF 模型，", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "若要存取 WebUI，請聯絡管理員。管理員可以從管理面板管理使用者狀態。", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "若要在此處附加知識庫，請先將它們新增到「知識」工作區。", "To learn more about available endpoints, visit our documentation.": "若要進一步了解可用的端點，請參閱我們的檔案。", "To learn more about powerful prompt variables, click here": "要了解更多關於強大的提示詞變數的資訊，請點擊此處", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "為了保護您的隱私，只會分享您回饋中的評分、模型 ID、標籤和中繼資料 —— 您的對話紀錄仍然是私密的，不會被包含在內。", "To select actions here, add them to the \"Functions\" workspace first.": "若要在此選擇動作，請先將它們新增到「函式」工作區。", "To select filters here, add them to the \"Functions\" workspace first.": "若要在此選擇篩選器，請先將它們新增到「函式」工作區。", "To select toolkits here, add them to the \"Tools\" workspace first.": "若要在此選擇工具包，請先將它們新增到「工具」工作區。", "Toast notifications for new updates": "快顯通知新的更新", "Today": "今天", "Toggle search": "切換搜尋", "Toggle settings": "切換設定", "Toggle sidebar": "切換側邊欄", "Toggle whether current connection is active.": "切換當前連接的啟用狀態", "Token": "Token", "Too verbose": "太過冗長", "Tool created successfully": "成功建立工具", "Tool deleted successfully": "成功刪除工具", "Tool Description": "工具描述", "Tool ID": "工具 ID", "Tool imported successfully": "成功匯入工具", "Tool Name": "工具名稱", "Tool Servers": "工具伺服器", "Tool updated successfully": "成功更新工具", "Tools": "工具", "Tools Access": "工具存取", "Tools are a function calling system with arbitrary code execution": "工具是一個具有任意程式碼執行功能的函式呼叫系統", "Tools Function Calling Prompt": "工具函式呼叫提示詞", "Tools have a function calling system that allows arbitrary code execution.": "工具具有允許執行任意程式碼的函式呼叫系統。", "Tools Public Sharing": "工具公開分享", "Top K": "Top K", "Top K Reranker": "Top K Reranker", "Transformers": "Transformers", "Trouble accessing Ollama?": "存取 Ollama 時遇到問題？", "Trust Proxy Environment": "信任代理環境", "Try Again": "重新產生", "TTS Model": "文字轉語音 (TTS) 模型", "TTS Settings": "文字轉語音 (TTS) 設定", "TTS Voice": "文字轉語音 (TTS) 聲音", "Type": "類型", "Type Hugging Face Resolve (Download) URL": "輸入 Hugging Face 的解析（下載）URL", "Uh-oh! There was an issue with the response.": "哎呀！回應出了點問題。", "UI": "使用者介面", "Unarchive All": "解除封存全部", "Unarchive All Archived Chats": "解除封存全部已封存對話", "Unarchive Chat": "解除封存對話", "Underline": "底線", "Unloads {{FROM_NOW}}": "於 {{FROM_NOW}} 後卸載", "Unlock mysteries": "解鎖謎題", "Unpin": "取消釘選", "Unravel secrets": "揭開秘密", "Unsupported file type.": "不支援的檔案類型", "Untagged": "取消標簽的", "Untitled": "未命名", "Update": "更新", "Update and Copy Link": "更新並複製連結", "Update for the latest features and improvements.": "更新以獲得最新功能和改進。", "Update password": "更新密碼", "Updated": "已更新", "Updated at": "更新於", "Updated At": "更新於", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "升級至授權方案以獲得更強大功能，包括客製化主題與品牌，和專屬支援。", "Upload": "上傳", "Upload a GGUF model": "上傳 GGUF 模型", "Upload Audio": "上傳音訊", "Upload directory": "上傳目錄", "Upload files": "上傳檔案", "Upload Files": "上傳檔案", "Upload Pipeline": "上傳管線", "Upload Progress": "上傳進度", "Upload Progress: {{uploadedFiles}}/{{totalFiles}} ({{percentage}}%)": "上傳進度：{{uploadedFiles}}/{{totalFiles}} ({{percentage}}%)", "URL": "URL", "URL is required": "URL 為必填項目", "URL Mode": "URL 模式", "Usage": "使用量", "Use '#' in the prompt input to load and include your knowledge.": "在提示詞輸入中使用 '#' 來載入並包含您的知識。", "Use groups to group your users and assign permissions.": "使用群組來組織您的使用者並分配權限。", "Use LLM": "使用 LLM", "Use no proxy to fetch page contents.": "不使用代理擷取頁面內容。", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "使用 http_proxy 和 https_proxy 環境變數指定的代理擷取頁面內容。", "user": "使用者", "User": "使用者", "User Groups": "使用者群組", "User location successfully retrieved.": "成功取得使用者位置。", "User menu": "使用者選單", "User Webhooks": "使用者 Webhooks", "Username": "使用者名稱", "Users": "使用者", "Using Entire Document": "使用完整文件", "Using Focused Retrieval": "使用聚焦檢索", "Using the default arena model with all models. Click the plus button to add custom models.": "正在使用預設競技場模型與所有模型。點選加號按鈕以新增自訂模型。", "Valid time units:": "有效的時間單位：", "Validate certificate": "驗證憑證有效性", "Valves": "設定項目", "Valves updated": "設定項目已更新", "Valves updated successfully": "設定項目成功更新", "variable": "變數", "Verify Connection": "驗證連線", "Verify SSL Certificate": "驗證 SSL 憑證", "Version": "版本", "Version {{selectedVersion}} of {{totalVersions}}": "第 {{selectedVersion}} 版，共 {{totalVersions}} 版", "View Replies": "檢視回覆", "View Result from **{{NAME}}**": "檢視來自 **{{NAME}}** 的結果", "Visibility": "可見度", "Vision": "視覺", "Voice": "語音", "Voice Input": "語音輸入", "Voice mode": "語音模式", "Warning": "警告", "Warning:": "警告：", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "警告：啟用此功能將允許使用者在伺服器上上傳任意程式碼。", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "警告：如果您更新或更改嵌入模型，您將需要重新匯入所有檔案。", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "警告：Jupyter 執行允許任意程式碼執行，構成嚴重安全風險 —— 請務必極度謹慎。", "Web": "網頁", "Web API": "網頁 API", "Web Loader Engine": "網頁載入引擎", "Web Search": "網頁搜尋", "Web Search Engine": "網頁搜尋引擎", "Web Search in Chat": "在對話中進行網頁搜尋", "Web Search Query Generation": "網頁搜尋查詢生成", "Webhook URL": "Webhook URL", "WebUI Settings": "WebUI 設定", "WebUI URL": "WebUI URL", "WebUI will make requests to \"{{url}}\"": "WebUI 將向 \"{{url}}\" 傳送請求", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI 將向 \"{{url}}/api/chat\" 傳送請求", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI 將向 \"{{url}}/chat/completions\" 傳送請求", "What are you trying to achieve?": "您正在試圖完成什麼？", "What are you working on?": "您現在的工作是什麼？", "What's New in": "新功能", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "啟用時，模型將即時回應每個對話訊息，在使用者傳送訊息後立即生成回應。此模式適用於即時對話應用程式，但在較慢的硬體上可能會影響效能。", "wherever you are": "無論您在何處", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "是否啟用輸出分頁功能，每頁會以水平線與頁碼分隔。預設為 False。", "Whisper (Local)": "Whisper（本機）", "Why?": "為什麼？", "Widescreen Mode": "寬螢幕模式", "Width": "寬度", "Won": "獲勝", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "與 top-k 一起使用。較高的值（例如：0.95）將產生更多樣化的文字，而較低的值（例如：0.5）將生成更集中和保守的文字。", "Workspace": "工作區", "Workspace Permissions": "工作區權限", "Write": "撰寫", "Write a prompt suggestion (e.g. Who are you?)": "撰寫提示詞建議（例如：你是誰？）", "Write a summary in 50 words that summarizes [topic or keyword].": "用 50 字寫一篇總結 [主題或關鍵字] 的摘要。", "Write something...": "寫一些什麼...", "Write your model system prompt content here\ne.g.) You are Mario from Super Mario Bros, acting as an assistant.": "請在此輸入您的模型系統提示內容\n例如：你是超級瑪利歐兄弟（Super Mario Bros）中的瑪利歐（Mario），扮演助理的角色。", "Yacy Instance URL": "Ya<PERSON> 執行個體 URL", "Yacy Password": "<PERSON><PERSON> 密碼", "Yacy Username": "Ya<PERSON> 使用者名稱", "Yesterday": "昨天", "You": "您", "You are currently using a trial license. Please contact support to upgrade your license.": "您目前使用的是試用授權。請聯絡支援以升級您的授權。", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "您一次最多只能與 {{maxCount}} 個檔案進行對話。", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "您可以透過下方的「管理」按鈕新增記憶，將您與大型語言模型的互動個人化，讓它們更有幫助並更符合您的需求。", "You cannot upload an empty file.": "您無法上傳空檔案", "You do not have permission to upload files.": "您沒有權限上傳檔案。", "You have no archived conversations.": "您沒有已封存的對話。", "You have shared this chat": "您已分享此對話", "You're a helpful assistant.": "您是一位樂於助人的助理。", "You're now logged in.": "您已登入。", "Your Account": "您的帳號", "Your account status is currently pending activation.": "您的帳號目前正在等待啟用。", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "您的所有貢獻將會直接交給外掛開發者；Open WebUI 不會收取任何百分比。然而，所選擇的贊助平臺可能有其自身的費用。", "Youtube": "YouTube", "Youtube Language": "YouTube 語言", "Youtube Proxy URL": "YouTube 代理伺服器網址"}