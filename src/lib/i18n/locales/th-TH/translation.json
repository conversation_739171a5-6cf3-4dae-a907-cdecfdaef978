{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' หรือ '-1' สำหรับไม่มีการหมดอายุ", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(เช่น `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(เช่น `sh webui.sh --api`)", "(latest)": "(ล่าสุด)", "(leave blank for to use commercial endpoint)": "", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "{{ models }}", "{{COUNT}} Available Tools": "", "{{COUNT}} characters": "", "{{COUNT}} extracted lines": "", "{{COUNT}} hidden lines": "", "{{COUNT}} Replies": "", "{{COUNT}} words": "", "{{model}} download has been canceled": "", "{{user}}'s Chats": "การสนทนาของ {{user}}", "{{webUIName}} Backend Required": "ต้องการ Backend ของ {{webUIName}}", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "ใช้โมเดลงานเมื่อทำงานเช่นการสร้างหัวข้อสำหรับการสนทนาและการค้นหาเว็บ", "a user": "ผู้ใช้", "About": "เกี่ยวกับ", "Accept autocomplete generation / Jump to prompt variable": "", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "บัญชี", "Account Activation Pending": "การเปิดใช้งานบัญชีอยู่ระหว่างดำเนินการ", "Accurate information": "ข้อมูลที่ถูกต้อง", "Action": "", "Action not found": "", "Action Required for Chat Log Storage": "ต้องดำเนินการเพื่อบันทึกบันทึกการแชต", "Actions": "", "Activate": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active": "กำลังใช้งาน", "Active Users": "ผู้ใช้ที่ใช้งานอยู่", "Add": "เพิ่ม", "Add a model ID": "", "Add a short description about what this model does": "เพิ่มคำอธิบายสั้นๆ เกี่ยวกับสิ่งที่โมเดลนี้ทำ", "Add a tag": "เพิ่มแท็ก", "Add Arena Model": "", "Add Connection": "เพิ่มการเชื่อมต่อ", "Add Content": "เพิ่มเนื้อหา", "Add content here": "เพิ่มเนื้อหาตรงนี้", "Add Custom Parameter": "", "Add custom prompt": "เพิ่มพรอมต์ที่กำหนดเอง", "Add Details": "", "Add Files": "เพิ่มไฟล์", "Add Group": "เพิ่มกลุ่ม", "Add Memory": "เพิ่มความจำ", "Add Model": "เพิ่มโมเดล", "Add Reaction": "", "Add Tag": "", "Add Tags": "เพิ่มแท็ก", "Add text content": "", "Add User": "เพิ่มผู้ใช้", "Add User Group": "", "Additional Config": "", "Additional configuration options for marker. This should be a JSON string with key-value pairs. For example, '{\"key\": \"value\"}'. Supported keys include: disable_links, keep_pageheader_in_output, keep_pagefooter_in_output, filter_blank_pages, drop_repeated_text, layout_coverage_threshold, merge_threshold, height_tolerance, gap_threshold, image_threshold, min_line_length, level_count, default_level": "", "Adjusting these settings will apply changes universally to all users.": "การปรับการตั้งค่าเหล่านี้จะนำไปใช้กับผู้ใช้ทั้งหมด", "admin": "ผู้ดูแลระบบ", "Admin": "ผู้ดูแลระบบ", "Admin Panel": "แผงผู้ดูแลระบบ", "Admin Settings": "การตั้งค่าผู้ดูแลระบบ", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "ผู้ดูแลระบบสามารถเข้าถึงเครื่องมือทั้งหมดได้ตลอดเวลา; ผู้ใช้ต้องการเครื่องมือที่กำหนดต่อโมเดลในพื้นที่ทำงาน", "Advanced Parameters": "พารามิเตอร์ขั้นสูง", "Advanced Params": "พารามิเตอร์ขั้นสูง", "AI": "", "All": "", "All Documents": "เอกสารทั้งหมด", "All models deleted successfully": "", "Allow Call": "อนุญาตการโทร", "Allow Chat Controls": "อนุญาตการควบคุมแชท", "Allow Chat Delete": "อนุญาตการลบแชท", "Allow Chat Deletion": "อนุญาตการลบการสนทนา", "Allow Chat Edit": "อนุญาตการแก้ไขแชท", "Allow Chat Export": "อนุญาตการส่งออกแชท", "Allow Chat Params": "", "Allow Chat Share": "อนุญาตการแชร์แชท", "Allow Chat System Prompt": "", "Allow Chat Valves": "", "Allow Continue Response": "", "Allow Delete Messages": "", "Allow File Upload": "อนุญาตการนำเข้าไฟล์", "Allow Multiple Models in Chat": "", "Allow non-local voices": "อนุญาตเสียงที่ไม่ใช่ท้องถิ่น", "Allow Rate Response": "", "Allow Regenerate Response": "", "Allow Speech to Text": "อนุญาตแปลงเสียงเป็นตัวอักษร", "Allow Temporary Chat": "อนุญาตการแชทชั่วคราว", "Allow Text to Speech": "อนุญาตแปลงตัวอักษรเป็นตัวเสียง", "Allow User Location": "อนุญาตตำแหน่งผู้ใช้", "Allow Voice Interruption in Call": "อนุญาตการแทรกเสียงในสาย", "Allowed Endpoints": "", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "มีบัญชีอยู่แล้ว?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "", "Always": "", "Always Collapse Code Blocks": "", "Always Expand Details": "", "Always Play Notification Sound": "", "Amazing": "", "an assistant": "ผู้ช่วย", "An error occurred while fetching the explanation": "", "Analytics": "", "Analyzed": "", "Analyzing...": "", "and": "และ", "and {{COUNT}} more": "", "and create a new shared link.": "และสร้างลิงก์ที่แชร์ใหม่", "Android": "", "API": "", "API Base URL": "URL ฐานของ API", "API Base URL for Datalab Marker service. Defaults to: https://www.datalab.to/api/v1/marker": "", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "คีย์ API", "API Key created.": "สร้างคีย์ API แล้ว", "API Key Endpoint Restrictions": "", "API keys": "คีย์ API", "API Version": "", "API Version is required": "", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "เมษายน", "Archive": "เก็บถาวร", "Archive All Chats": "เก็บถาวรการสนทนาทั้งหมด", "Archived Chats": "การสนทนาที่เก็บถาวร", "archived-chat-export": "", "Are you sure you want to clear all memories? This action cannot be undone.": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "คุณแน่ใจหรือ?", "Arena Models": "", "Artifacts": "", "Ask": "", "Ask a question": "", "Assistant": "", "Attach file from knowledge": "", "Attention to detail": "ใส่ใจในรายละเอียด", "Attribute for Mail": "", "Attribute for Username": "", "Audio": "เสียง", "August": "สิงหาคม", "Auth": "", "Authenticate": "", "Authentication": "", "Auto": "", "Auto-Copy Response to Clipboard": "ตอบสนองการคัดลอกอัตโนมัติไปยังคลิปบอร์ด", "Auto-playback response": "ตอบสนองการเล่นอัตโนมัติ", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "สตริงการตรวจสอบ API ของ AUTOMATIC1111", "AUTOMATIC1111 Base URL": "URL ฐานของ AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "ต้องการ URL ฐานของ AUTOMATIC1111", "Available list": "", "Available Tools": "", "available users": "ผู้ใช้ที่ใช้ได้", "available!": "พร้อมใช้งาน!", "Away": "ไม่อยู่", "Awful": "", "Azure AI Speech": "", "Azure OpenAI": "Azure OpenAI", "Azure Region": "", "Back": "กลับ", "Bad Response": "การตอบสนองที่ไม่ดี", "Banners": "แบนเนอร์", "Base Model (From)": "โมเดลพื้นฐาน (จาก)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "Bearer": "", "before": "ก่อน", "Being lazy": "ขี้เกียจ", "Beta": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Bio": "", "Birth Date": "", "BM25 Weight": "", "Bocha Search API Key": "", "Bold": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "คีย์ API ของ Brave Search", "Bullet List": "", "Button ID": "", "Button Label": "", "Button Prompt": "", "By {{name}}": "", "Bypass Embedding and Retrieval": "", "Bypass Web Loader": "", "Cache Base Model List": "", "Calendar": "", "Call": "โทร", "Call feature is not supported when using Web STT engine": "ไม่รองรับฟีเจอร์การโทรเมื่อใช้เครื่องยนต์ Web STT", "Camera": "กล้อง", "Cancel": "ยกเลิก", "Capabilities": "ความสามารถ", "Capture": "", "Capture Audio": "", "Certificate Path": "", "Change Password": "เปลี่ยนรหัสผ่าน", "Channel deleted successfully": "", "Channel Name": "", "Channel updated successfully": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "แชท", "Chat Background Image": "ภาพพื้นหลังแชท", "Chat Bubble UI": "UI ฟองแชท", "Chat Controls": "การควบคุมแชท", "Chat Conversation": "", "Chat direction": "ทิศทางการแชท", "Chat ID": "", "Chat moved successfully": "", "Chat Overview": "", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "แชท", "Check Again": "ตรวจสอบอีกครั้ง", "Check for updates": "ตรวจสอบการอัปเดต", "Checking for updates...": "กำลังตรวจสอบการอัปเดต...", "Choose a model before saving...": "เลือกโมเดลก่อนบันทึก...", "Chunk Overlap": "ทับซ้อนส่วนข้อมูล", "Chunk Size": "ขนาดส่วนข้อมูล", "Ciphers": "", "Citation": "การอ้างอิง", "Citations": "", "Clear memory": "ล้างความจำ", "Clear Memory": "", "click here": "", "Click here for filter guides.": "", "Click here for help.": "คลิกที่นี่เพื่อขอความช่วยเหลือ", "Click here to": "คลิกที่นี่เพื่อ", "Click here to download user import template file.": "คลิกที่นี่เพื่อดาวน์โหลดไฟล์แม่แบบนำเข้าผู้ใช้", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to see available models.": "", "Click here to select": "คลิกที่นี่เพื่อเลือก", "Click here to select a csv file.": "คลิกที่นี่เพื่อเลือกไฟล์ csv", "Click here to select a py file.": "คลิกที่นี่เพื่อเลือกไฟล์ py", "Click here to upload a workflow.json file.": "", "click here.": "คลิกที่นี่", "Click on the user role button to change a user's role.": "คลิกที่ปุ่มบทบาทผู้ใช้เพื่อเปลี่ยนบทบาทของผู้ใช้", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "การอนุญาตเขียนคลิปบอร์ดถูกปฏิเสธ โปรดตรวจสอบการตั้งค่าเบราว์เซอร์ของคุณเพื่อให้สิทธิ์ที่จำเป็น", "Clone": "โคลน", "Clone Chat": "", "Clone of {{TITLE}}": "", "Close": "ปิด", "Close Banner": "", "Close Configure Connection Modal": "", "Close modal": "", "Close settings modal": "", "Close Sidebar": "", "CMU ARCTIC speaker embedding name": "", "Code Block": "", "Code execution": "", "Code Execution": "", "Code Execution Engine": "", "Code Execution Timeout": "", "Code formatted successfully": "จัดรูปแบบโค้ดสำเร็จแล้ว", "Code Interpreter": "", "Code Interpreter Engine": "", "Code Interpreter Prompt Template": "", "Collapse": "", "Collection": "คอลเลคชัน", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "URL ฐานของ ComfyUI", "ComfyUI Base URL is required.": "ต้องการ URL ฐานของ ComfyUI", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Comma separated Node Ids (e.g. 1 or 1,2)": "", "Command": "คำสั่ง", "Comment": "", "Completions": "", "Compress Images in Channels": "", "Concurrent Requests": "คำขอพร้อมกัน", "Config imported successfully": "", "Configure": "", "Confirm": "ยืนยัน", "Confirm Password": "ยืนยันรหัสผ่าน", "Confirm your action": "ยืนยันการดำเนินการของคุณ", "Confirm your new password": "", "Confirm Your Password": "", "Connect to your own OpenAI compatible API endpoints.": "", "Connect to your own OpenAPI compatible external tool servers.": "", "Connection failed": "", "Connection successful": "", "Connection Type": "", "Connections": "การเชื่อมต่อ", "Connections saved successfully": "", "Connections settings updated": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "", "Contact Admin for WebUI Access": "ติดต่อผู้ดูแลระบบสำหรับการเข้าถึง WebUI", "Content": "เนื้อหา", "Content Extraction Engine": "", "Continue Response": "ตอบสนองต่อไป", "Continue with {{provider}}": "ดำเนินการต่อด้วย {{provider}}", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "", "Controls": "การควบคุม", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "", "Conversation saved successfully": "", "Copied": "", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "คัดลอก URL แชทที่แชร์ไปยังคลิปบอร์ดแล้ว!", "Copied to clipboard": "", "Copy": "คัดลอก", "Copy Formatted Text": "", "Copy last code block": "คัดลอกบล็อกโค้ดสุดท้าย", "Copy last response": "คัดลอกการตอบสนองล่าสุด", "Copy link": "", "Copy Link": "คัดลอกลิงก์", "Copy to clipboard": "", "Copying to clipboard was successful!": "คัดลอกไปยังคลิปบอร์ดสำเร็จแล้ว!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "", "Create": "", "Create a knowledge base": "", "Create a model": "สร้างโมเดล", "Create Account": "สร้างบัญชี", "Create Admin Account": "", "Create Channel": "", "Create Folder": "", "Create Group": "", "Create Knowledge": "", "Create new key": "สร้างคีย์ใหม่", "Create new secret key": "สร้างคีย์ลับใหม่", "Create Note": "", "Create your first note by clicking on the plus button below.": "", "Created at": "สร้างเมื่อ", "Created At": "สร้างเมื่อ", "Created by": "สร้างโดย", "CSV Import": "นำเข้า CSV", "Ctrl+Enter to Send": "", "Current Model": "โมเดลปัจจุบัน", "Current Password": "รหัสผ่านปัจจุบัน", "Custom": "กำหนดเอง", "Custom description enabled": "", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "", "Dark": "มืด", "Database": "ฐานข้อมูล", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "DD/MM/YYYY": "", "December": "ธันวาคม", "Deepgram": "", "Default": "ค่าเริ่มต้น", "Default (Open AI)": "", "Default (SentenceTransformers)": "ค่าเริ่มต้น (SentenceTransformers)", "Default action buttons will be used.": "", "Default description enabled": "", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "", "Default Model": "โมเดลค่าเริ่มต้น", "Default model updated": "อัปเดตโมเดลค่าเริ่มต้นแล้ว", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "คำแนะนำพรอมต์ค่าเริ่มต้น", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "", "Default User Role": "บทบาทผู้ใช้ค่าเริ่มต้น", "Delete": "ลบ", "Delete a model": "ลบโมเดล", "Delete All Chats": "ลบการสนทนาทั้งหมด", "Delete All Models": "", "Delete chat": "ลบแชท", "Delete Chat": "ลบแชท", "Delete chat?": "ลบแชท?", "Delete folder?": "", "Delete function?": "ลบฟังก์ชัน?", "Delete Message": "", "Delete message?": "", "Delete note?": "", "Delete prompt?": "ลบพรอมต์?", "delete this link": "ลบลิงก์นี้", "Delete tool?": "ลบเครื่องมือ?", "Delete User": "ลบผู้ใช้", "Deleted {{deleteModelTag}}": "ลบ {{deleteModelTag}}", "Deleted {{name}}": "ลบ {{name}}", "Deleted User": "", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "", "Description": "คำอธิบาย", "Detect Artifacts Automatically": "", "Dictate": "", "Didn't fully follow instructions": "ไม่ได้ปฏิบัติตามคำแนะนำทั้งหมด", "Direct": "", "Direct Connections": "", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "", "Direct Tool Servers": "", "Directory selection was cancelled": "", "Disable Code Interpreter": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "ปิดใช้งาน", "Discover a function": "ค้นหาฟังก์ชัน", "Discover a model": "ค้นหาโมเดล", "Discover a prompt": "ค้นหาพรอมต์", "Discover a tool": "ค้นหาเครื่องมือ", "Discover how to use Open WebUI and seek support from the community.": "", "Discover wonders": "", "Discover, download, and explore custom functions": "ค้นหา ดาวน์โหลด และสำรวจฟังก์ชันที่กำหนดเอง", "Discover, download, and explore custom prompts": "ค้นหา ดาวน์โหลด และสำรวจพรอมต์ที่กำหนดเอง", "Discover, download, and explore custom tools": "ค้นหา ดาวน์โหลด และสำรวจเครื่องมือที่กำหนดเอง", "Discover, download, and explore model presets": "ค้นหา ดาวน์โหลด และสำรวจพรีเซ็ตโมเดล", "Display": "", "Display Emoji in Call": "แสดงอิโมจิในการโทร", "Display Multi-model Responses in Tabs": "", "Display the username instead of You in the Chat": "แสดงชื่อผู้ใช้แทนคุณในการแชท", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "อย่าติดตั้งฟังก์ชันจากแหล่งที่คุณไม่ไว้วางใจอย่างเต็มที่", "Do not install tools from sources you do not fully trust.": "อย่าติดตั้งเครื่องมือจากแหล่งที่คุณไม่ไว้วางใจอย่างเต็มที่", "Docling": "", "Docling Server URL required.": "", "Document": "เอกสาร", "Document Intelligence": "", "Document Intelligence endpoint required.": "", "Documentation": "เอกสารประกอบ", "Documents": "เอกสาร", "does not make any external connections, and your data stays securely on your locally hosted server.": "ไม่เชื่อมต่อภายนอกใดๆ และข้อมูลของคุณจะอยู่บนเซิร์ฟเวอร์ที่โฮสต์ในท้องถิ่นของคุณอย่างปลอดภัย", "Domain Filter List": "", "don't fetch random pipelines from sources you don't trust.": "อย่าดึง pipelines แบบสุ่มจากแหล่งที่ไม่น่าเชื่อถือ", "Don't have an account?": "ยังไม่มีบัญชี?", "don't install random functions from sources you don't trust.": "อย่าติดตั้งฟังก์ชันแบบสุ่มจากแหล่งที่คุณไม่ไว้วางใจ", "don't install random tools from sources you don't trust.": "อย่าติดตั้งเครื่องมือแบบสุ่มจากแหล่งที่คุณไม่ไว้วางใจ", "Don't like the style": "ไม่ชอบสไตล์นี้", "Done": "เสร็จสิ้น", "Download": "ดาวน์โหลด", "Download & Delete": "ดาวน์โหลดและลบ", "Download as SVG": "", "Download canceled": "ยกเลิกการดาวน์โหลด", "Download Database": "ดาวน์โหลดฐานข้อมูล", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "เช่น '30s', '10m' หน่วยเวลาที่ถูกต้องคือ 's', 'm', 'h'", "e.g. \"json\" or a JSON schema": "", "e.g. 60": "", "e.g. A filter to remove profanity from text": "", "e.g. en": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "e.g., westus (leave blank for eastus)": "", "Edit": "แก้ไข", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Folder": "", "Edit Memory": "แก้ไขความจำ", "Edit User": "แก้ไขผู้ใช้", "Edit User Group": "", "Edited": "", "Editing": "", "Eject": "", "ElevenLabs": "", "Email": "อีเมล", "Embark on adventures": "", "Embedding": "", "Embedding Batch Size": "ขนาดชุดการฝัง", "Embedding Model": "โมเดลการฝัง", "Embedding Model Engine": "เครื่องยนต์โมเดลการฝัง", "Embedding model set to \"{{embedding_model}}\"": "ตั้งค่าโมเดลการฝังเป็น \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Code Execution": "", "Enable Code Interpreter": "", "Enable Community Sharing": "เปิดใช้งานการแชร์ในชุมชน", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity.": "", "Enable New Sign Ups": "เปิดใช้งานการสมัครใหม่", "Enable, disable, or customize the reasoning tags used by the model. \"Enabled\" uses default tags, \"Disabled\" turns off reasoning tags, and \"Custom\" lets you specify your own start and end tags.": "", "Enabled": "เปิดใช้งาน", "End Tag": "", "Endpoint URL": "", "Enforce Temporary Chat": "", "Enhance": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "ตรวจสอบว่าไฟล์ CSV ของคุณมี 4 คอลัมน์ในลำดับนี้: ชื่อ, อีเมล, รหัสผ่าน, บทบาท", "Enter {{role}} message here": "ใส่ข้อความ {{role}} ที่นี่", "Enter a detail about yourself for your LLMs to recall": "ใส่รายละเอียดเกี่ยวกับตัวคุณสำหรับ LLMs ของคุณให้จดจำ", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "ใส่สตริงการตรวจสอบ API (เช่น username:password)", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Bocha Search API Key": "", "Enter Brave Search API Key": "ใส่คีย์ API ของ Brave Search", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "ใส่การทับซ้อนส่วนข้อมูล", "Enter Chunk Size": "ใส่ขนาดส่วนข้อมูล", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter coordinates (e.g. 51.505, -0.09)": "", "Enter Datalab Marker API Base URL": "", "Enter Datalab Marker API Key": "", "Enter description": "", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "", "Enter Document Intelligence Endpoint": "", "Enter Document Intelligence Key": "", "Enter domains separated by commas (e.g., example.com,site.org)": "", "Enter Exa API Key": "", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter folder name": "", "Enter Github Raw URL": "ใส่ URL ดิบของ Github", "Enter Google PSE API Key": "ใส่คีย์ API ของ Google PSE", "Enter Google PSE Engine Id": "ใส่รหัสเครื่องยนต์ของ Google PSE", "Enter hex color (e.g. #FF0000)": "", "Enter ID": "", "Enter Image Size (e.g. 512x512)": "ใส่ขนาดภาพ (เช่น 512x512)", "Enter Jina API Key": "", "Enter JSON config (e.g., {\"disable_links\": true})": "", "Enter Jupyter Password": "", "Enter Jupyter Token": "", "Enter Jupyter URL": "", "Enter Kagi Search API Key": "", "Enter Key Behavior": "", "Enter language codes": "ใส่รหัสภาษา", "Enter Mistral API Key": "", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "ใส่แท็กโมเดล (เช่น {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "ใส่จำนวนขั้นตอน (เช่น 50)", "Enter Perplexity API Key": "", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "", "Enter reasoning effort": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "ใส่คะแนน", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "ใส URL การค้นหาของ Searxng", "Enter Seed": "", "Enter SerpApi API Key": "", "Enter SerpApi Engine": "", "Enter Serper API Key": "ใส่คีย์ API ของ Serper", "Enter Serply API Key": "ใส่คีย์ API ของ Serply", "Enter Serpstack API Key": "ใส่คีย์ API ของ Serpstack", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter Sougou Search API sID": "", "Enter Sougou Search API SK": "", "Enter stop sequence": "ใส่ลำดับหยุด", "Enter system prompt": "ใส่พรอมต์ระบบ", "Enter system prompt here": "", "Enter Tavily API Key": "ใส่คีย์ API ของ Tavily", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "ใส่ URL เซิร์ฟเวอร์ของ Tika", "Enter timeout in seconds": "", "Enter to Send": "", "Enter Top K": "ใส่ Top K", "Enter Top K Reranker": "", "Enter URL (e.g. http://127.0.0.1:7860/)": "ใส่ URL (เช่น http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "ใส่ URL (เช่น http://localhost:11434)", "Enter value": "", "Enter value (true/false)": "", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your code here...": "ป้อนโค้ดของคุณที่นี่...", "Enter your current password": "", "Enter Your Email": "ใส่อีเมลของคุณ", "Enter Your Full Name": "ใส่ชื่อเต็มของคุณ", "Enter your gender": "", "Enter your message": "ใส่ข้อความของคุณ", "Enter your name": "กรอกชื่อของคุณ", "Enter Your Name": "", "Enter your new password": "กรอกรหัสผ่านใหม่ของคุณ", "Enter Your Password": "ใส่รหัสผ่านของคุณ", "Enter Your Role": "ใส่บทบาทของคุณ", "Enter Your Username": "กรอกชื่อบัญชีผู้ใช้ของคุณ", "Enter your webhook URL": "", "Error": "ข้อผิดพลาด", "ERROR": "", "Error accessing directory": "", "Error accessing Google Drive: {{error}}": "", "Error accessing media devices.": "", "Error starting recording.": "", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "", "Error: A model with the ID '{{modelId}}' already exists. Please select a different ID to proceed.": "", "Error: Model ID cannot be empty. Please enter a valid ID to proceed.": "", "Evaluations": "การประเมิน", "Everyone": "", "Exa API Key": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: mail": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "", "Exclude": "", "Execute code for analysis": "", "Executing **{{NAME}}**...": "", "Expand": "ขยาย", "Experimental": "การทดลอง", "Explain": "อธิบาย", "Explore the cosmos": "", "Export": "ส่งออก", "Export All Archived Chats": "", "Export All Chats (All Users)": "ส่งออกการสนทนาทั้งหมด (ผู้ใช้ทั้งหมด)", "Export chat (.json)": "ส่งออกการสนทนา (.json)", "Export Chats": "ส่งออกการสนทนา", "Export Config to JSON File": "", "Export Functions": "ส่งออกฟังก์ชัน", "Export Models": "ส่งออกโมเดล", "Export Presets": "", "Export Prompt Suggestions": "", "Export Prompts": "ส่งออกพรอมต์", "Export to CSV": "", "Export Tools": "ส่งออกเครื่องมือ", "Export Users": "", "External": "", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "", "External Web Loader URL": "", "External Web Search API Key": "", "External Web Search URL": "", "Fade Effect for Streaming Text": "", "Failed to add file.": "", "Failed to connect to {{URL}} OpenAPI tool server": "", "Failed to copy link": "", "Failed to create API Key.": "สร้างคีย์ API ล้มเหลว", "Failed to delete note": "", "Failed to extract content from the file: {{error}}": "", "Failed to extract content from the file.": "", "Failed to fetch models": "", "Failed to generate title": "", "Failed to load chat preview": "", "Failed to load file content.": "", "Failed to move chat": "", "Failed to read clipboard contents": "อ่านเนื้อหาคลิปบอร์ดล้มเหลว", "Failed to save connections": "", "Failed to save conversation": "บันทึกการสนทนาล้มเหลว", "Failed to save models configuration": "", "Failed to update settings": "อัปเดตการตั้งค่าล้มเหลว", "Failed to upload file.": "", "Features": "", "Features Permissions": "", "February": "กุมภาพันธ์", "Feedback Details": "", "Feedback History": "ประวัติการการตอบรับ", "Feedbacks": "การตอบรับ", "Feel free to add specific details": "สามารถเพิ่มรายละเอียดเฉพาะได้", "Female": "", "File": "ไฟล์", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "โหมดไฟล์", "File not found.": "ไม่พบไฟล์", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File Upload": "", "File uploaded successfully": "", "Files": "ไฟล์", "Filter": "", "Filter is now globally disabled": "การกรองถูกปิดใช้งานทั่วโลกแล้ว", "Filter is now globally enabled": "การกรองถูกเปิดใช้งานทั่วโลกแล้ว", "Filters": "ตัวกรอง", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "ตรวจพบการปลอมแปลงลายนิ้วมือ: ไม่สามารถใช้ชื่อย่อเป็นอวตารได้ ใช้รูปโปรไฟล์เริ่มต้น", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Floating Quick Actions": "", "Focus chat input": "โฟกัสการป้อนแชท", "Folder deleted successfully": "", "Folder Name": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Folder updated successfully": "", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "ปฏิบัติตามคำแนะนำอย่างสมบูรณ์แบบ", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "", "Form": "ฟอร์ม", "Format Lines": "", "Format the lines in the output. Defaults to False. If set to True, the lines will be formatted to detect inline math and styles.": "", "Format your variables using brackets like this:": "", "Formatting may be inconsistent from source.": "", "Forwards system user session credentials to authenticate": "", "Full Context Mode": "", "Function": "", "Function Calling": "", "Function created successfully": "สร้างฟังก์ชันสำเร็จ", "Function deleted successfully": "ลบฟังก์ชันสำเร็จ", "Function Description": "", "Function ID": "", "Function imported successfully": "", "Function is now globally disabled": "ฟังก์ชันถูกปิดใช้งานทั่วโลกแล้ว", "Function is now globally enabled": "ฟังก์ชันถูกเปิดใช้งานทั่วโลกแล้ว", "Function Name": "", "Function updated successfully": "อัปเดตฟังก์ชันสำเร็จ", "Functions": "ฟังก์ชัน", "Functions allow arbitrary code execution.": "ฟังก์ชันอนุญาตการเรียกใช้โค้ดโดยพลการ", "Functions imported successfully": "นำเข้าฟังก์ชันสำเร็จ", "Gemini": "", "Gemini API Config": "", "Gemini API Key is required.": "", "Gender": "", "General": "ทั่วไป", "Generate": "", "Generate an image": "", "Generate Image": "สร้างภาพ", "Generate prompt pair": "", "Generating search query": "สร้างคำค้นหา", "Generating...": "", "Get information on {{name}} in the UI": "", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "ทั่วโลก", "Good Response": "การตอบสนองที่ดี", "Google Drive": "", "Google PSE API Key": "คีย์ API ของ Google PSE", "Google PSE Engine Id": "รหัสเครื่องยนต์ของ Google PSE", "Gravatar": "", "Group": "กลุ่ม", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "ชื่อกลุ่ม", "Group updated successfully": "", "Groups": "กลุ่ม", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "", "Height": "", "Hello, {{name}}": "สวัสดี, {{name}}", "Help": "ช่วยเหลือ", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "ซ่อน", "Hide from Sidebar": "", "Hide Model": "", "High": "", "High Contrast Mode": "", "Home": "", "Host": "", "How can I help you today?": "วันนี้ฉันจะช่วยอะไรคุณได้บ้าง?", "How would you rate this response?": "", "HTML": "", "Hybrid Search": "การค้นหาแบบไฮบริด", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "ฉันรับทราบว่าฉันได้อ่านและเข้าใจผลกระทบของการกระทำของฉัน ฉันทราบถึงความเสี่ยงที่เกี่ยวข้องกับการเรียกใช้โค้ดโดยพลการและฉันได้ตรวจสอบความน่าเชื่อถือของแหล่งที่มาแล้ว", "ID": "", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "", "Image": "", "Image Compression": "", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "", "Image Generation (Experimental)": "การสร้างภาพ (การทดลอง)", "Image Generation Engine": "เครื่องยนต์การสร้างภาพ", "Image Max Compression Size": "", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "", "Image Prompt Generation Prompt": "", "Image Settings": "การตั้งค่าภาพ", "Images": "ภาพ", "Import": "", "Import Chats": "นำเข้าการสนทนา", "Import Config from JSON File": "", "Import From Link": "", "Import Functions": "นำเข้าฟังก์ชัน", "Import Models": "นำเข้าโมเดล", "Import Notes": "", "Import Presets": "", "Import Prompt Suggestions": "", "Import Prompts": "นำเข้าพรอมต์", "Import Tools": "นำเข้าเครื่องมือ", "Important Update": "อัปเดตสำคัญ", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "รวมแฟลก `--api-auth` เมื่อเรียกใช้ stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "รวมแฟลก `--api` เมื่อเรียกใช้ stable-diffusion-webui", "Includes SharePoint": "รวม SharePoint", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "", "Info": "ข้อมูล", "Initials": "", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "", "Input": "", "Input commands": "คำสั่งป้อนข้อมูล", "Input Key (e.g. text, unet_name, steps)": "", "Input Variables": "", "Insert": "", "Insert Follow-Up Prompt to Input": "", "Insert Prompt as Rich Text": "", "Install from Github URL": "ติดตั้งจาก URL ของ Github", "Instant Auto-Send After Voice Transcription": "ส่งอัตโนมัติทันทีหลังจากการถอดเสียง", "Integration": "", "Interface": "อินเทอร์เฟซ", "Invalid file content": "", "Invalid file format.": "", "Invalid JSON file": "", "Invalid JSON format for ComfyUI Workflow.": "", "Invalid JSON format in Additional Config": "", "Invalid Tag": "แท็กไม่ถูกต้อง", "is typing...": "", "Italic": "", "January": "มกราคม", "Jina API Key": "", "join our Discord for help.": "เข้าร่วม Discord ของเราเพื่อขอความช่วยเหลือ", "JSON": "JSON", "JSON Preview": "ดูตัวอย่าง JSON", "July": "กรกฎาคม", "June": "มิถุนายน", "Jupyter Auth": "", "Jupyter URL": "", "JWT Expiration": "การหมดอายุของ JWT", "JWT Token": "โทเค็น JWT", "Kagi Search API Key": "", "Keep Follow-Up Prompts in Chat": "", "Keep in Sidebar": "", "Key": "", "Key is required": "", "Keyboard shortcuts": "ทางลัดแป้นพิมพ์", "Knowledge": "ความรู้", "Knowledge Access": "การเข้าถึงความรู้", "Knowledge Base": "", "Knowledge created successfully.": "สร้างความรู้สำเร็จ", "Knowledge deleted successfully.": "ลบความรู้สำเร็จ", "Knowledge Description": "", "Knowledge Name": "", "Knowledge Public Sharing": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Kokoro.js (Browser)": "", "Kokoro.js Dtype": "", "Label": "", "Landing Page Mode": "", "Language": "ภาษา", "Language Locales": "", "Last Active": "ใช้งานล่าสุด", "Last Modified": "แก้ไขล่าสุด", "Last reply": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Learn More": "", "Learn more about OpenAPI tool servers.": "", "Leave empty for no compression": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{url}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Leave model field empty to use the default model.": "", "lexical": "", "License": "", "Lift List": "", "Light": "แสง", "Listening...": "กำลังฟัง...", "Llama.cpp": "", "LLMs can make mistakes. Verify important information.": "LLMs สามารถทำผิดพลาดได้ ตรวจสอบข้อมูลสำคัญ", "Loader": "", "Loading Kokoro.js...": "", "Loading...": "กำลังโหลด...", "Local": "", "Local Task Model": "", "Location access not allowed": "", "Lost": "", "Low": "", "LTR": "LTR", "Made by Open WebUI Community": "สร้างโดยชุมชน OpenWebUI", "Make password visible in the user interface": "", "Make sure to enclose them with": "", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Male": "", "Manage": "จัดการ", "Manage Direct Connections": "", "Manage Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "จัดการไปป์ไลน์", "Manage Tool Servers": "", "Manage your account information.": "", "March": "มีนาคม", "Markdown": "", "Markdown (Header)": "", "Max Speakers": "", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "สามารถดาวน์โหลดโมเดลได้สูงสุด 3 โมเดลในเวลาเดียวกัน โปรดลองอีกครั้งในภายหลัง", "May": "พฤษภาคม", "Medium": "", "Memories accessible by LLMs will be shown here.": "", "Memory": "ความจำ", "Memory added successfully": "เพิ่มโมเดลสำเร็จ", "Memory cleared successfully": "ล้าง", "Memory deleted successfully": "ลบโมเดลสำเร็จ", "Memory updated successfully": "", "Merge Responses": "", "Merged Response": "การตอบกลับที่รวมกัน", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "ข้อความที่คุณส่งหลังจากสร้างลิงก์ของคุณแล้วจะไม่ถูกแชร์ ผู้ใช้ที่มี URL จะสามารถดูแชทที่แชร์ได้", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "โมเดล '{{modelName}}' ถูกดาวน์โหลดเรียบร้อยแล้ว", "Model '{{modelTag}}' is already in queue for downloading.": "โมเดล '{{modelTag}}' กำลังอยู่ในคิวสำหรับการดาวน์โหลด", "Model {{modelId}} not found": "ไม่พบโมเดล {{modelId}}", "Model {{modelName}} is not vision capable": "โมเดล {{modelName}} ไม่มีคุณสมบัติวิสชั่น", "Model {{name}} is now {{status}}": "โมเดล {{name}} ขณะนี้ {{status}}", "Model {{name}} is now hidden": "", "Model {{name}} is now visible": "", "Model accepts file inputs": "", "Model accepts image inputs": "", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "สร้างโมเดลสำเร็จ!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "ตรวจพบเส้นทางระบบไฟล์ของโมเดล ต้องการชื่อย่อของโมเดลสำหรับการอัปเดต ไม่สามารถดำเนินการต่อได้", "Model Filtering": "", "Model ID": "รหัสโมเดล", "Model ID is required.": "", "Model IDs": "", "Model Name": "", "Model name already exists, please choose a different one": "", "Model Name is required.": "", "Model not selected": "ยังไม่ได้เลือกโมเดล", "Model Params": "พารามิเตอร์ของโมเดล", "Model Permissions": "", "Model unloaded successfully": "", "Model updated successfully": "อัปเดตโมเดลเรียบร้อยแล้ว", "Model(s) do not support file upload": "", "Modelfile Content": "เนื้อหาของไฟล์โมเดล", "Models": "โมเดล", "Models Access": "", "Models configuration saved successfully": "", "Models Public Sharing": "", "Mojeek Search API Key": "", "more": "", "More": "เพิ่มเติม", "More Concise": "", "More Options": "", "Move": "", "Name": "ชื่อ", "Name and ID are required, please fill them out": "", "Name your knowledge base": "", "Native": "", "New Button": "", "New Chat": "แชทใหม่", "New Folder": "", "New Function": "", "New Note": "", "New Password": "รหัสผ่านใหม่", "New Tool": "", "new-channel": "", "Next message": "", "No chats found": "", "No chats found for this user.": "", "No chats found.": "", "No content": "", "No content found": "", "No content found in file.": "", "No content to speak": "ไม่มีเนื้อหาที่จะพูด", "No conversation to save": "", "No distance available": "", "No feedbacks found": "", "No file selected": "ไม่ได้เลือกไฟล์", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No inference engine with management support found": "", "No knowledge found": "", "No memories to clear": "", "No model IDs": "", "No models found": "", "No models selected": "", "No Notes": "", "No results": "ไม่มีผลลัพธ์", "No results found": "ไม่มีผลลัพธ์", "No search query generated": "ไม่มีการสร้างคำค้นหา", "No source available": "ไม่มีแหล่งข้อมูล", "No suggestion prompts": "ไม่มีพรอมพ์แนะนำ", "No users were found.": "", "No valves": "", "No valves to update": "ไม่มีวาล์วที่จะอัปเดต", "Node Ids": "", "None": "ไม่มี", "Not factually correct": "ไม่ถูกต้องตามข้อเท็จจริง", "Not helpful": "", "Note deleted successfully": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "หมายเหตุ: หากคุณตั้งค่าคะแนนขั้นต่ำ การค้นหาจะคืนเอกสารที่มีคะแนนมากกว่าหรือเท่ากับคะแนนขั้นต่ำเท่านั้น", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "การแจ้งเตือน", "November": "พฤศจิกายน", "OAuth ID": "OAuth ID", "October": "ตุลาคม", "Off": "ปิด", "Okay, Let's Go!": "ตกลง ไปกัน!", "OLED Dark": "OLED โหมดมื", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "", "Ollama Version": "เวอร์ชั่น <PERSON><PERSON>ma", "On": "เปิด", "OneDrive": "", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "อนุญาตให้ใช้เฉพาะอักขระตัวอักษรและตัวเลข รวมถึงเครื่องหมายขีดกลางในสตริงคำสั่งเท่านั้น", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only markdown files are allowed": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "อุ๊บส์! ดูเหมือนว่า URL ไม่ถูกต้อง กรุณาตรวจสอบและลองใหม่อีกครั้ง", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "อุ๊บส์! คุณกำลังใช้วิธีที่ไม่รองรับ (เฉพาะเว็บส่วนหน้า) กรุณาให้บริการ WebUI จากเว็บส่วนแบ็กเอนด์", "Open file": "", "Open in full screen": "", "Open modal to configure connection": "", "Open Modal To Manage Floating Quick Actions": "", "Open new chat": "เปิดแชทใหม่", "Open Sidebar": "", "Open User Profile Menu": "", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "เวอร์ชั่น Open WebUI (v{{OPEN_WEBUI_VERSION}}) ต่ำกว่าเวอร์ชั่นที่ต้องการ (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "การตั้งค่า OpenAI API", "OpenAI API Key is required.": "จำเป็นต้องใช้คีย์ OpenAI API", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "จำเป็นต้องใช้ URL/คีย์ OpenAI", "openapi.json URL or Path": "", "Optional": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "หรือ", "Ordered List": "", "Organize your users": "", "Other": "อื่น ๆ", "OUTPUT": "", "Output format": "", "Output Format": "", "Overview": "", "page": "", "Paginate": "", "Parameters": "", "Password": "รหัสผ่าน", "Passwords do not match.": "", "Paste Large Text as File": "", "PDF document (.pdf)": "เอกสาร PDF (.pdf)", "PDF Extract Images (OCR)": "การแยกรูปภาพจาก PDF (OCR)", "pending": "รอดำเนินการ", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "ถูกปฏิเสธเมื่อเข้าถึงอุปกรณ์", "Permission denied when accessing microphone": "ถูกปฏิเสธเมื่อเข้าถึงไมโครโฟน", "Permission denied when accessing microphone: {{error}}": "การอนุญาตถูกปฏิเสธเมื่อเข้าถึงไมโครโฟน: {{error}}", "Permissions": "", "Perplexity API Key": "", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "การปรับแต่ง", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "ปักหมุด", "Pinned": "ปักหมุดแล้ว", "Pioneer insights": "", "Pipe": "", "Pipeline deleted successfully": "ลบไปป์ไลน์เรียบร้อยแล้ว", "Pipeline downloaded successfully": "ดาวน์โหลดไปป์ไลน์เรียบร้อยแล้ว", "Pipelines": "ไปป์ไลน์", "Pipelines are a plugin system with arbitrary code execution —": "Pipelines เป็นระบบปลั๊กอินที่สามารถรันโค้ดได้ตามอำเภอใจ —", "Pipelines Not Detected": "ไม่พบไปป์ไลน์", "Pipelines Valves": "วาล์วของไปป์ไลน์", "Plain text (.md)": "", "Plain text (.txt)": "ไฟล์ข้อความ (.txt)", "Playground": "สนามทดสอบ", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "โปรดตรวจสอบคำเตือนต่อไปนี้อย่างละเอียด:", "Please do not close the settings page while loading the model.": "", "Please enter a message or attach a file.": "", "Please enter a prompt": "", "Please enter a valid path": "", "Please enter a valid URL": "", "Please fill in all fields.": "", "Please select a model first.": "", "Please select a model.": "", "Please select a reason": "", "Please wait until all files are uploaded.": "", "Port": "", "Positive attitude": "ทัศนคติด้านบวก", "Prefer not to say": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Prevent file creation": "", "Preview": "", "Previous 30 days": "30 วันที่ผ่านมา", "Previous 7 days": "7 วันที่ผ่านมา", "Previous message": "", "Private": "", "Profile": "โปรไฟล์", "Prompt": "", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "พรอมต์ (เช่น บอกข้อเท็จจริงที่น่าสนุกเกี่ยวกับจักรวรรดิโรมัน)", "Prompt Autocompletion": "", "Prompt Content": "เนื้อหาพรอมต์", "Prompt created successfully": "", "Prompt suggestions": "", "Prompt updated successfully": "", "Prompts": "พรอมต์", "Prompts Access": "", "Prompts Public Sharing": "", "Public": "", "Pull \"{{searchValue}}\" from Ollama.com": "", "Pull a model from Ollama.com": "", "Query Generation Prompt": "", "Quick Actions": "", "RAG Template": "แม่แบบ RAG", "Rating": "", "Re-rank models by topic similarity": "", "Read": "", "Read Aloud": "อ่านออกเสียง", "Reason": "", "Reasoning Effort": "", "Reasoning Tags": "", "Record": "", "Record voice": "บันทึกเสียง", "Redirecting you to Open WebUI Community": "กำลังเปลี่ยนเส้นทางคุณไปยังชุมชน OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "เรียกตัวเองว่า \"ผู้ใช้\" (เช่น \"ผู้ใช้กำลังเรียนภาษาสเปน\")", "References from": "", "Refused when it shouldn't have": "ปฏิเสธเมื่อไม่ควรทำ", "Regenerate": "สร้างใหม่", "Regenerate Menu": "", "Reindex": "", "Reindex Knowledge Base Vectors": "", "Release Notes": "บันทึกรุ่น", "Releases": "", "Relevance": "", "Relevance Threshold": "", "Remember Dismissal": "", "Remove": "ลบ", "Remove {{MODELID}} from list.": "", "Remove file": "", "Remove File": "", "Remove image": "", "Remove Model": "ลบโมเดล", "Remove this tag from list": "", "Rename": "เปลี่ยนชื่อ", "Reorder Models": "", "Reply in Thread": "", "Reranking Engine": "", "Reranking Model": "จัดอันดับใหม่โมเดล", "Reset": "รีเซ็ต", "Reset All Models": "", "Reset Image": "รีเซ็ตภาพ", "Reset Upload Directory": "รีเซ็ตไดเร็กทอรีการอัปโหลด", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "ไม่สามารถเปิดการแจ้งเตือนการตอบสนองได้เนื่องจากเว็บไซต์ปฏิเสธ กรุณาเข้าการตั้งค่าเบราว์เซอร์ของคุณเพื่อให้สิทธิ์การเข้าถึงที่จำเป็น", "Response splitting": "", "Response Watermark": "", "Result": "", "RESULT": "ผลลัพธ์", "Retrieval": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "บทบาท", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "", "Running": "กำลังทำงาน", "Running...": "กำลังทำงาน...", "Save": "บันทึก", "Save & Create": "บันทึกและสร้าง", "Save & Update": "บันทึกและอัปเดต", "Save As Copy": "", "Save Chat": "", "Save Tag": "", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "การบันทึกบันทึกการสนทนาโดยตรงไปยังที่จัดเก็บในเบราว์เซอร์ของคุณไม่ได้รับการสนับสนุนอีกต่อไป โปรดสละเวลาสักครู่เพื่อดาวน์โหลดและลบบันทึกการสนทนาของคุณโดยคลิกที่ปุ่มด้านล่าง ไม่ต้องกังวล คุณสามารถนำเข้าบันทึกการสนทนาของคุณกลับไปยังส่วนแบ็กเอนด์ได้อย่างง่ายดายผ่าน", "Scroll On Branch Change": "", "Search": "ค้นหา", "Search a model": "ค้นหาโมเดล", "Search all emojis": "", "Search Base": "", "Search Chats": "ค้นหาแชท", "Search Collection": "", "Search Filters": "", "search for archived chats": "", "search for folders": "", "search for pinned chats": "", "search for shared chats": "", "search for tags": "", "Search Functions": "ค้นหาฟังก์ชัน", "Search In Models": "", "Search Knowledge": "", "Search Models": "ค้นหาโมเดล", "Search Notes": "", "Search options": "", "Search Prompts": "ค้นหาพรอมต์", "Search Result Count": "จำนวนผลลัพธ์การค้นหา", "Search the internet": "", "Search Tools": "เครื่องมือค้นหา", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "กำลังค้นหา \"{{searchQ<PERSON>y}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searching the web...": "", "Searxng Query URL": "URL คำค้นหา", "See readme.md for instructions": "ดู readme.md สำหรับคำแนะนำ", "See what's new": "ดูสิ่งที่ใหม่", "Seed": "Seed", "Select": "", "Select a base model": "เลือกโมเดลฐาน", "Select a base model (e.g. llama3, gpt-4o)": "", "Select a conversation to preview": "", "Select a engine": "เลือกเอนจิน", "Select a function": "เลือกฟังก์ชัน", "Select a group": "", "Select a language": "", "Select a mode": "", "Select a model": "เลือกโมเดล", "Select a model (optional)": "", "Select a pipeline": "เลือกไปป์ไลน์", "Select a pipeline url": "เลือก URL ไปป์ไลน์", "Select a reranking model engine": "", "Select a role": "", "Select a theme": "", "Select a tool": "เลือกเครื่องมือ", "Select a voice": "", "Select an auth method": "", "Select an embedding model engine": "", "Select an engine": "", "Select an Ollama instance": "", "Select an output format": "", "Select dtype": "", "Select Engine": "", "Select how to split message text for TTS requests": "", "Select Knowledge": "", "Select only one model to call": "เลือกเพียงโมเดลเดียวที่จะใช้", "Selected model(s) do not support image inputs": "โมเดลที่เลือกไม่รองรับภาพ", "semantic": "", "Semantic distance to query": "", "Send": "ส่ง", "Send a Message": "ส่งข้อความ", "Send message": "ส่งข้อความ", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "กันยายน", "SerpApi API Key": "", "SerpApi Engine": "", "Serper API Key": "คีย์ API ของ Serper", "Serply API Key": "คีย์ API ของ Serply", "Serpstack API Key": "คีย์ API ของ Serpstack", "Server connection verified": "ยืนยันการเชื่อมต่อเซิร์ฟเวอร์แล้ว", "Session": "", "Set as default": "ตั้งเป็นค่าเริ่มต้น", "Set CFG Scale": "", "Set Default Model": "ตั้งโมเดลเริ่มต้น", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "ตั้งค่าโมเดลการฝัง (เช่น {{model}})", "Set Image Size": "ตั้งค่าขนาดภาพ", "Set reranking model (e.g. {{model}})": "ตั้งค่าโมเดลการจัดอันดับใหม่ (เช่น {{model}})", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "ตั้งค่าขั้นตอน", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "ตั้งค่าเสียง", "Set whisper model": "", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets how far back for the model to look back to prevent repetition.": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "", "Sets the size of the context window used to generate the next token.": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "การตั้งค่า", "Settings saved successfully!": "บันทึกการตั้งค่าเรียบร้อยแล้ว!", "Share": "แชร์", "Share Chat": "แชร์แชท", "Share to Open WebUI Community": "แชร์ไปยังชุมชน OpenWebUI", "Share your background and interests": "", "Sharing Permissions": "", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "แสดง", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "แสดงรายละเอียดผู้ดูแลระบบในหน้าจอรอการอนุมัติบัญชี", "Show All": "", "Show Formatting Toolbar": "", "Show image preview": "", "Show Less": "", "Show Model": "", "Show shortcuts": "แสดงทางลัด", "Show your support!": "แสดงการสนับสนุนของคุณ!", "Showcased creativity": "แสดงความคิดสร้างสรรค์", "Sign in": "ลงชื่อเข้าใช้", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "ลงชื่อออก", "Sign up": "สมัครสมาชิก", "Sign up to {{WEBUI_NAME}}": "", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to False.": "", "Signing in to {{WEBUI_NAME}}": "", "Sink List": "", "sk-1234": "", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Something went wrong :/": "", "Sonar": "", "Sonar Deep Research": "", "Sonar Pro": "", "Sonar Reasoning": "", "Sonar Reasoning Pro": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "แหล่งที่มา", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "ข้อผิดพลาดในการรู้จำเสียง: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "เครื่องมือแปลงเสียงเป็นข้อความ", "Start of the channel": "จุดเริ่มต้นของช่อง", "Start Tag": "", "STDOUT/STDERR": "STDOUT/STDERR", "Stop": "", "Stop Generating": "", "Stop Sequence": "หยุดลำดับ", "Stream Chat Response": "", "Stream Delta Chunk Size": "", "Strikethrough": "", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "โมเดลแปลงเสียงเป็นข้อความ", "STT Settings": "การตั้งค่าแปลงเสียงเป็นข้อความ", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "คำบรรยาย (เช่น เกี่ยวกับจักรวรรดิโรมัน)", "Success": "สำเร็จ", "Successfully imported {{userCount}} users.": "", "Successfully updated.": "อัปเดตเรียบร้อยแล้ว", "Suggest a change": "", "Suggested": "แนะนำ", "Support": "สนับสนุน", "Support this plugin:": "สนับสนุนปลั๊กอินนี้:", "Supported MIME Types": "", "Sync directory": "", "System": "ระบบ", "System Instructions": "", "System Prompt": "ระบบพรอมต์", "Tags": "", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "", "Talk to model": "พูดคุยกับโมเดล", "Tap to interrupt": "แตะเพื่อขัดจังหวะ", "Task List": "", "Task Model": "", "Tasks": "", "Tavily API Key": "คีย์ API ของ Tavily", "Tavily Extract Depth": "", "Tell us more:": "บอกเรามากขึ้น:", "Temperature": "อุณหภูมิ", "Temporary Chat": "", "Temporary Chat by Default": "", "Text Splitter": "", "Text-to-Speech": "", "Text-to-Speech Engine": "เครื่องมือแปลงข้อความเป็นเสียง", "Thanks for your feedback!": "ขอบคุณสำหรับความคิดเห็นของคุณ!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "นักพัฒนาที่อยู่เบื้องหลังปลั๊กอินนี้เป็นอาสาสมัครที่มีชื่นชอบการแบ่งบัน หากคุณพบว่าปลั๊กอินนี้มีประโยชน์ โปรดพิจารณาสนับสนุนการพัฒนาของเขา", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The passwords you entered don't quite match. Please double-check and try again.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "คะแนนควรอยู่ระหว่าง 0.0 (0%) ถึง 1.0 (100%)", "The stream delta chunk size for the model. Increasing the chunk size will make the model respond with larger pieces of text at once.": "", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "", "The Weight of BM25 Hybrid Search. 0 more lexical, 1 more semantic. Default 0.5": "", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "ธีม", "Thinking...": "กำลังคิด...", "This action cannot be undone. Do you wish to continue?": "การกระทำนี้ไม่สามารถย้อนกลับได้ คุณต้องการดำเนินการต่อหรือไม่?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "", "This chat won't appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "สิ่งนี้ทำให้มั่นใจได้ว่าการสนทนาที่มีค่าของคุณจะถูกบันทึกอย่างปลอดภัยในฐานข้อมูลแบ็กเอนด์ของคุณ ขอบคุณ!", "This feature is experimental and may be modified or discontinued without notice.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "นี่เป็นฟีเจอร์ทดลอง อาจไม่ทำงานตามที่คาดไว้และอาจมีการเปลี่ยนแปลงได้ตลอดเวลา", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "สิ่งนี้จะลบ", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Thorough explanation": "คำอธิบายอย่างละเอียด", "Thought for {{DURATION}}": "", "Thought for {{DURATION}} seconds": "", "Thought for less than a second": "", "Thread": "กระทู้", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "จำเป็นต้องมี URL ของเซิร์ฟเวอร์ Tika", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "เคล็ดลับ: อัปเดตช่องตัวแปรหลายช่องติดต่อกันโดยการกดปุ่มแท็บในช่องใส่ข้อความแชทหลังจากแต่ละการแทนที่", "Title": "ชื่อเรื่อง", "Title (e.g. Tell me a fun fact)": "ชื่อเรื่อง (เช่น บอกข้อเท็จจริงที่น่าสนุก)", "Title Auto-Generation": "การสร้างชื่ออัตโนมัติ", "Title cannot be an empty string.": "ชื่อเรื่องไม่สามารถเป็นสตริงว่างได้", "Title Generation": "", "Title Generation Prompt": "พรอมต์การสร้างชื่อเรื่อง", "TLS": "", "To access the available model names for downloading,": "ในการเข้าถึงชื่อโมเดลที่มีให้ดาวน์โหลด", "To access the GGUF models available for downloading,": "ในการเข้าถึงโมเดล GGUF ที่มีให้ดาวน์โหลด", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "ในการเข้าถึง WebUI โปรดติดต่อผู้ดูแลระบบ ผู้ดูแลระบบสามารถจัดการสถานะผู้ใช้จากแผงควบคุมผู้ดูแลระบบ", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To learn more about powerful prompt variables, click here": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "ในการเลือกฟิลเตอร์ที่นี่ ให้เพิ่มไปยังพื้นที่ทำงาน \"ฟังก์ชัน\" ก่อน", "To select toolkits here, add them to the \"Tools\" workspace first.": "ในการเลือกชุดเครื่องมือที่นี่ ให้เพิ่มไปยังพื้นที่ทำงาน \"เครื่องมือ\" ก่อน", "Toast notifications for new updates": "", "Today": "วันนี้", "Toggle search": "", "Toggle settings": "สลับการตั้งค่า", "Toggle sidebar": "สลับแถบด้านข้าง", "Toggle whether current connection is active.": "", "Token": "", "Too verbose": "", "Tool created successfully": "สร้างเครื่องมือเรียบร้อยแล้ว", "Tool deleted successfully": "ลบเครื่องมือเรียบร้อยแล้ว", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "นำเข้าเครื่องมือเรียบร้อยแล้ว", "Tool Name": "", "Tool Servers": "", "Tool updated successfully": "อัปเดตเครื่องมือเรียบร้อยแล้ว", "Tools": "เครื่องมือ", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "เครื่องมือคือระบบการเรียกใช้ฟังก์ชันที่สามารถดำเนินการโค้ดใดๆ ได้", "Tools Function Calling Prompt": "", "Tools have a function calling system that allows arbitrary code execution.": "เครื่องมือมีระบบการเรียกใช้ฟังก์ชันที่สามารถดำเนินการโค้ดใดๆ ได้", "Tools Public Sharing": "", "Top K": "Top K", "Top K Reranker": "", "Transformers": "", "Trouble accessing Ollama?": "มีปัญหาในการเข้าถึง Ollama?", "Trust Proxy Environment": "", "Try Again": "", "TTS Model": "โมเดลแปลงข้อความเป็นเสียง", "TTS Settings": "การตั้งค่าแปลงข้อความเป็นเสียง", "TTS Voice": "เสียงแปลงข้อความเป็นเสียง", "Type": "ประเภท", "Type Hugging Face Resolve (Download) URL": "พิมพ์ URL ของ Hugging Face Resolve (Download)", "Uh-oh! There was an issue with the response.": "", "UI": "ส่วนติดต่อผู้ใช้", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Underline": "", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "", "Unpin": "ยกเลิกการปักหมุด", "Unravel secrets": "", "Unsupported file type.": "", "Untagged": "", "Untitled": "", "Update": "อัปเดต", "Update and Copy Link": "อัปเดตและคัดลอกลิงก์", "Update for the latest features and improvements.": "", "Update password": "อัปเดตรหัสผ่าน", "Updated": "", "Updated at": "อัปเดตเมื่อ", "Updated At": "", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "", "Upload": "อัปโหลด", "Upload a GGUF model": "อัปโหลดโมเดล GGUF", "Upload Audio": "", "Upload directory": "", "Upload files": "", "Upload Files": "อัปโหลดไฟล์", "Upload Pipeline": "อัปโหลดพายป์ไลน์", "Upload Progress": "ความคืบหน้าการอัปโหลด", "Upload Progress: {{uploadedFiles}}/{{totalFiles}} ({{percentage}}%)": "", "URL": "", "URL is required": "", "URL Mode": "โหมด URL", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "", "Use groups to group your users and assign permissions.": "", "Use LLM": "", "Use no proxy to fetch page contents.": "", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "user": "ผู้ใช้", "User": "", "User Groups": "", "User location successfully retrieved.": "ดึงตำแหน่งที่ตั้งของผู้ใช้เรียบร้อยแล้ว", "User menu": "", "User Webhooks": "", "Username": "", "Users": "ผู้ใช้", "Using Entire Document": "", "Using Focused Retrieval": "", "Using the default arena model with all models. Click the plus button to add custom models.": "ใช้โมเดล Arena กับโมเดลทั้งหมด คลิกปุ่มบวกเพื่อเพิ่มโมเดลที่กำหนดเอง", "Valid time units:": "หน่วยเวลาใช้ได้:", "Validate certificate": "", "Valves": "วาล์ว", "Valves updated": "วาล์วที่อัปเดตแล้ว", "Valves updated successfully": "อัปเดตวาล์วเรียบร้อยแล้ว", "variable": "ตัวแปร", "Verify Connection": "", "Verify SSL Certificate": "", "Version": "เวอร์ชัน", "Version {{selectedVersion}} of {{totalVersions}}": "", "View Replies": "", "View Result from **{{NAME}}**": "", "Visibility": "", "Vision": "", "Voice": "เสียง", "Voice Input": "", "Voice mode": "", "Warning": "คำเตือน", "Warning:": "คำเตือน:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "คำเตือน: หากคุณอัปเดตหรือเปลี่ยนโมเดลการฝัง คุณจะต้องนำเข้าเอกสารทั้งหมดอีกครั้ง", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "", "Web": "เว็บ", "Web API": "เว็บ API", "Web Loader Engine": "", "Web Search": "การค้นหาเว็บ", "Web Search Engine": "เครื่องมือค้นหาเว็บ", "Web Search in Chat": "", "Web Search Query Generation": "", "Webhook URL": "URL ของ Webhook", "WebUI Settings": "การตั้งค่า WebUI", "WebUI URL": "", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "What are you trying to achieve?": "", "What are you working on?": "", "What's New in": "มีอะไรใหม่ใน", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "<PERSON>his<PERSON> (โลคอล)", "Why?": "", "Widescreen Mode": "โหมดหน้าจอกว้าง", "Width": "", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "", "Workspace": "พื้นที่ทำงาน", "Workspace Permissions": "", "Write": "", "Write a prompt suggestion (e.g. Who are you?)": "เขียนคำแนะนำพรอมต์ (เช่น คุณคือใคร?)", "Write a summary in 50 words that summarizes [topic or keyword].": "เขียนสรุปใน 50 คำที่สรุป [หัวข้อหรือคำสำคัญ]", "Write something...": "", "Write your model system prompt content here\ne.g.) You are Mario from Super Mario Bros, acting as an assistant.": "เขียนเนื้อหา system prompt ของโมเดลของคุณที่นี่\nเช่น: คุณคือมาริโอจาก Super Mario Bros และทำหน้าที่เป็นผู้ช่วย", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "เมื่อวาน", "You": "คุณ", "You are currently using a trial license. Please contact support to upgrade your license.": "", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "คุณสามารถปรับแต่งการโต้ตอบของคุณกับ LLMs โดยเพิ่มความทรงจำผ่านปุ่ม 'จัดการ' ด้านล่าง ทำให้มันมีประโยชน์และเหมาะกับคุณมากขึ้น", "You cannot upload an empty file.": "", "You do not have permission to upload files.": "", "You have no archived conversations.": "คุณไม่มีการสนทนาที่เก็บถาวร", "You have shared this chat": "คุณได้แชร์แชทนี้แล้ว", "You're a helpful assistant.": "คุณคือผู้ช่วยที่มีประโยชน์", "You're now logged in.": "คุณเข้าสู่ระบบแล้ว", "Your Account": "", "Your account status is currently pending activation.": "สถานะบัญชีของคุณกำลังรอการเปิดใช้งาน", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "การสนับสนุนทั้งหมดของคุณจะไปยังนักพัฒนาปลั๊กอินโดยตรง; Open WebUI ไม่รับส่วนแบ่งใด ๆ อย่างไรก็ตาม แพลตฟอร์มการระดมทุนที่เลือกอาจมีค่าธรรมเนียมของตัวเอง", "Youtube": "Youtube", "Youtube Language": "", "Youtube Proxy URL": ""}