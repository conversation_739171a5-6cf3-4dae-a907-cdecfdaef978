{"-1 for no limit, or a positive integer for a specific limit": "-1 para ilimitado, ou un número enteiro positivo para un límite específico.", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' o '-1' para evitar expiración.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(p.ej. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(p.ej. `sh webui.sh --api`)", "(latest)": "(O mais recente)", "(leave blank for to use commercial endpoint)": "", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "{{ models }}", "{{COUNT}} Available Tools": "", "{{COUNT}} characters": "", "{{COUNT}} extracted lines": "", "{{COUNT}} hidden lines": "", "{{COUNT}} Replies": "{{COUNT}} Respostas", "{{COUNT}} words": "", "{{model}} download has been canceled": "", "{{user}}'s Chats": "Chats do {{user}}", "{{webUIName}} Backend Required": "{{webUIName}} Ser<PERSON>or <PERSON>", "*Prompt node ID(s) are required for image generation": "Os ID do nodo son requeridos para a xeneración de imáxes", "A new version (v{{LATEST_VERSION}}) is now available.": "Unha nova versión (v{{LATEST_VERSION}}) está disponible.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "o modelo de tarefas utilizase realizando tarefas como a xeneración de títulos para chats e consultas da búsqueda web", "a user": "un usuario", "About": "Sobre nos", "Accept autocomplete generation / Jump to prompt variable": "", "Access": "Acceso", "Access Control": "Control de Acceso", "Accessible to all users": "Accesible para todos os usuarios", "Account": "Conta", "Account Activation Pending": "Activación da conta pendente", "Accurate information": "Información precisa", "Action": "", "Action not found": "", "Action Required for Chat Log Storage": "Requírese unha acción para gardar o rexistro do chat", "Actions": "Accións", "Activate": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Active este comando escribindo \"/{{COMMAND}}\" no chat", "Active": "Activo", "Active Users": "Usuarios activos", "Add": "Agregar", "Add a model ID": "Agregado ID do modelo", "Add a short description about what this model does": "Agregue unha breve descripción sobre o que fai este modelo", "Add a tag": "Agregar unha etiqueta", "Add Arena Model": "Agregar un modelo a aArena", "Add Connection": "Agregar Conexión", "Add Content": "Agregar contido", "Add content here": "Agrege contido aquí", "Add Custom Parameter": "", "Add custom prompt": "Agregar un prompt personalizado", "Add Details": "", "Add Files": "<PERSON>g<PERSON><PERSON>", "Add Group": "Agregar Grupo", "Add Memory": "Agregar <PERSON>", "Add Model": "Agregar <PERSON>", "Add Reaction": "Agregar <PERSON>", "Add Tag": "Agregar etiqueta", "Add Tags": "agregar etiquetas", "Add text content": "Aña<PERSON> contido de texto", "Add User": "<PERSON><PERSON><PERSON><PERSON>", "Add User Group": "Agregar usuario al grupo", "Additional Config": "", "Additional configuration options for marker. This should be a JSON string with key-value pairs. For example, '{\"key\": \"value\"}'. Supported keys include: disable_links, keep_pageheader_in_output, keep_pagefooter_in_output, filter_blank_pages, drop_repeated_text, layout_coverage_threshold, merge_threshold, height_tolerance, gap_threshold, image_threshold, min_line_length, level_count, default_level": "", "Adjusting these settings will apply changes universally to all users.": "Axustar estas opcions aplicará os cambios universalmente a todos os usuarios.", "admin": "admin", "Admin": "Admin", "Admin Panel": "Panel de Administración", "Admin Settings": "Configuración de Administrador", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Os administradores teñen acceso a todas as ferramentas en todo momento; os usuarios necesitan ferramentas asignadas por modelo no espacio de trabajo.", "Advanced Parameters": "<PERSON>rá<PERSON><PERSON>", "Advanced Params": "Pará<PERSON><PERSON>", "AI": "", "All": "", "All Documents": "Todos os Documentos", "All models deleted successfully": "Todos os modelos han sido borrados", "Allow Call": "", "Allow Chat Controls": "Permitir Control dos Chats", "Allow Chat Delete": "<PERSON><PERSON><PERSON>", "Allow Chat Deletion": "<PERSON><PERSON><PERSON>", "Allow Chat Edit": "<PERSON><PERSON><PERSON><PERSON>", "Allow Chat Export": "", "Allow Chat Params": "", "Allow Chat Share": "", "Allow Chat System Prompt": "", "Allow Chat Valves": "", "Allow Continue Response": "", "Allow Delete Messages": "", "Allow File Upload": "<PERSON><PERSON><PERSON>", "Allow Multiple Models in Chat": "", "Allow non-local voices": "Permitir voces non locales", "Allow Rate Response": "", "Allow Regenerate Response": "", "Allow Speech to Text": "", "Allow Temporary Chat": "<PERSON><PERSON><PERSON>", "Allow Text to Speech": "", "Allow User Location": "Permitir Ubicación do Usuario", "Allow Voice Interruption in Call": "Permitir interrupción de voz en chamada", "Allowed Endpoints": "Puntos finais permitidos", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "¿Xa tes unha conta?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "", "Always": "Sempre", "Always Collapse Code Blocks": "", "Always Expand Details": "", "Always Play Notification Sound": "", "Amazing": "Sorprendente", "an assistant": "un asistente", "An error occurred while fetching the explanation": "", "Analytics": "", "Analyzed": "<PERSON><PERSON><PERSON><PERSON>", "Analyzing...": "Analizando..", "and": "e", "and {{COUNT}} more": "e {{COUNT}} mais", "and create a new shared link.": "e xerar un novo enlace compartido.", "Android": "", "API": "", "API Base URL": "Dirección URL da API", "API Base URL for Datalab Marker service. Defaults to: https://www.datalab.to/api/v1/marker": "", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "<PERSON><PERSON> da <PERSON> ", "API Key created.": "<PERSON>ve da <PERSON> creada.", "API Key Endpoint Restrictions": "Restriccions de Endpoint de Chave de API", "API keys": "<PERSON><PERSON> da <PERSON>", "API Version": "", "API Version is required": "", "Application DN": "Aplicacion DN", "Application DN Password": " Contrasinal  da Aplicacion DN", "applies to all users with the \"user\" role": "aplicacar a todos os usuarios co rol \"user\" ", "April": "Abril", "Archive": "<PERSON><PERSON><PERSON><PERSON>", "Archive All Chats": "<PERSON><PERSON><PERSON><PERSON> todos os chats", "Archived Chats": "Chats arquivados", "archived-chat-export": "Exportación de chats arquivados", "Are you sure you want to clear all memories? This action cannot be undone.": "", "Are you sure you want to delete this channel?": "¿Seguro que queres eliminar este canal?", "Are you sure you want to delete this message?": "¿Seguro que queres eliminar este mensaxe? ", "Are you sure you want to unarchive all archived chats?": "¿Estás seguro de que quieres desArquivar todos os chats arquivados?", "Are you sure?": "¿E<PERSON>á seguro?", "Arena Models": "Area de Modelos", "Artifacts": "Artefactos", "Ask": "", "Ask a question": "<PERSON>ai unha pregunta", "Assistant": "<PERSON><PERSON><PERSON>", "Attach file from knowledge": "", "Attention to detail": "Detalle preciso", "Attribute for Mail": "Atributo para correo", "Attribute for Username": "Atributo para o nome do usuario", "Audio": "Audio", "August": "Agosto", "Auth": "", "Authenticate": "Autenticar", "Authentication": "", "Auto": "", "Auto-Copy Response to Clipboard": "Copiar a resposta automáticamente o portapapeis", "Auto-playback response": "Respuesta de reproducción automática", "Autocomplete Generation": "xeneración de autocompletado", "Autocomplete Generation Input Max Length": "Longitud máxima de entrada da xeneración de autocompletado", "Automatic1111": "AUTOMATIC1111", "AUTOMATIC1111 Api Auth String": "API de autenticación para a instancia de AUTOMATIC1111", "AUTOMATIC1111 Base URL": "Dirección URL de AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "A dirección URL de AUTOMATIC1111 e requerida.", "Available list": "Lista dispoñible", "Available Tools": "", "available users": "usuarios dispoñibles", "available!": "¡dispoñible!", "Away": "Ausente", "Awful": "Horrible", "Azure AI Speech": "Voz de Azure AI", "Azure OpenAI": "Azure OpenAI", "Azure Region": "Rexión de Azure", "Back": "Volver", "Bad Response": "<PERSON><PERSON><PERSON><PERSON>a", "Banners": "Mensaxes emerxentes", "Base Model (From)": "Modelo base (desde)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "Bearer": "", "before": "antes", "Being lazy": "<PERSON> pregizeiro", "Beta": "Beta", "Bing Search V7 Endpoint": "Punto final da busqueda de Bing versión V7", "Bing Search V7 Subscription Key": "Chave de suscripción da busqueda de Bing versión V7", "Bio": "", "Birth Date": "", "BM25 Weight": "", "Bocha Search API Key": "", "Bold": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "Chave de API da busqueda Brave", "Bullet List": "", "Button ID": "", "Button Label": "", "Button Prompt": "", "By {{name}}": "Por {{name}}", "Bypass Embedding and Retrieval": "", "Bypass Web Loader": "", "Cache Base Model List": "", "Calendar": "", "Call": "<PERSON><PERSON>", "Call feature is not supported when using Web STT engine": "A funcionalidade da chamada non pode usarse xunto co motor da STT Web", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "<PERSON><PERSON><PERSON>", "Capabilities": "Capacidades", "Capture": "Captura", "Capture Audio": "", "Certificate Path": "Ruta para os Certificados", "Change Password": "Cambiar o contrasinal ", "Channel deleted successfully": "", "Channel Name": "Nome do Canal", "Channel updated successfully": "", "Channels": "Canal", "Character": "<PERSON><PERSON>", "Character limit for autocomplete generation input": "Limite de caracteres para a entrada de xeneración do autocompletado", "Chart new frontiers": "Dibuxar novas fronteiras", "Chat": "Cha<PERSON>", "Chat Background Image": "Imaxe de fondo do Chat", "Chat Bubble UI": "Burbuxa do chat UI", "Chat Controls": "<PERSON><PERSON>is do chat", "Chat Conversation": "", "Chat direction": "Dirección do Chat", "Chat ID": "", "Chat moved successfully": "", "Chat Overview": "Vista xeral do chat", "Chat Permissions": "Permisos do Chat", "Chat Tags Auto-Generation": "Auto-xeneración das Etiquetas para o Chat", "Chats": "Chats", "Check Again": "Verifica de novo", "Check for updates": "Verificar actualizacions", "Checking for updates...": "Verificando actualizacions...", "Choose a model before saving...": "Escolle un modelo antes de gardar os cambios...", "Chunk Overlap": "Superposición de fragmentos", "Chunk Size": "Tamaño de fragmentos", "Ciphers": "Cifrado", "Citation": "Cita", "Citations": "", "Clear memory": "Liberar memoria", "Clear Memory": "Limpar memoria", "click here": "Pica aquí", "Click here for filter guides.": "Pica aquí para guías de filtros", "Click here for help.": "Pica aquí para obter axuda.", "Click here to": "Pica aquí para", "Click here to download user import template file.": "Pica aquí para descargar o arquivo de plantilla de importación de usuario.", "Click here to learn more about faster-whisper and see the available models.": "Clic aquí para aprender mais sobre faster-whisper y ver os modelos disponibles.", "Click here to see available models.": "", "Click here to select": "Pica aquí para seleccionar", "Click here to select a csv file.": "Pica aquí para seleccionar un arquivo csv.", "Click here to select a py file.": "Pica aquí para seleccionar un arquivo py.", "Click here to upload a workflow.json file.": "Pica aquí para subir un arquivo workflow.json.", "click here.": "Pica aquí.", "Click on the user role button to change a user's role.": "Pica no botón de roles do usuario para cambiar su rol.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Permisos de escritura do portapapeles denegados. Por favor, comprueba as configuracions de tu navegador para otorgar o acceso necesario.", "Clone": "Clonar", "Clone Chat": "<PERSON><PERSON><PERSON> chat", "Clone of {{TITLE}}": "Clon de {{TITLE}}", "Close": "<PERSON><PERSON><PERSON>", "Close Banner": "", "Close Configure Connection Modal": "", "Close modal": "", "Close settings modal": "", "Close Sidebar": "", "CMU ARCTIC speaker embedding name": "", "Code Block": "", "Code execution": "Execución de código", "Code Execution": "Execución de código", "Code Execution Engine": "Motor de execución de código", "Code Execution Timeout": "Tempo de espera esgotado para a execución de código", "Code formatted successfully": "Formateouse correctamente o código.", "Code Interpreter": "Interprete de Código", "Code Interpreter Engine": "Motor interprete de código", "Code Interpreter Prompt Template": "Exemplos de Prompt para o Interprete de Código", "Collapse": "Esconder", "Collection": "Colección", "Color": "Cor", "ComfyUI": "ComfyUI", "ComfyUI API Key": "Chave da <PERSON> de ComfyUI", "ComfyUI Base URL": "ComfyUI Base URL", "ComfyUI Base URL is required.": "ComfyUI Base URL e requerido.", "ComfyUI Workflow": "Fluxo de traballo de ComfyUI", "ComfyUI Workflow Nodes": "Nodos para ComfyUI Workflow", "Comma separated Node Ids (e.g. 1 or 1,2)": "", "Command": "Comand<PERSON>", "Comment": "", "Completions": "Respostas autoxeradas", "Compress Images in Channels": "", "Concurrent Requests": "Solicitudes simultáneas", "Config imported successfully": "", "Configure": "Configurar", "Confirm": "Confirmar", "Confirm Password": "Confirma<PERSON> ", "Confirm your action": "Confirma a tua acción", "Confirm your new password": "Confirmar o teu novo contrasinal ", "Confirm Your Password": "", "Connect to your own OpenAI compatible API endpoints.": "Conecta os teus propios Api compatibles con OpenAI.", "Connect to your own OpenAPI compatible external tool servers.": "", "Connection failed": "", "Connection successful": "", "Connection Type": "", "Connections": "Conexions", "Connections saved successfully": "", "Connections settings updated": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "Define o esforzo do modelo de razonamento, so aplicable a modelos de razonamento de provedores específicos que soportan o esforzo de razonamento.", "Contact Admin for WebUI Access": "Contacta o administrador para obter acceso o WebUI", "Content": "<PERSON><PERSON><PERSON>", "Content Extraction Engine": "Motor extractor de contido", "Continue Response": "<PERSON><PERSON><PERSON><PERSON>", "Continue with {{provider}}": "Continuar co {{provider}}", "Continue with Email": "Continuar co email", "Continue with LDAP": "Continuar co LDAP", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Controlar como o texto do mensaxe se divide para as solicitudes de TTS. 'Puntuation' divide en oracions, 'paragraphs' divide en párrafos e 'none' manten o mensaxe como unha sola cadea.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "Controla a repetición de secuencias de tokens no texto xerado. Un valor máis alto (por exemplo, 1,5) penalizará as repeticións con máis forza, mentres que un valor máis baixo (por exemplo, 1,1) será mais tolerante. Cando é 1, está desactivado.", "Controls": "Controles", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "Controla o equilibrio entre a coherencia e a diversidade da saída. Un valor máis baixo dará como resultado un texto máis centrado e coherente.", "Conversation saved successfully": "", "Copied": "Copiado", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "¡URL de chat compartido copiado o portapapeis!", "Copied to clipboard": "Copiado o portapapeis", "Copy": "Copiar", "Copy Formatted Text": "", "Copy last code block": "Copia o último bloque de código", "Copy last response": "Copia a última respuesta", "Copy link": "", "Copy Link": "<PERSON><PERSON><PERSON> enlace", "Copy to clipboard": "Copiado o portapapeis", "Copying to clipboard was successful!": "!A copia o portapapeis realizouse correctamente!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "O CORS debe estar debidamente configurado polo provedor para permitir solicitudes desde Open WebUI.", "Create": "Xerar", "Create a knowledge base": "Xerar base de conocemento", "Create a model": "Xerar un modelo", "Create Account": "<PERSON>erar unha conta", "Create Admin Account": "Xerar conta administrativa", "Create Channel": "Xerar Canal", "Create Folder": "", "Create Group": "Xerar grupo", "Create Knowledge": "<PERSON><PERSON><PERSON>", "Create new key": "Xerar unha nova chave", "Create new secret key": "Xerar unha nova chave secreta", "Create Note": "", "Create your first note by clicking on the plus button below.": "", "Created at": "Creado en", "Created At": "Creado en", "Created by": "<PERSON><PERSON>o por", "CSV Import": "Importa un CSV", "Ctrl+Enter to Send": "", "Current Model": "Modelo Actual", "Current Password": "contrasinal  Actual", "Custom": "Personalizado", "Custom description enabled": "", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "", "Dark": "Oscuro", "Database": "Base de datos", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "DD/MM/YYYY": "", "December": "Decembro", "Deepgram": "", "Default": "Por defecto", "Default (Open AI)": "Predetermina<PERSON> (Open AI)", "Default (SentenceTransformers)": "Predeterminado (SentenceTransformers)", "Default action buttons will be used.": "", "Default description enabled": "", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "O modo predeterminado funciona con unha gama mais ampla de modelos chamando as ferramentas unha vez antes da execución. o modo nativo aproveita as capacidades integradas de chamada de ferramentas do modelo, pero requiere que o modelo soporte esta función de manera inherente.", "Default Model": "<PERSON><PERSON> predeterminado", "Default model updated": "O modelo por defecto foi actualizado", "Default Models": "Modelos predeterminados", "Default permissions": "Permisos predeterminados", "Default permissions updated successfully": "Permisos predeterminados actualizados correctamente", "Default Prompt Suggestions": "Sugerencias de mensaxes por defecto", "Default to 389 or 636 if TLS is enabled": "Predeterminado a 389 o 636 si TLS está habilitado", "Default to ALL": "Predeterminado a TODOS", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "", "Default User Role": "Rol por defecto para os usuarios", "Delete": "Bo<PERSON>r", "Delete a model": "Borra un modelo", "Delete All Chats": "Eliminar todos os chats", "Delete All Models": "Eliminar todos os modelos", "Delete chat": "Bo<PERSON>r chat", "Delete Chat": "<PERSON><PERSON><PERSON>", "Delete chat?": "Borrar o chat?", "Delete folder?": "¿Eliminar carpeta?", "Delete function?": "Borrar afunción?", "Delete Message": "Elimina<PERSON> mensaxe", "Delete message?": "", "Delete note?": "", "Delete prompt?": "Borrar o prompt?", "delete this link": "Bo<PERSON>r este enlace", "Delete tool?": "Borrar a ferramenta", "Delete User": "<PERSON><PERSON><PERSON>", "Deleted {{deleteModelTag}}": "Se borró {{deleteModelTag}}", "Deleted {{name}}": "Eliminado {{nombre}}", "Deleted User": "<PERSON><PERSON>rio eliminado", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "Describe a tua base de coñecementos e obxetivos", "Description": "Descripción", "Detect Artifacts Automatically": "", "Dictate": "", "Didn't fully follow instructions": "Non sigueu as instruccions", "Direct": "", "Direct Connections": "Conexións directas", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "Conexións directas permiten aos usuarios conectar cos seus propios puntos finais de API compatibles con OpenAI.", "Direct Tool Servers": "", "Directory selection was cancelled": "", "Disable Code Interpreter": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "Desactivado", "Discover a function": "Descubre unha función", "Discover a model": "Descubrir un modelo", "Discover a prompt": "Descubre un Prompt", "Discover a tool": "Descubre unha ferramenta", "Discover how to use Open WebUI and seek support from the community.": "Descubra como usar Open WebUI e busque apoio na comunidade.", "Discover wonders": "Descubre maravillas", "Discover, download, and explore custom functions": "Descubre, descarga y explora funcións personalizadas", "Discover, download, and explore custom prompts": "Des<PERSON><PERSON>, descarga, y explora Prompts personalizados", "Discover, download, and explore custom tools": "Descubre, descarga y explora ferramentas personalizadas", "Discover, download, and explore model presets": "Descubre, descarga y explora ajustes preestablecidos de modelos", "Display": "Mostrar", "Display Emoji in Call": "Muestra Emoji en chamada", "Display Multi-model Responses in Tabs": "", "Display the username instead of You in the Chat": "Mostrar o nombre de usuario en lugar de Vostede no chat", "Displays citations in the response": "Muestra citas en arespuesta", "Dive into knowledge": "Sumérgete no coñecemento", "Do not install functions from sources you do not fully trust.": "Non instale funcións desde fontes nas que no confíe totalmente.", "Do not install tools from sources you do not fully trust.": "Non instale ferramentas desde fontes nas que no confíe totalmente.", "Docling": "", "Docling Server URL required.": "", "Document": "Documento", "Document Intelligence": "Inteligencia documental", "Document Intelligence endpoint required.": "", "Documentation": "Documentación", "Documents": "Documentos", "does not make any external connections, and your data stays securely on your locally hosted server.": "non realiza ninguna conexión externa y sus datos permanecen seguros en su servidor alojado localmente.", "Domain Filter List": "", "don't fetch random pipelines from sources you don't trust.": "Non obteñas pipelines aleatorias de fontes non fiables.", "Don't have an account?": "¿Non tes unha conta?", "don't install random functions from sources you don't trust.": "Non instale funcións aleatorias desde fontes nas que non confíe.", "don't install random tools from sources you don't trust.": "Non instale ferramentas aleatorias desde fontes nas que non confíe.", "Don't like the style": "Non che gusta o estilo?", "Done": "<PERSON><PERSON>", "Download": "<PERSON><PERSON><PERSON>", "Download & Delete": "Descargar e eliminar", "Download as SVG": "", "Download canceled": "Descarga cancelada", "Download Database": "Descarga a Base de Datos", "Drag and drop a file to upload or select a file to view": "Arrastra y suelta un Arquivo para subirlo o selecciona un Arquivo para verlo", "Draw": "<PERSON><PERSON><PERSON>", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "p.ej. '30s','10m'. Unidades válidas detempo son 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "", "e.g. 60": "e.g. 60", "e.g. A filter to remove profanity from text": "p.ej. Un filtro para eliminar a profanidade do texto", "e.g. en": "", "e.g. My Filter": "p.ej. O meu Filtro", "e.g. My Tools": "p.ej. As miñas ferramentas", "e.g. my_filter": "p.ej. meu_filtro", "e.g. my_tools": "p.ej. mi<PERSON><PERSON>_ferramentas", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "p.ej. ferramentas para realizar diversas operacions", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "e.g., westus (leave blank for eastus)": "", "Edit": "<PERSON><PERSON>", "Edit Arena Model": "Editar modelo de Arena", "Edit Channel": "Editar Canal", "Edit Connection": "Editar Conexión", "Edit Default Permissions": "Editar permisos predeterminados", "Edit Folder": "", "Edit Memory": "<PERSON><PERSON>", "Edit User": "<PERSON><PERSON>", "Edit User Group": "Editar grupo de usuarios", "Edited": "", "Editing": "", "Eject": "", "ElevenLabs": "ElevenLabs", "Email": "Email", "Embark on adventures": "Emprende aventuras", "Embedding": "", "Embedding Batch Size": "Tamaño de Embedding", "Embedding Model": "Modelo de Embedding", "Embedding Model Engine": "Motor de Modelo de Embedding", "Embedding model set to \"{{embedding_model}}\"": "Modelo de Embedding configurado a \"{{embedding_model}}\"", "Enable API Key": "Habilitar chave de API", "Enable autocomplete generation for chat messages": "Habilitar xeneración de autocompletado para mensaxes de chat", "Enable Code Execution": "Habilitar a execución de código", "Enable Code Interpreter": "Habilitar o interprete de código", "Enable Community Sharing": "Habilitar o uso compartido da comunidad", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Habilitar o bloqueo de memoria (mlock) para evitar que os datos do modelo se intercambien da RAM. Esta opción bloquea o conxunto de páxinas de traballo do modelo na RAM, asegurando que non se intercambiarán ao disco. Isto pode axudar a manter o rendemento evitando fallos de páxina e garantindo un acceso rápido aos datos.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Habilitar o mapeo de memoria (mmap) para cargar os datos do modelo. Esta opción permite ao sistema usar o almacenamento en disco como unha extensión da RAM tratando os arquivos de disco como se estivesen na RAM. Isto pode mellorar o rendemento do modelo permitindo un acceso máis rápido aos datos. Sen embargo, pode non funcionar correctamente con todos os sistemas e pode consumir unha cantidade significativa de espazo en disco.", "Enable Message Rating": "Habilitar a calificación de os mensaxes", "Enable Mirostat sampling for controlling perplexity.": "Habilitar o muestreo de Mirostat para controlar Perplexity.", "Enable New Sign Ups": "Habilitar novos Registros", "Enable, disable, or customize the reasoning tags used by the model. \"Enabled\" uses default tags, \"Disabled\" turns off reasoning tags, and \"Custom\" lets you specify your own start and end tags.": "", "Enabled": "Activado", "End Tag": "", "Endpoint URL": "", "Enforce Temporary Chat": "", "Enhance": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "asegurese de o teu arquivo CSV inclúe 4 columnas nesta orde: Nome, Email, Contrasinal, Rol.", "Enter {{role}} message here": "Ingrese o mensaxe {{role}} aquí", "Enter a detail about yourself for your LLMs to recall": "Ingrese un detalle sobre vostede para que as suas LLMs recorden", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "Ingrese a cadena de autorización de api (p.ej., nombre:contrasinal )", "Enter Application DN": "Ingrese a DN da aplicación", "Enter Application DN Password": "Ingrese a contrasinal  da DN da aplicación", "Enter Bing Search V7 Endpoint": "Ingrese o punto final de Bing Search V7", "Enter Bing Search V7 Subscription Key": "Ingrese a chave de suscripción de Bing Search V7", "Enter Bocha Search API Key": "Ingresa a chave de API da busqueda de Bocha", "Enter Brave Search API Key": "Ingresa a chave de API de busqueda de Brave", "Enter certificate path": "Ingrese a ruta do certificado", "Enter CFG Scale (e.g. 7.0)": "Ingresa a escala de CFG (p.ej., 7.0)", "Enter Chunk Overlap": "Ingresar superposición de fragmentos", "Enter Chunk Size": "Ingrese o tamaño do fragmento", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter coordinates (e.g. 51.505, -0.09)": "", "Enter Datalab Marker API Base URL": "", "Enter Datalab Marker API Key": "", "Enter description": "Ingrese a descripción", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "", "Enter Document Intelligence Endpoint": "Ingrese o punto final de Intelixencia de Documentos", "Enter Document Intelligence Key": "Ingrese a chave de Intelixencia de Documentos", "Enter domains separated by commas (e.g., example.com,site.org)": "Ingrese dominios separados por comas (p.ej., example.com,site.org)", "Enter Exa API Key": "Ingrese a chave API de Exa", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter folder name": "", "Enter Github Raw URL": "Ingresa a URL sin procesar de Github", "Enter Google PSE API Key": "Ingrese a chave API de Google PSE", "Enter Google PSE Engine Id": "Introduzca o ID do motor PSE de Google", "Enter hex color (e.g. #FF0000)": "", "Enter ID": "", "Enter Image Size (e.g. 512x512)": "Ingrese o tamaño da imaxen (p.ej. 512x512)", "Enter Jina API Key": "Ingrese a chave API de Jina", "Enter JSON config (e.g., {\"disable_links\": true})": "", "Enter Jupyter Password": "Ingrese o contrasinal de Jupyter", "Enter Jupyter Token": "Ingrese o <PERSON> de <PERSON>", "Enter Jupyter URL": "Ingrese a URL de Jupyter", "Enter Kagi Search API Key": "Ingrese a chave API de Kagi Search", "Enter Key Behavior": "Ingrese o comportamento da chave", "Enter language codes": "Ingrese códigos de idioma", "Enter Mistral API Key": "", "Enter Model ID": "Ingresa o ID do modelo", "Enter model tag (e.g. {{modelTag}})": "Ingrese a etiqueta do modelo (p.ej. {{modelTag}})", "Enter Mojeek Search API Key": "Ingrese a chave API de Mojeek Search", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "Ingrese o número de pasos (p.ej., 50)", "Enter Perplexity API Key": "Ingrese a chave API de Perplexity", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "Ingrese a URL do proxy (p.ej. **************************:port)", "Enter reasoning effort": "Ingrese o esfuerzo de razonamiento", "Enter Sampler (e.g. Euler a)": "Ingrese o sampler (p.ej., Euler a)", "Enter Scheduler (e.g. Karras)": "Ingrese o planificador (p.ej., <PERSON><PERSON><PERSON>)", "Enter Score": "Ingrese a puntuación", "Enter SearchApi API Key": "Ingrese a chave API de SearchApi", "Enter SearchApi Engine": "Ingrese o motor de SearchApi", "Enter Searxng Query URL": "Introduzca a URL de consulta de Searxng", "Enter Seed": "Ingrese a semilla", "Enter SerpApi API Key": "Ingrese a chave API de SerpApi", "Enter SerpApi Engine": "Ingrese o motor de SerpApi", "Enter Serper API Key": "Ingrese a chave API de Serper", "Enter Serply API Key": "Ingrese a chave API de Serply", "Enter Serpstack API Key": "Ingrese a chave API de Serpstack", "Enter server host": "Ingrese o host do servidor", "Enter server label": "Ingrese a etiqueta do servidor", "Enter server port": "Ingrese o puerto do servidor", "Enter Sougou Search API sID": "", "Enter Sougou Search API SK": "", "Enter stop sequence": "Ingrese a secuencia de parada", "Enter system prompt": "Ingrese o prompt do sistema", "Enter system prompt here": "", "Enter Tavily API Key": "Ingrese a chave API de Tavily", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "Ingrese a URL pública da sua WebUI. Esta URL utilizaráse para generar enlaces en as notificacions.", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "Ingrese a URL do servidor Tika", "Enter timeout in seconds": "Ingrese o tempo de espera en segundos", "Enter to Send": "Ingrese para enviar", "Enter Top K": "Ingrese o Top K", "Enter Top K Reranker": "", "Enter URL (e.g. http://127.0.0.1:7860/)": "Ingrese a URL (p.ej., http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Ingrese a URL (p.ej., http://localhost:11434)", "Enter value": "", "Enter value (true/false)": "", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your code here...": "Introduce o teu código aquí...", "Enter your current password": "Ingrese o seu contrasinal  actual", "Enter Your Email": "Ingrese o seu correo electrónico", "Enter Your Full Name": "Ingrese o seu nome completo", "Enter your gender": "", "Enter your message": "Ingrese o seu mensaxe", "Enter your name": "", "Enter Your Name": "", "Enter your new password": "Ingrese o seu novo contrasinal ", "Enter Your Password": "Ingrese o seu contrasinal ", "Enter Your Role": "Ingrese o seu rol", "Enter Your Username": "Ingrese o seu nome de usuario", "Enter your webhook URL": "Ingrese a sua URL de webhook", "Error": "Error", "ERROR": "ERROR", "Error accessing directory": "", "Error accessing Google Drive: {{error}}": "Error o acceder a Google Drive: {{error}}", "Error accessing media devices.": "", "Error starting recording.": "", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "Error o subir o Arquivo: {{error}}", "Error: A model with the ID '{{modelId}}' already exists. Please select a different ID to proceed.": "", "Error: Model ID cannot be empty. Please enter a valid ID to proceed.": "", "Evaluations": "Evaluacions", "Everyone": "", "Exa API Key": "chave API de Exa", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Exemplo: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Exemplo: TODOS", "Example: mail": "Exemplo: correo", "Example: ou=users,dc=foo,dc=example": "Exemplo: ou=usuarios,dc=foo,dc=Exemplo", "Example: sAMAccountName or uid or userPrincipalName": "Exemplo: sAMAccountName o uid o userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "", "Exclude": "Excluir", "Execute code for analysis": "Ejecutar código para análisis", "Executing **{{NAME}}**...": "", "Expand": "expandir", "Experimental": "Experimental", "Explain": "Explicar", "Explore the cosmos": "Explora o cosmos", "Export": "Exportar", "Export All Archived Chats": "Exportar todos os chats Arquivados", "Export All Chats (All Users)": "Exportar todos os chats (Todos os usuarios)", "Export chat (.json)": "Exportar chat (.json)", "Export Chats": "Exportar Chats", "Export Config to JSON File": "Exportar configuración a Arquivo JSON", "Export Functions": "Exportar Funcions", "Export Models": "Exportar Modelos", "Export Presets": "Exportar ajustes preestablecidos", "Export Prompt Suggestions": "", "Export Prompts": "Exportar Prompts", "Export to CSV": "Exportar a CSV", "Export Tools": "Exportar ferramentas", "Export Users": "", "External": "", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "", "External Web Loader URL": "", "External Web Search API Key": "", "External Web Search URL": "", "Fade Effect for Streaming Text": "", "Failed to add file.": "Non pudo agregarse o Arquivo.", "Failed to connect to {{URL}} OpenAPI tool server": "", "Failed to copy link": "", "Failed to create API Key.": "Non pudo xerarse a chave API.", "Failed to delete note": "", "Failed to extract content from the file: {{error}}": "", "Failed to extract content from the file.": "", "Failed to fetch models": "Non puderon obterse os modelos", "Failed to generate title": "", "Failed to load chat preview": "", "Failed to load file content.": "", "Failed to move chat": "", "Failed to read clipboard contents": "Non pudo Lerse o contido do portapapeles", "Failed to save connections": "", "Failed to save conversation": "Non puido gardarse a conversa", "Failed to save models configuration": "Non pudogardarse a configuración de os modelos", "Failed to update settings": "Falla al actualizar os ajustes", "Failed to upload file.": "Falla al subir o Arquivo.", "Features": "Características", "Features Permissions": "Permisos de características", "February": "<PERSON><PERSON><PERSON>", "Feedback Details": "", "Feedback History": "Historial de retroalimentación", "Feedbacks": "Retroalimentacions", "Feel free to add specific details": "Libre de agregar detalles específicos", "Female": "", "File": "Arquivo", "File added successfully.": "Arquivo agregado correctamente.", "File content updated successfully.": "Contido do Arquivo actualizado correctamente.", "File Mode": "Modo de Arquivo", "File not found.": "Arquivo non encontrado.", "File removed successfully.": "Arquivo eliminado correctamente.", "File size should not exceed {{maxSize}} MB.": "Tamaño do Arquivo non debe exceder {{maxSize}} MB.", "File Upload": "", "File uploaded successfully": "Arquivo subido correctamente", "Files": "<PERSON>r<PERSON><PERSON>", "Filter": "", "Filter is now globally disabled": "O filtro ahora está desactivado globalmente", "Filter is now globally enabled": "O filtro ahora está habilitado globalmente", "Filters": "<PERSON><PERSON><PERSON>", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Destectouse suplantación de pegadas: Non puderon usarse as iniciais como avatar. Por defecto utilizase a imaxen de perfil predeterminada.", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Floating Quick Actions": "", "Focus chat input": "Enfoca a entrada do chat", "Folder deleted successfully": "Carpeta eliminada correctamente", "Folder Name": "", "Folder name cannot be empty.": "O nome da carpeta non pode estar vacío", "Folder name updated successfully": "Nombre da carpeta actualizado correctamente", "Folder updated successfully": "", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "<PERSON><PERSON><PERSON> as instruccions perfectamente", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "Forxa novos caminos", "Form": "De", "Format Lines": "", "Format the lines in the output. Defaults to False. If set to True, the lines will be formatted to detect inline math and styles.": "", "Format your variables using brackets like this:": "Formatea tus variables usando corchetes así:", "Formatting may be inconsistent from source.": "", "Forwards system user session credentials to authenticate": "", "Full Context Mode": "", "Function": "Función", "Function Calling": "chamada de función", "Function created successfully": "Función creada exitosamente", "Function deleted successfully": "Función borrada exitosamente", "Function Description": "Descripción da función", "Function ID": "ID da función", "Function imported successfully": "", "Function is now globally disabled": "a función ahora está desactivada globalmente", "Function is now globally enabled": "Afunción está habilitada globalmente", "Function Name": "Nombre da función", "Function updated successfully": "Función actualizada exitosamente", "Functions": "Funcions", "Functions allow arbitrary code execution.": "Funcions habilitan aexecución de código arbitrario.", "Functions imported successfully": "Funcions importadas exitosamente", "Gemini": "", "Gemini API Config": "", "Gemini API Key is required.": "", "Gender": "", "General": "General", "Generate": "", "Generate an image": "<PERSON><PERSON> unha imaxen", "Generate Image": "Generar imaxen", "Generate prompt pair": "", "Generating search query": "xeneración de consultas de búsqueda", "Generating...": "", "Get information on {{name}} in the UI": "", "Get started": "Empezar", "Get started with {{WEBUI_NAME}}": "<PERSON><PERSON>zar con {{WEBUI_NAME}}", "Global": "Global", "Good Response": "Buena Respuesta", "Google Drive": "Google Drive", "Google PSE API Key": "chave API de Google PSE", "Google PSE Engine Id": "ID do motor PSE de Google", "Gravatar": "", "Group": "Grupo", "Group created successfully": "Grupo creado correctamente", "Group deleted successfully": "Grupo eliminado correctamente", "Group Description": "Descripción do grupo", "Group Name": "Nome do grupo", "Group updated successfully": "Grupo actualizado correctamente", "Groups": "Grupos", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "Retroalimentación háptica", "Height": "", "Hello, {{name}}": "<PERSON><PERSON>, {{name}}", "Help": "axuda", "Help us create the best community leaderboard by sharing your feedback history!": "¡Axudanos a xerar o mejor tablero de líderes da comunidad compartindo o teu historial de retroalimentación!", "Hex Color": "<PERSON><PERSON>", "Hex Color - Leave empty for default color": "Cor Hex - deixa vacío para o color predeterminado", "Hide": "Esconder", "Hide from Sidebar": "", "Hide Model": "", "High": "", "High Contrast Mode": "", "Home": "", "Host": "Host", "How can I help you today?": "¿Cómo podo axudarche hoxe?", "How would you rate this response?": "¿Cómo calificarías esta resposta?", "HTML": "", "Hybrid Search": "Búsqueda Híbrida", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "<PERSON>eg<PERSON>u que leu e entendo as implicacions da miña acción. Estou consciente dos riscos asociados con a execución de código arbitrario e verificou a confianza da fonte.", "ID": "ID", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "Encender a curiosidad", "Image": "imaxen", "Image Compression": "Compresión de imaxen", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "xeneración de imaxes", "Image Generation (Experimental)": "xeneración de imaxes (experimental)", "Image Generation Engine": "Motor de xeneración de imaxes", "Image Max Compression Size": "Tamaño máximo de compresión de imaxen", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "xeneración de prompt de imaxen", "Image Prompt Generation Prompt": "Prompt de xeneración de prompt de imaxen", "Image Settings": "<PERSON><PERSON><PERSON><PERSON> Imaxe", "Images": "imaxes", "Import": "", "Import Chats": "Importar chats", "Import Config from JSON File": "Importar configuración desde Arquivo JSON", "Import From Link": "", "Import Functions": "Importar Funcions", "Import Models": "Importar modelos", "Import Notes": "", "Import Presets": "Importar ajustes preestablecidos", "Import Prompt Suggestions": "", "Import Prompts": "Importar Prompts", "Import Tools": "Importar ferramentas", "Important Update": "Actualización importante", "Include": "Incluir", "Include `--api-auth` flag when running stable-diffusion-webui": "Incluir o indicador `--api-auth` al ejecutar stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "Incluir o indicador `--api` al ejecutar stable-diffusion-webui", "Includes SharePoint": "Inclúe SharePoint", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "", "Info": "Información", "Initials": "", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "", "Input": "", "Input commands": "Ingresar comandos", "Input Key (e.g. text, unet_name, steps)": "", "Input Variables": "", "Insert": "", "Insert Follow-Up Prompt to Input": "", "Insert Prompt as Rich Text": "", "Install from Github URL": "Instalar desde a URL de Github", "Instant Auto-Send After Voice Transcription": "Auto-Enviar despois da Transcripción de Voz", "Integration": "Integración", "Interface": "Interfaz", "Invalid file content": "", "Invalid file format.": "Formato de Arquivo inválido.", "Invalid JSON file": "", "Invalid JSON format for ComfyUI Workflow.": "", "Invalid JSON format in Additional Config": "", "Invalid Tag": "Etiqueta <PERSON>álid<PERSON>", "is typing...": "está escribindo...", "Italic": "", "January": "Xaneiro", "Jina API Key": "chave API de Jina", "join our Discord for help.": "Únase o noso Discord para obter axuda.", "JSON": "JSON", "JSON Preview": "Vista previa de JSON", "July": "<PERSON><PERSON>", "June": "<PERSON><PERSON>", "Jupyter Auth": "autenticación Jupyter", "Jupyter URL": "url <PERSON>", "JWT Expiration": "Expiración do JWT", "JWT Token": "Token JWT", "Kagi Search API Key": "chave API de Kagi Search", "Keep Follow-Up Prompts in Chat": "", "Keep in Sidebar": "", "Key": "Chave", "Key is required": "", "Keyboard shortcuts": "Atallos de teclado", "Knowledge": "coñ<PERSON><PERSON><PERSON>", "Knowledge Access": "Acceso al coñecemento", "Knowledge Base": "", "Knowledge created successfully.": "coñecemento creado exitosamente.", "Knowledge deleted successfully.": "coñecemento eliminado exitosamente.", "Knowledge Description": "", "Knowledge Name": "", "Knowledge Public Sharing": "", "Knowledge reset successfully.": "coñecemento restablecido exitosamente.", "Knowledge updated successfully": "coñecemento actualizado exitosamente.", "Kokoro.js (Browser)": "Kokoro .js (Navegador)", "Kokoro.js Dtype": "Kokoro .js Dtype", "Label": "Etiqueta", "Landing Page Mode": "Modo de Página de Inicio", "Language": "Lenguaje", "Language Locales": "", "Last Active": "Última Actividad", "Last Modified": "Modificado por última vez", "Last reply": "Última respuesta", "LDAP": "LDAP", "LDAP server updated": "Servidor LDAP actualizado", "Leaderboard": "Tablero de líderes", "Learn More": "", "Learn more about OpenAPI tool servers.": "", "Leave empty for no compression": "", "Leave empty for unlimited": "Deje vacío para ilimitado", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{url}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "Deixa vacío para incluir todos os modelos o seleccione modelos específicos", "Leave empty to use the default prompt, or enter a custom prompt": "Deixa vacío para usar o prompt predeterminado, o ingrese un prompt personalizado", "Leave model field empty to use the default model.": "Deixa o campo do modelo vacío para usar o modelo predeterminado.", "lexical": "", "License": "Licencia", "Lift List": "", "Light": "<PERSON><PERSON><PERSON>", "Listening...": "Escoitando...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "Os LLM poden cometer erros. Verifica a información importante.", "Loader": "Cargador", "Loading Kokoro.js...": "Cargando Kokoro.js...", "Loading...": "Cargando...", "Local": "Local", "Local Task Model": "", "Location access not allowed": "", "Lost": "<PERSON><PERSON><PERSON>", "Low": "", "LTR": "LTR", "Made by Open WebUI Community": "Feito por a comunidad de OpenWebUI", "Make password visible in the user interface": "", "Make sure to enclose them with": "Asegúrese de adxuntarlos con", "Make sure to export a workflow.json file as API format from ComfyUI.": "Asegúrese de exportar un Arquivo workflow.json en formato API desde ComfyUI.", "Male": "", "Manage": "Xestionar", "Manage Direct Connections": "Xestionar Conexións Directas", "Manage Models": "Xestionar modelos", "Manage Ollama": "Xestionar <PERSON>lla<PERSON>", "Manage Ollama API Connections": "Xestionar conexiones API de Ollama", "Manage OpenAI API Connections": "Xestionar conexiones API de OpenAI", "Manage Pipelines": "Administrar Pipelines", "Manage Tool Servers": "", "Manage your account information.": "", "March": "<PERSON><PERSON>", "Markdown": "", "Markdown (Header)": "", "Max Speakers": "", "Max Upload Count": "Cantidad máxima de cargas", "Max Upload Size": "Tamaño máximo de Cargas", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Podense descargar un máximo de 3 modelos simultáneamente. <PERSON><PERSON> favor, intenteo de novo mais tarde.", "May": "Mayo", "Medium": "", "Memories accessible by LLMs will be shown here.": "As memorias accesibles por os LLMs mostraránse aquí.", "Memory": "Memoria", "Memory added successfully": "Memoria añadida correctamente", "Memory cleared successfully": "Memoria liberada correctamente", "Memory deleted successfully": "Memoria borrada correctamente", "Memory updated successfully": "Memoria actualizada correctamente", "Merge Responses": "Fusionar Respuestas", "Merged Response": "Resposta combinada", "Message rating should be enabled to use this feature": "a calificación de mensaxes debe estar habilitada para usar esta función", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Os mensaxes que envíe despois de xerar su enlace no compartiránse. os usuarios co enlace podrán ver o chat compartido.", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "<PERSON><PERSON>", "Model '{{modelName}}' has been successfully downloaded.": "0 modelo '{{modelName}}' se ha descargado correctamente.", "Model '{{modelTag}}' is already in queue for downloading.": "0 modelo '{{modelTag}}' ya está en cola para descargar.", "Model {{modelId}} not found": "0 modelo {{modelId}} no fue encontrado", "Model {{modelName}} is not vision capable": "O  modelo {{modelName}} no es capaz de ver", "Model {{name}} is now {{status}}": "O  modelo {{name}} ahora es {{status}}", "Model {{name}} is now hidden": "", "Model {{name}} is now visible": "", "Model accepts file inputs": "", "Model accepts image inputs": "O modelo acepta entradas de imaxenes", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "Modelo creado correctamente!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Destectouse a ruta do sistema de ficheiros do modelo. É necesario o nome curto do modelo para a actualización, non se pode continuar.", "Model Filtering": "Filtrado de modelos", "Model ID": "ID do modelo", "Model ID is required.": "", "Model IDs": "IDs de modelos", "Model Name": "Nombre do modelo", "Model name already exists, please choose a different one": "", "Model Name is required.": "", "Model not selected": "Modelo no seleccionado", "Model Params": "Parámetros do modelo", "Model Permissions": "Permisos do modelo", "Model unloaded successfully": "", "Model updated successfully": "Modelo actualizado correctamente", "Model(s) do not support file upload": "", "Modelfile Content": "Contenido do Modelfile", "Models": "Modelos", "Models Access": "Acceso a modelos", "Models configuration saved successfully": "Configuración de modelos guardada correctamente", "Models Public Sharing": "", "Mojeek Search API Key": "chave API de Mojeek Search", "more": "mais", "More": "mais", "More Concise": "", "More Options": "", "Move": "", "Name": "Nombre", "Name and ID are required, please fill them out": "", "Name your knowledge base": "Nombra a tua base de coñecementos", "Native": "Nativo", "New Button": "", "New Chat": "Novo Chat", "New Folder": "Nova carpeta", "New Function": "", "New Note": "", "New Password": "Novo contrasinal ", "New Tool": "", "new-channel": "novo-canal", "Next message": "", "No chats found": "", "No chats found for this user.": "", "No chats found.": "", "No content": "", "No content found": "No se encontró contido", "No content found in file.": "", "No content to speak": "Non ten contido para falar", "No conversation to save": "", "No distance available": "Non ten distancia disponible", "No feedbacks found": "No se encontraron retroalimentacions", "No file selected": "Ningún arquivo fué seleccionado", "No groups with access, add a group to grant access": "Non ten grupos con acceso, agrega un grupo para otorgar acceso", "No HTML, CSS, or JavaScript content found.": "No se encontró contido HTML, CSS, o JavaScript.", "No inference engine with management support found": "No se encontró un motor de inferencia con soporte de gestión", "No knowledge found": "No se encontrou ningún coñecemento", "No memories to clear": "Non hay memorias que limpar", "No model IDs": "Non ten IDs de modelos", "No models found": "No se encontraron modelos", "No models selected": "No se seleccionaron modelos", "No Notes": "", "No results": "No se han encontrado resultados", "No results found": "No se han encontrado resultados", "No search query generated": "No se ha generado ninguna consulta de búsqueda", "No source available": "Non ten fuente disponible", "No suggestion prompts": "Sen prompts su<PERSON><PERSON><PERSON>", "No users were found.": "No se encontraron usuarios.", "No valves": "", "No valves to update": "Non ten válvulas para actualizar", "Node Ids": "", "None": "<PERSON><PERSON><PERSON>", "Not factually correct": "No es correcto en todos os aspectos", "Not helpful": "No útil", "Note deleted successfully": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Nota: Se estableces unha puntuación mínima, a búsqueda sólo devolverá documentos con unha puntuación mayor o igual a a puntuación mínima.", "Notes": "Notas", "Notification Sound": "Sonido de notificación", "Notification Webhook": "Webhook de notificación", "Notifications": "Notificacions", "November": "Noviembre", "OAuth ID": "OAuth ID", "October": "Octubre", "Off": "Desactivado", "Okay, Let's Go!": "<PERSON><PERSON>, ¡Vamos!", "OLED Dark": "OLED oscuro", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "Configuración de Ollama API actualizada", "Ollama Version": "Versión de Ollama", "On": "Activado", "OneDrive": "OneDrive", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "<PERSON><PERSON><PERSON> se <PERSON>en caracteres alfanuméricos y guiones", "Only alphanumeric characters and hyphens are allowed in the command string.": "<PERSON><PERSON><PERSON> se <PERSON>en caracteres alfanuméricos y guiones en a cadena de comando.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Solo se pueden editar as coleccions, xerar unha nova base de coñecementos para editar / añadir documentos", "Only markdown files are allowed": "", "Only select users and groups with permission can access": "Solo os usuarios y grupos con permiso pueden acceder", "Oops! Looks like the URL is invalid. Please double-check and try again.": "¡Ups! Parece que a URL no es válida. Vuelva a verificar e inténtelo novamente.", "Oops! There are files still uploading. Please wait for the upload to complete.": "¡Ups! Todavía hay arquivos subiendo. Por favor, espere a que a subida se complete.", "Oops! There was an error in the previous response.": "¡Ups! Hubo un error en a resposta anterior.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "¡Ups! Estás utilizando un método no compatible (solo frontend). Por favor ejecute a WebUI desde o backend.", "Open file": "Abrir arquivo", "Open in full screen": "Abrir en pantalla completa", "Open modal to configure connection": "", "Open Modal To Manage Floating Quick Actions": "", "Open new chat": "Abrir novo chat", "Open Sidebar": "", "Open User Profile Menu": "", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "Open WebUI usa faster-whisper internamente.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI usa SpeechT5 y embeddings de locutores CMU Arctic.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Aversión de Open WebUI (v{{OPEN_WEBUI_VERSION}}) es inferior a aversión requerida (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "Configuración de OpenAI API", "OpenAI API Key is required.": "Achave da API de OpenAI es requerida.", "OpenAI API settings updated": "Configuración de OpenAI API actualizada", "OpenAI URL/Key required.": "URL/chave de OpenAI es requerida.", "openapi.json URL or Path": "", "Optional": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "ou", "Ordered List": "", "Organize your users": "Organiza os teus usuarios", "Other": "Outro", "OUTPUT": "SAIDA", "Output format": "Formato de saida", "Output Format": "", "Overview": "Vista xeral", "page": "<PERSON><PERSON><PERSON><PERSON>", "Paginate": "", "Parameters": "", "Password": "Contrasinal ", "Passwords do not match.": "", "Paste Large Text as File": "Pegar texto grande como arquivo", "PDF document (.pdf)": "Documento PDF (.pdf)", "PDF Extract Images (OCR)": "Extraer imaxes de PDF (OCR)", "pending": "pendente", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "Permiso denegado al acceder a os dispositivos", "Permission denied when accessing microphone": "Permiso denegado al acceder a a micrófono", "Permission denied when accessing microphone: {{error}}": "Permiso denegado al acceder al micrófono: {{error}}", "Permissions": "<PERSON><PERSON><PERSON>", "Perplexity API Key": "", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "Personalización", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "<PERSON><PERSON>", "Pinned": "<PERSON><PERSON><PERSON>", "Pioneer insights": "Descubrir novas perspectivas", "Pipe": "", "Pipeline deleted successfully": "Pipeline borrada exitosamente", "Pipeline downloaded successfully": "Pipeline descargada exitosamente", "Pipelines": "Pipelines", "Pipelines are a plugin system with arbitrary code execution —": "Pipelines é un sistema de complementos con execución arbitraria de código —", "Pipelines Not Detected": "Pipeline No Detectada", "Pipelines Valves": "Tuberías Válvulas", "Plain text (.md)": "", "Plain text (.txt)": "Texto plano (.txt)", "Playground": "Patio de <PERSON>", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "Por favor revise con cuidado os siguientes avisos:", "Please do not close the settings page while loading the model.": "", "Please enter a message or attach a file.": "", "Please enter a prompt": "Por favor ingrese un prompt", "Please enter a valid path": "", "Please enter a valid URL": "", "Please fill in all fields.": "Por favor llene todos os campos.", "Please select a model first.": "Por favor seleccione un modelo primeiro.", "Please select a model.": "Por favor seleccione un modelo.", "Please select a reason": "Por favor seleccione unha razón", "Please wait until all files are uploaded.": "", "Port": "Puerto", "Positive attitude": "<PERSON>itud positiva", "Prefer not to say": "", "Prefix ID": "ID de prefijo", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "O ID de prefixo se utiliza para evitar conflictos con outras conexiones añadiendo un prefijo a os IDs de os modelos - deje vacío para deshabilitar", "Prevent file creation": "", "Preview": "", "Previous 30 days": "Últimos 30 días", "Previous 7 days": "Últimos 7 días", "Previous message": "", "Private": "", "Profile": "Perfil", "Prompt": "Prompt", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (por Exemplo, cuéntame unha cosa divertida sobre o Imperio Romano)", "Prompt Autocompletion": "", "Prompt Content": "Contenido do Prompt", "Prompt created successfully": "Prompt creado exitosamente", "Prompt suggestions": "Sugerencias de Prompts", "Prompt updated successfully": "Prompt actualizado exitosamente", "Prompts": "Prompts", "Prompts Access": "Acceso a Prompts", "Prompts Public Sharing": "", "Public": "", "Pull \"{{searchValue}}\" from Ollama.com": "Extraer \"{{searchValue}}\" de Ollama.com", "Pull a model from Ollama.com": "Obter un modelo de Ollama.com", "Query Generation Prompt": "Prompt de xeneración de consulta", "Quick Actions": "", "RAG Template": "Plantilla de RAG", "Rating": "Calificación", "Re-rank models by topic similarity": "Re-clasificar modelos por similitud de tema", "Read": "<PERSON>r", "Read Aloud": "Ler en voz alta", "Reason": "", "Reasoning Effort": "Esfuerzo de razonamiento", "Reasoning Tags": "", "Record": "", "Record voice": "Grabar voz", "Redirecting you to Open WebUI Community": "Redireccionándote a a comunidad OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Referirse a vostede mismo como \"Usuario\" (por Exemplo, \"O usuario está aprendiendo Español\")", "References from": "Referencias de", "Refused when it shouldn't have": "Rechazado cuando no debería", "Regenerate": "<PERSON><PERSON><PERSON>", "Regenerate Menu": "", "Reindex": "", "Reindex Knowledge Base Vectors": "", "Release Notes": "Notas da versión", "Releases": "", "Relevance": "Relevancia", "Relevance Threshold": "", "Remember Dismissal": "", "Remove": "Eliminar", "Remove {{MODELID}} from list.": "", "Remove file": "", "Remove File": "", "Remove image": "", "Remove Model": "Eliminar modelo", "Remove this tag from list": "", "Rename": "Renombrar", "Reorder Models": "Reordenar modelos", "Reply in Thread": "Responder no hilo", "Reranking Engine": "", "Reranking Model": "<PERSON><PERSON>rank<PERSON>", "Reset": "Reiniciar", "Reset All Models": "Reiniciar todos os modelos", "Reset Image": "Restablecer imaxe", "Reset Upload Directory": "Reiniciar Directorio de carga", "Reset Vector Storage/Knowledge": "Reiniciar almacenamiento de vectores/coñecemento", "Reset view": "Reiniciar vista", "Response": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "As notificacions de resposta non poden activarse debido a que os permisos do sitio web han sido denegados. Por favor, visite as configuracions da sua navegador para otorgar o acceso necesario.", "Response splitting": "División de respostas", "Response Watermark": "", "Result": "<PERSON><PERSON><PERSON><PERSON>", "RESULT": "<PERSON><PERSON><PERSON><PERSON>", "Retrieval": "recuperación", "Retrieval Query Generation": "xeneración de consulta de recuperación", "Rich Text Input for Chat": "Entrada de texto enriquecido para chat", "RK": "RK", "Role": "Rol", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "Executar", "Running": "Executando", "Running...": "Executando...", "Save": "Gardar", "Save & Create": "Gardar y xerar", "Save & Update": "Gardar y Actualizar", "Save As Copy": "Gardar como copia", "Save Chat": "", "Save Tag": "Gardar etiqueta", "Saved": "Gardado", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Xa non se admite gardar registros de chat directamente no almacenamiento da sua navegador. Tómese un momento para descargar y eliminar sus registros de chat haciendo clic no botón a continuación. No te preocupes, puedes volver a importar fácilmente tus registros de chat al backend a través de", "Scroll On Branch Change": "", "Search": "Buscar", "Search a model": "Buscar un modelo", "Search all emojis": "", "Search Base": "Base de búsqueda", "Search Chats": "Chats de búsqueda", "Search Collection": "<PERSON><PERSON>", "Search Filters": "Filtros de búsqueda", "search for archived chats": "", "search for folders": "", "search for pinned chats": "", "search for shared chats": "", "search for tags": "buscar etiquetas", "Search Functions": "Buscar Funcions", "Search In Models": "", "Search Knowledge": "Buscar coñecemento", "Search Models": "Buscar Modelos", "Search Notes": "", "Search options": "Opcions de búsqueda", "Search Prompts": "Buscar Prompts", "Search Result Count": "Recuento de resultados de búsqueda", "Search the internet": "Buscar en internet", "Search Tools": "Búsqueda de ferramentas", "SearchApi API Key": "chave API de SearchApi", "SearchApi Engine": "Motor de SearchApi", "Searched {{count}} sites": "Buscadas {{count}} sitios", "Searching \"{{searchQuery}}\"": "Buscando \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON> para \"{{searchQuery}}\"", "Searching the web...": "", "Searxng Query URL": "Searxng URL de consulta", "See readme.md for instructions": "Vea o readme.md para instruccions", "See what's new": "Ver as novedades", "Seed": "<PERSON><PERSON>", "Select": "", "Select a base model": "Seleccionar un modelo base", "Select a base model (e.g. llama3, gpt-4o)": "", "Select a conversation to preview": "", "Select a engine": "Busca un motor", "Select a function": "Busca unha función", "Select a group": "Seleccionar un grupo", "Select a language": "", "Select a mode": "", "Select a model": "Selecciona un modelo", "Select a model (optional)": "", "Select a pipeline": "Selección de unha Pipeline", "Select a pipeline url": "Selección de unha dirección URL de Pipeline", "Select a reranking model engine": "", "Select a role": "", "Select a theme": "", "Select a tool": "Busca unha ferramenta", "Select a voice": "", "Select an auth method": "", "Select an embedding model engine": "", "Select an engine": "", "Select an Ollama instance": "Se<PERSON><PERSON><PERSON>r unha instancia de Ollama", "Select an output format": "", "Select dtype": "", "Select Engine": "Selecciona Motor", "Select how to split message text for TTS requests": "", "Select Knowledge": "Selecciona coñecemento", "Select only one model to call": "Selecciona sólo un modelo para llamar", "Selected model(s) do not support image inputs": "Os modelos seleccionados no admiten entradas de imaxen", "semantic": "", "Semantic distance to query": "Distancia semántica a a consulta", "Send": "Enviar", "Send a Message": "Enviar un mensaxe", "Send message": "<PERSON><PERSON><PERSON> mensaxe", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Envia `stream_options: { include_usage: true }` en a solicitud.\nLos proveedores admitidos devolverán información de uso do token en a resposta cuando se establezca.", "September": "Septiembre", "SerpApi API Key": "", "SerpApi Engine": "", "Serper API Key": "chave API de Serper", "Serply API Key": "chave API de Serply", "Serpstack API Key": "chave API de Serpstack", "Server connection verified": "Conexión do servidor verificada", "Session": "", "Set as default": "<PERSON><PERSON><PERSON> por defecto", "Set CFG Scale": "Establecer la escala CFG", "Set Default Model": "Establecer modelo predeterminado", "Set embedding model": "Establecer modelo de embedding", "Set embedding model (e.g. {{model}})": "Establecer modelo de embedding (ej. {{model}})", "Set Image Size": "<PERSON><PERSON><PERSON> de <PERSON>", "Set reranking model (e.g. {{model}})": "Establecer modelo de reranking (ej. {{model}})", "Set Sampler": "<PERSON><PERSON><PERSON>", "Set Scheduler": "Establecer Programador", "Set Steps": "E<PERSON><PERSON>", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "Establece o número de capas que se descargarán na  GPU. Aumentar este valor puede mejorar significativamente o rendimiento de os modelos optimizados para la aceleración de GPU, pero también puede consumir mais energía y recursos de GPU.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "Establece o número de hilos de trabajo utilizados para o cálculo. Esta opción controla cuántos hilos se utilizan para procesar as solicitudes entrantes simultáneamente. Aumentar este valor puede mejorar o rendimiento bajo cargas de trabajo de alta concurrencia, pero también puede consumir mais recursos de CPU.", "Set Voice": "E<PERSON><PERSON> la voz", "Set whisper model": "E<PERSON>cer modelo de <PERSON>", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Establece un sesgo plano contra os tokens que han aparecido al menos una vez. Un valor más alto (por Exemplo, 1.5) penalizará más fuertemente las repeticiones, mientras que un valor más bajo (por Exemplo, 0.9) será más indulgente. En 0, está deshabilitado.", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Establece un sesgo de escala contra os tokens para penalizar las repeticiones, basado en cuántas veces han aparecido. Un valor más alto (por Exemplo, 1.5) penalizará más fuertemente las repeticiones, mientras que un valor más bajo (por Exemplo, 0.9) será más indulgente. En 0, está deshabilitado.", "Sets how far back for the model to look back to prevent repetition.": "E<PERSON>ce canto tempo debe retroceder o modelo para evitar a repetición.", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "Establece a semente de número aleatorio a usar para xeneración. Establecer esto a un número específico hará que el modelo genere el mismo texto para el mismo prompt.", "Sets the size of the context window used to generate the next token.": "Establece o tamaño da ventana de contexto utilizada para xenerar o seguinte token.", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Establece as secuencias de parada a usar. Cuando se encuentre este patrón, o LLM dejará de generar texto y devolverá. Se pueden establecer varios patrones de parada especificando múltiples parámetros de parada separados en un arquivo de modelo.", "Settings": "Configuración", "Settings saved successfully!": "¡Configuración gardada con éxito!", "Share": "Compartir", "Share Chat": "Compartir <PERSON>", "Share to Open WebUI Community": "Compartir coa comunidads OpenWebUI", "Share your background and interests": "", "Sharing Permissions": "", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "Mostrar", "Show \"What's New\" modal on login": "Mostrar modal \"Qué hay de novo\" al iniciar sesión", "Show Admin Details in Account Pending Overlay": "Mostrar detalles de administración na  capa de espera da conta", "Show All": "", "Show Formatting Toolbar": "", "Show image preview": "", "Show Less": "", "Show Model": "", "Show shortcuts": "<PERSON><PERSON> at<PERSON>s", "Show your support!": "¡Motra o teu apoio!", "Showcased creativity": "Creatividade mostrada", "Sign in": "In<PERSON><PERSON>", "Sign in to {{WEBUI_NAME}}": "Iniciar <PERSON><PERSON><PERSON> en {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "Iniciar se<PERSON><PERSON> en {{WEBUI_NAME}} con LDAP", "Sign Out": "<PERSON><PERSON><PERSON>", "Sign up": "<PERSON>erar unha conta", "Sign up to {{WEBUI_NAME}}": "Xerar unha conta en {{WEBUI_NAME}}", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to False.": "", "Signing in to {{WEBUI_NAME}}": "Iniciando sesi<PERSON> en {{WEBUI_NAME}}", "Sink List": "", "sk-1234": "sk-1234", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Something went wrong :/": "", "Sonar": "", "Sonar Deep Research": "", "Sonar Pro": "", "Sonar Reasoning": "", "Sonar Reasoning Pro": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "Fonte", "Speech Playback Speed": "Velocidad de reproducción de voz", "Speech recognition error: {{error}}": "Error de recoñecemento de voz: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "Motor de voz a texto", "Start of the channel": "<PERSON><PERSON><PERSON> da <PERSON>", "Start Tag": "", "STDOUT/STDERR": "STDOUT/STDERR", "Stop": "Detener", "Stop Generating": "", "Stop Sequence": "Detener secuencia", "Stream Chat Response": "Transmitir resposta de chat", "Stream Delta Chunk Size": "", "Strikethrough": "", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "Modelo STT", "STT Settings": "Configuracions de STT", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "Subtítulo (por Exemplo, sobre o Imperio Romano)", "Success": "Éxito", "Successfully imported {{userCount}} users.": "", "Successfully updated.": "Actualizado exitosamente.", "Suggest a change": "", "Suggested": "Sugerido", "Support": "Soporte", "Support this plugin:": "Brinda soporte a este plugin:", "Supported MIME Types": "", "Sync directory": "Sincroniza directorio", "System": "Sistema", "System Instructions": "Instruccions do sistema", "System Prompt": "Prompt do sistema", "Tags": "", "Tags Generation": "xeneración de etiquetas", "Tags Generation Prompt": "Prompt de xeneración de etiquetas", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "", "Talk to model": "<PERSON><PERSON><PERSON> co <PERSON>o", "Tap to interrupt": "Toca para interrumpir", "Task List": "", "Task Model": "", "Tasks": "<PERSON><PERSON><PERSON>", "Tavily API Key": "chave API de Tavily", "Tavily Extract Depth": "", "Tell us more:": "<PERSON><PERSON> mais:", "Temperature": "Temperatura", "Temporary Chat": "Chat temporal", "Temporary Chat by Default": "", "Text Splitter": "Divisor de texto", "Text-to-Speech": "", "Text-to-Speech Engine": "Motor de texto a voz", "Thanks for your feedback!": "¡Gracias pola tua retroalimentación!", "The Application Account DN you bind with for search": "A conta de aplicación DN que vincula para a búsqueda", "The base to search for users": "A base para buscar usuarios", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "O tamaño do lote determina canto texto se procesa xunto de unha vez. Un tamaño de lote maior pode aumentar o rendimiento y a velocidade do modelo, pero también requiere mais memoria.", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Os desarrolladores de este plugin son apasionados voluntarios da comunidade. Se encontras este plugin útil, por favor considere contribuir a seu desarrollo.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "O tableiro de líderes de evaluación basase no sistema de clasificación Elo e actualizase entempo real.", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "O atributo LDAP que se asigna al correo que os usuarios utilizan para iniciar sesión.", "The LDAP attribute that maps to the username that users use to sign in.": "O atributo LDAP que se asigna al nombre de usuario que os usuarios utilizan para iniciar sesión.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "O tablero de líderes está actualmente en beta y podemos axustar os cálculos de clasificación a medida que refinamos o algoritmo.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "O tamaño máximo do arquivo en MB. Si o tamaño do arquivo supera este límite, o arquivo no se subirá.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "O número máximo de arquivos que se pueden utilizar a la vez en chat. Si este límite es superado, os arquivos no se subirán.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The passwords you entered don't quite match. Please double-check and try again.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "La puntuación debe ser un valor entre 0.0 (0%) y 1.0 (100%).", "The stream delta chunk size for the model. Increasing the chunk size will make the model respond with larger pieces of text at once.": "", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "", "The Weight of BM25 Hybrid Search. 0 more lexical, 1 more semantic. Default 0.5": "", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "<PERSON><PERSON>", "Thinking...": "Pensando...", "This action cannot be undone. Do you wish to continue?": "Esta acción no se puede deshacer. ¿Desea continuar?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "", "This chat won't appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Esto garantiza que sus valiosas conversacions se guarden de forma segura en su base de datos no backend. ¡Gracias!", "This feature is experimental and may be modified or discontinued without notice.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Esta es unha característica experimental que puede no funcionar como se esperaba y está sujeto a cambios en cualquier momento.", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Esta opción eliminará todos os arquivos existentes na  colección y os reemplazará con novos arquivos subidos.", "This response was generated by \"{{model}}\"": "Esta resposta fue generada por \"{{model}}\"", "This will delete": "Esto eliminará", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "Esto eliminará <strong>{{NAME}}</strong> y <strong>todo su contido</strong>.", "This will delete all models including custom models": "Esto eliminará todos os modelos, incluidos os modelos personalizados", "This will delete all models including custom models and cannot be undone.": "Esto eliminará todos os modelos, incluidos os modelos personalizados y no se puede deshacer.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Esto reseteará la base de coñecementos y sincronizará todos os arquivos. ¿Desea continuar?", "Thorough explanation": "Explicación exhaustiva", "Thought for {{DURATION}}": "Pensamiento para {{DURATION}}", "Thought for {{DURATION}} seconds": "", "Thought for less than a second": "", "Thread": "Fío", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "URL do servidor de Tika", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Consello: Actualice múltiples variables consecutivamente presionando la tecla tab na  entrada do chat despois de cada reemplazo.", "Title": "<PERSON><PERSON><PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "Título (por Exemplo, cóntame unha curiosidad)", "Title Auto-Generation": "xeneración automática de títulos", "Title cannot be an empty string.": "O título non pode ser unha cadena vacía.", "Title Generation": "Xeneración de titulos", "Title Generation Prompt": "Prompt de xeneración de título", "TLS": "TLS", "To access the available model names for downloading,": "Para acceder os nomes de modelos dispoñibles para descargar,", "To access the GGUF models available for downloading,": "Para acceder os modelos GGUF dispoñibles para descargar,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Para acceder al interfaz de usuario web, por favor contacte al administrador. os administradores pueden administrar os estados de os usuarios desde o panel de administración.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Para adjuntar la base de coñecementos aquí, agreguelas al área de trabajo \"coñecemento\" primeiro.", "To learn more about available endpoints, visit our documentation.": "Para obter mais información sobre os endpoints disponibles, visite nuestra documentación.", "To learn more about powerful prompt variables, click here": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Para protexer a sua privacidade, solo compartense as calificacions, IDs dos modelos, etiquetas e metadatos da sua retroalimentación; os seus rexistros de chat permanecen privados e non se incluen.", "To select actions here, add them to the \"Functions\" workspace first.": "Para seleccionar accions aquí, agreguelas al área de trabajo \"Funcions\" primeiro.", "To select filters here, add them to the \"Functions\" workspace first.": "Para seleccionar filtros aquí, agreguelos al área de trabajo \"Funcions\" primeiro.", "To select toolkits here, add them to the \"Tools\" workspace first.": "Para seleccionar ferramentas aquí, agreguelas al área de trabajo \"Ferramentas\" primeiro.", "Toast notifications for new updates": "Notificacions emergentes para novas actualizacions", "Today": "Hoxe", "Toggle search": "", "Toggle settings": "Alternar configuración", "Toggle sidebar": "Alternar barra lateral", "Toggle whether current connection is active.": "", "Token": "Token", "Too verbose": "<PERSON><PERSON><PERSON><PERSON>", "Tool created successfully": "ferramenta creada con éxito", "Tool deleted successfully": "ferramenta eliminada con éxito", "Tool Description": "Descripción da ferramenta", "Tool ID": "ID da ferramenta", "Tool imported successfully": "ferramenta importada con éxito", "Tool Name": "Nome da ferramenta", "Tool Servers": "", "Tool updated successfully": "ferramenta actualizada con éxito", "Tools": "Ferramentas", "Tools Access": "Acceso a ferramentas", "Tools are a function calling system with arbitrary code execution": "As ferramentas son un sistema de chamada de funcións con código arbitrario", "Tools Function Calling Prompt": "Prompt de chamada de funcións de ferramentas", "Tools have a function calling system that allows arbitrary code execution.": "As ferramentas tienen un sistema de chamada de funcións que permite la execución de código arbitrario.", "Tools Public Sharing": "", "Top K": "Top K", "Top K Reranker": "", "Transformers": "Transformadores", "Trouble accessing Ollama?": "¿Problemas para acceder a Ollama?", "Trust Proxy Environment": "Confiar nos entornos de proxy", "Try Again": "", "TTS Model": "Modelo TTS", "TTS Settings": "Configuración de TTS", "TTS Voice": "Voz do TTS", "Type": "Tipo", "Type Hugging Face Resolve (Download) URL": "Escriba la URL (Descarga) de Hugging Face Resolve", "Uh-oh! There was an issue with the response.": "¡Ups! Hubo un problema con la respuesta.", "UI": "UI", "Unarchive All": "<PERSON><PERSON><PERSON><PERSON> todo", "Unarchive All Archived Chats": "Desarquivar todos os chats arquivados", "Unarchive Chat": "<PERSON>ar<PERSON><PERSON> chat", "Underline": "", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "Desbloquear misterios", "Unpin": "Desanclar", "Unravel secrets": "Desentrañar secretos", "Unsupported file type.": "", "Untagged": "Sin etiquetar", "Untitled": "", "Update": "Actualizar", "Update and Copy Link": "Actualizar y copiar enlace", "Update for the latest features and improvements.": "Actualize para as últimas características e mejoras.", "Update password": "Actualizar contrasinal ", "Updated": "Actualizado", "Updated at": "Actualizado en", "Updated At": "Actualizado en", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "", "Upload": "Subir", "Upload a GGUF model": "Subir un modelo GGUF", "Upload Audio": "", "Upload directory": "Directorio de carga", "Upload files": "Subir arquivos", "Upload Files": "Subir arquivos", "Upload Pipeline": "Subir Pipeline", "Upload Progress": "Progreso de carga", "Upload Progress: {{uploadedFiles}}/{{totalFiles}} ({{percentage}}%)": "", "URL": "URL", "URL is required": "", "URL Mode": "Modo de URL", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "Utilice '#' no prompt para cargar y incluir su coñecemento.", "Use groups to group your users and assign permissions.": "Use grupos para agrupar a sus usuarios y asignar permisos.", "Use LLM": "", "Use no proxy to fetch page contents.": "", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "user": "usuario", "User": "Usuario", "User Groups": "", "User location successfully retrieved.": "Localización do usuario recuperada con éxito.", "User menu": "", "User Webhooks": "", "Username": "Nombre de usuario", "Users": "Usuarios", "Using Entire Document": "", "Using Focused Retrieval": "", "Using the default arena model with all models. Click the plus button to add custom models.": "Usando o modelo de arena predeterminado con todos os modelos. Haga clic no botón mais para agregar modelos personalizados.", "Valid time units:": "Unidades válidas de tempo:", "Validate certificate": "", "Valves": "Valves", "Valves updated": "<PERSON>ves actualizados", "Valves updated successfully": "Valves actualizados con éxito", "variable": "variable", "Verify Connection": "", "Verify SSL Certificate": "", "Version": "Versión", "Version {{selectedVersion}} of {{totalVersions}}": "Versión {{selectedVersion}} de {{totalVersions}}", "View Replies": "Ver respuestas", "View Result from **{{NAME}}**": "", "Visibility": "Visibilidad", "Vision": "", "Voice": "Voz", "Voice Input": "Entrada de voz", "Voice mode": "", "Warning": "Advertencia", "Warning:": "Advertencia:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Advertencia: Habilitar esto permitirá a os usuarios subir código arbitrario no servidor.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Advertencia: Si actualiza o cambia su modelo de inserción, necesitará volver a importar todos os documentos.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "Aviso: A execución de Jupyter permite a execución de código arbitrario, o que supón riscos de seguridade graves - procede con extrema precaución.", "Web": "Web", "Web API": "API Web", "Web Loader Engine": "", "Web Search": "Búsqueda na  Web", "Web Search Engine": "Motor de búsqueda web", "Web Search in Chat": "Búsqueda web en chat", "Web Search Query Generation": "xeneración de consultas de búsqueda web", "Webhook URL": "Webhook URL", "WebUI Settings": "Configuración do WebUI", "WebUI URL": "URL do WebUI", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI hará solicitudes a \"{{url}}/api/chat\"", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI hará solicitudes a \"{{url}}/chat/completions\"", "What are you trying to achieve?": "¿Qué estás tratando de lograr?", "What are you working on?": "¿En qué estás trabajando?", "What's New in": "Novedades en", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "Cando está habilitado, o modelo responderá a cada mensaxe de chat en tempo real, generando unha resposta tan pronto como o usuario envíe un mensaxe. Este modo es útil para aplicacions de chat en vivo, pero puede afectar o rendimiento en hardware mais lento.", "wherever you are": "<PERSON><PERSON> queira que estés", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "Xextor de voz (Local)", "Why?": "¿Por qué?", "Widescreen Mode": "<PERSON><PERSON> de pan<PERSON> ancha", "Width": "", "Won": "Ganado", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "Funciona xunto con top-k. Un valor máis alto (por exemplo, 0,95) dará lugar a un texto máis diverso, mentres que un valor máis baixo (por exemplo, 0,5) xerará un texto máis centrado e conservador.", "Workspace": "Espacio de traballo", "Workspace Permissions": "Permisos do espacio de traballo", "Write": "Escribir", "Write a prompt suggestion (e.g. Who are you?)": "Escribe unha sugerencia para un prompt (por <PERSON>emplo, ¿quen eres?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Escribe un resumen en 50 palabras que resuma [tema ou palabra principal].", "Write something...": "Escribe algo...", "Write your model system prompt content here\ne.g.) You are Mario from Super Mario Bros, acting as an assistant.": "Escribe aquí o contido do system prompt do teu modelo\np. ex.: Es Mario de Super Mario Bros e actúas como asistente.", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "Onte", "You": "<PERSON><PERSON><PERSON>", "You are currently using a trial license. Please contact support to upgrade your license.": "Estás utilizando actualmente unha licenza de proba. Por favor, contacta co soporte para actualizar a túa licenza.", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Solo puede chatear con un máximo de {{maxCount}} arquivo(s) a la vez.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "<PERSON><PERSON><PERSON> personal<PERSON> as suas interaccions con LLMs añadiendo memorias a través do botón 'Xestionar' debajo, haciendo que sean mais útiles y personalizados para vostede.", "You cannot upload an empty file.": "Non pode subir un arquivo vacío.", "You do not have permission to upload files.": "Non ten permiso para subir arquivos.", "You have no archived conversations.": "Non ten conversacions arquivadas.", "You have shared this chat": "Vostede ha compartido esta conversación", "You're a helpful assistant.": "Vostede es un asistente útil.", "You're now logged in.": "Vostede ahora está conectado.", "Your Account": "", "Your account status is currently pending activation.": "O estado da sua conta actualmente encontrase pendente de activación.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "A sua contribución completa irá directamente o desarrollador do plugin; Open WebUI non toma ningun porcentaxe. Sin embargo, a plataforma de financiación elegida podría ter as suas propias tarifas.", "Youtube": "Youtube", "Youtube Language": "", "Youtube Proxy URL": ""}