{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'ਸ', 'ਮ', 'ਘੰ', 'ਦ', 'ਹਫ਼ਤਾ' ਜਾਂ '-1' ਬਿਨਾ ਮਿਆਦ ਦੇ।", "(e.g. `sh webui.sh --api --api-auth username_password`)": "", "(e.g. `sh webui.sh --api`)": "(ਉਦਾਹਰਣ ਦੇ ਤੌਰ ਤੇ `sh webui.sh --api`)", "(latest)": "(ਤਾਜ਼ਾ)", "(leave blank for to use commercial endpoint)": "", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "{{ ਮਾਡਲ }}", "{{COUNT}} Available Tools": "", "{{COUNT}} characters": "", "{{COUNT}} extracted lines": "", "{{COUNT}} hidden lines": "", "{{COUNT}} Replies": "", "{{COUNT}} words": "", "{{model}} download has been canceled": "", "{{user}}'s Chats": "{{user}} ਦੀਆਂ ਗੱਲਾਂ", "{{webUIName}} Backend Required": "{{webUIName}} ਬੈਕਐਂਡ ਲੋੜੀਂਦਾ ਹੈ", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "ਚੈਟਾਂ ਅਤੇ ਵੈੱਬ ਖੋਜ ਪੁੱਛਗਿੱਛਾਂ ਵਾਸਤੇ ਸਿਰਲੇਖ ਤਿਆਰ ਕਰਨ ਵਰਗੇ ਕਾਰਜ ਾਂ ਨੂੰ ਕਰਦੇ ਸਮੇਂ ਇੱਕ ਕਾਰਜ ਮਾਡਲ ਦੀ ਵਰਤੋਂ ਕੀਤੀ ਜਾਂਦੀ ਹੈ", "a user": "ਇੱਕ ਉਪਭੋਗਤਾ", "About": "ਬਾਰੇ", "Accept autocomplete generation / Jump to prompt variable": "", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "ਖਾਤਾ", "Account Activation Pending": "", "Accurate information": "ਸਹੀ ਜਾਣਕਾਰੀ", "Action": "", "Action not found": "", "Action Required for Chat Log Storage": "ਚੈਟ ਲਾਗ ਸੰਭਾਲਣ ਲਈ ਕਾਰਵਾਈ ਲੋੜੀਂਦੀ ਹੈ", "Actions": "", "Activate": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active": "ਸਕ੍ਰਿਆ", "Active Users": "", "Add": "ਸ਼ਾਮਲ ਕਰੋ", "Add a model ID": "", "Add a short description about what this model does": "ਇਸ ਬਾਰੇ ਇੱਕ ਸੰਖੇਪ ਵੇਰਵਾ ਸ਼ਾਮਲ ਕਰੋ ਕਿ ਇਹ ਮਾਡਲ ਕੀ ਕਰਦਾ ਹੈ", "Add a tag": "ਇੱਕ ਟੈਗ ਸ਼ਾਮਲ ਕਰੋ", "Add Arena Model": "", "Add Connection": "", "Add Content": "", "Add content here": "", "Add Custom Parameter": "", "Add custom prompt": "ਕਸਟਮ ਪ੍ਰੰਪਟ ਸ਼ਾਮਲ ਕਰੋ", "Add Details": "", "Add Files": "ਫਾਈਲਾਂ ਸ਼ਾਮਲ ਕਰੋ", "Add Group": "", "Add Memory": "ਮਿਹਾਨ ਸ਼ਾਮਲ ਕਰੋ", "Add Model": "ਮਾਡਲ ਸ਼ਾਮਲ ਕਰੋ", "Add Reaction": "", "Add Tag": "", "Add Tags": "ਟੈਗ ਸ਼ਾਮਲ ਕਰੋ", "Add text content": "", "Add User": "ਉਪਭੋਗਤਾ ਸ਼ਾਮਲ ਕਰੋ", "Add User Group": "", "Additional Config": "", "Additional configuration options for marker. This should be a JSON string with key-value pairs. For example, '{\"key\": \"value\"}'. Supported keys include: disable_links, keep_pageheader_in_output, keep_pagefooter_in_output, filter_blank_pages, drop_repeated_text, layout_coverage_threshold, merge_threshold, height_tolerance, gap_threshold, image_threshold, min_line_length, level_count, default_level": "", "Adjusting these settings will apply changes universally to all users.": "ਇਹ ਸੈਟਿੰਗਾਂ ਨੂੰ ਠੀਕ ਕਰਨ ਨਾਲ ਸਾਰੇ ਉਪਭੋਗਤਾਵਾਂ ਲਈ ਬਦਲਾਅ ਲਾਗੂ ਹੋਣਗੇ।", "admin": "ਪ੍ਰਬੰਧਕ", "Admin": "", "Admin Panel": "ਪ੍ਰਬੰਧਕ ਪੈਨਲ", "Admin Settings": "ਪ੍ਰਬੰਧਕ ਸੈਟਿੰਗਾਂ", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "", "Advanced Parameters": "ਉੱਚ ਸਤਰ ਦੇ ਪੈਰਾਮੀਟਰ", "Advanced Params": "ਐਡਵਾਂਸਡ ਪਰਮਜ਼", "AI": "", "All": "", "All Documents": "ਸਾਰੇ ਡਾਕੂਮੈਂਟ", "All models deleted successfully": "", "Allow Call": "", "Allow Chat Controls": "", "Allow Chat Delete": "", "Allow Chat Deletion": "ਗੱਲਬਾਤ ਮਿਟਾਉਣ ਦੀ ਆਗਿਆ ਦਿਓ", "Allow Chat Edit": "", "Allow Chat Export": "", "Allow Chat Params": "", "Allow Chat Share": "", "Allow Chat System Prompt": "", "Allow Chat Valves": "", "Allow Continue Response": "", "Allow Delete Messages": "", "Allow File Upload": "", "Allow Multiple Models in Chat": "", "Allow non-local voices": "", "Allow Rate Response": "", "Allow Regenerate Response": "", "Allow Speech to Text": "", "Allow Temporary Chat": "", "Allow Text to Speech": "", "Allow User Location": "", "Allow Voice Interruption in Call": "", "Allowed Endpoints": "", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "ਪਹਿਲਾਂ ਹੀ ਖਾਤਾ ਹੈ?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "", "Always": "", "Always Collapse Code Blocks": "", "Always Expand Details": "", "Always Play Notification Sound": "", "Amazing": "", "an assistant": "ਇੱਕ ਸਹਾਇਕ", "An error occurred while fetching the explanation": "", "Analytics": "", "Analyzed": "", "Analyzing...": "", "and": "ਅਤੇ", "and {{COUNT}} more": "", "and create a new shared link.": "ਅਤੇ ਇੱਕ ਨਵਾਂ ਸਾਂਝਾ ਲਿੰਕ ਬਣਾਓ।", "Android": "", "API": "", "API Base URL": "API ਬੇਸ URL", "API Base URL for Datalab Marker service. Defaults to: https://www.datalab.to/api/v1/marker": "", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "API ਕੁੰਜੀ", "API Key created.": "API ਕੁੰਜੀ ਬਣਾਈ ਗਈ।", "API Key Endpoint Restrictions": "", "API keys": "API ਕੁੰਜੀਆਂ", "API Version": "", "API Version is required": "", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "ਅਪ੍ਰੈਲ", "Archive": "ਆਰਕਾਈਵ", "Archive All Chats": "ਸਾਰੀਆਂ ਚੈਟਾਂ ਨੂੰ ਆਰਕਾਈਵ ਕਰੋ", "Archived Chats": "ਆਰਕਾਈਵ ਕੀਤੀਆਂ ਗੱਲਾਂ", "archived-chat-export": "", "Are you sure you want to clear all memories? This action cannot be undone.": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "ਕੀ ਤੁਸੀਂ ਯਕੀਨਨ ਹੋ?", "Arena Models": "", "Artifacts": "", "Ask": "", "Ask a question": "", "Assistant": "", "Attach file from knowledge": "", "Attention to detail": "ਵੇਰਵੇ 'ਤੇ ਧਿਆਨ", "Attribute for Mail": "", "Attribute for Username": "", "Audio": "ਆਡੀਓ", "August": "ਅਗਸਤ", "Auth": "", "Authenticate": "", "Authentication": "", "Auto": "", "Auto-Copy Response to Clipboard": "ਜਵਾਬ ਆਟੋ ਕਾਪੀ ਕਲਿੱਪਬੋਰਡ 'ਤੇ", "Auto-playback response": "ਆਟੋ-ਪਲੇਬੈਕ ਜਵਾਬ", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 ਬੇਸ URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 ਬੇਸ URL ਦੀ ਲੋੜ ਹੈ।", "Available list": "", "Available Tools": "", "available users": "ਉਪਲਬਧ ਯੂਜ਼ਰ", "available!": "ਉਪਲਬਧ ਹੈ!", "Away": "ਗੈਰਹਾਜ਼ਿਰ", "Awful": "", "Azure AI Speech": "", "Azure OpenAI": "Azure OpenAI", "Azure Region": "", "Back": "ਵਾਪਸ", "Bad Response": "ਖਰਾਬ ਜਵਾਬ", "Banners": "ਬੈਨਰ", "Base Model (From)": "ਬੇਸ ਮਾਡਲ (ਤੋਂ)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "Bearer": "", "before": "ਪਹਿਲਾਂ", "Being lazy": "ਆਲਸੀ ਹੋਣਾ", "Beta": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Bio": "", "Birth Date": "", "BM25 Weight": "", "Bocha Search API Key": "", "Bold": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "ਬਹਾਦਰ ਖੋਜ API ਕੁੰਜੀ", "Bullet List": "", "Button ID": "", "Button Label": "", "Button Prompt": "", "By {{name}}": "", "Bypass Embedding and Retrieval": "", "Bypass Web Loader": "", "Cache Base Model List": "", "Calendar": "", "Call": "", "Call feature is not supported when using Web STT engine": "", "Camera": "", "Cancel": "ਰੱਦ ਕਰੋ", "Capabilities": "ਸਮਰੱਥਾਵਾਂ", "Capture": "", "Capture Audio": "", "Certificate Path": "", "Change Password": "ਪਾਸਵਰਡ ਬਦਲੋ", "Channel deleted successfully": "", "Channel Name": "", "Channel updated successfully": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "ਗੱਲਬਾਤ", "Chat Background Image": "", "Chat Bubble UI": "ਗੱਲਬਾਤ ਬਬਲ UI", "Chat Controls": "", "Chat Conversation": "", "Chat direction": "ਗੱਲਬਾਤ ਡਿਰੈਕਟਨ", "Chat ID": "", "Chat moved successfully": "", "Chat Overview": "", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "ਗੱਲਾਂ", "Check Again": "ਮੁੜ ਜਾਂਚ ਕਰੋ", "Check for updates": "ਅੱਪਡੇਟ ਲਈ ਜਾਂਚ ਕਰੋ", "Checking for updates...": "ਅੱਪਡੇਟ ਲਈ ਜਾਂਚ ਰਿਹਾ ਹੈ...", "Choose a model before saving...": "ਸੰਭਾਲਣ ਤੋਂ ਪਹਿਲਾਂ ਇੱਕ ਮਾਡਲ ਚੁਣੋ...", "Chunk Overlap": "ਚੰਕ ਓਵਰਲੈਪ", "Chunk Size": "ਚੰਕ ਆਕਾਰ", "Ciphers": "", "Citation": "ਹਵਾਲਾ", "Citations": "", "Clear memory": "", "Clear Memory": "", "click here": "", "Click here for filter guides.": "", "Click here for help.": "ਮਦਦ ਲਈ ਇੱਥੇ ਕਲਿੱਕ ਕਰੋ।", "Click here to": "ਇੱਥੇ ਕਲਿੱਕ ਕਰੋ", "Click here to download user import template file.": "", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to see available models.": "", "Click here to select": "ਚੁਣਨ ਲਈ ਇੱਥੇ ਕਲਿੱਕ ਕਰੋ", "Click here to select a csv file.": "CSV ਫਾਈਲ ਚੁਣਨ ਲਈ ਇੱਥੇ ਕਲਿੱਕ ਕਰੋ।", "Click here to select a py file.": "", "Click here to upload a workflow.json file.": "", "click here.": "ਇੱਥੇ ਕਲਿੱਕ ਕਰੋ।", "Click on the user role button to change a user's role.": "ਉਪਭੋਗਤਾ ਦੀ ਭੂਮਿਕਾ ਬਦਲਣ ਲਈ ਉਪਭੋਗਤਾ ਭੂਮਿਕਾ ਬਟਨ 'ਤੇ ਕਲਿੱਕ ਕਰੋ।", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "", "Clone": "ਕਲੋਨ", "Clone Chat": "", "Clone of {{TITLE}}": "", "Close": "ਬੰਦ ਕਰੋ", "Close Banner": "", "Close Configure Connection Modal": "", "Close modal": "", "Close settings modal": "", "Close Sidebar": "", "CMU ARCTIC speaker embedding name": "", "Code Block": "", "Code execution": "", "Code Execution": "", "Code Execution Engine": "", "Code Execution Timeout": "", "Code formatted successfully": "", "Code Interpreter": "", "Code Interpreter Engine": "", "Code Interpreter Prompt Template": "", "Collapse": "", "Collection": "ਸੰਗ੍ਰਹਿ", "Color": "", "ComfyUI": "ਕੰਫੀਯੂਆਈ", "ComfyUI API Key": "", "ComfyUI Base URL": "ਕੰਫੀਯੂਆਈ ਬੇਸ URL", "ComfyUI Base URL is required.": "ਕੰਫੀਯੂਆਈ ਬੇਸ URL ਦੀ ਲੋੜ ਹੈ।", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Comma separated Node Ids (e.g. 1 or 1,2)": "", "Command": "ਕਮਾਂਡ", "Comment": "", "Completions": "", "Compress Images in Channels": "", "Concurrent Requests": "ਸਮਕਾਲੀ ਬੇਨਤੀਆਂ", "Config imported successfully": "", "Configure": "", "Confirm": "", "Confirm Password": "ਪਾਸਵਰਡ ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ", "Confirm your action": "", "Confirm your new password": "", "Confirm Your Password": "", "Connect to your own OpenAI compatible API endpoints.": "", "Connect to your own OpenAPI compatible external tool servers.": "", "Connection failed": "", "Connection successful": "", "Connection Type": "", "Connections": "ਕਨੈਕਸ਼ਨ", "Connections saved successfully": "", "Connections settings updated": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "", "Contact Admin for WebUI Access": "", "Content": "ਸਮੱਗਰੀ", "Content Extraction Engine": "", "Continue Response": "ਜਵਾਬ ਜਾਰੀ ਰੱਖੋ", "Continue with {{provider}}": "", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "", "Controls": "", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "", "Conversation saved successfully": "", "Copied": "", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "ਸਾਂਝੇ ਕੀਤੇ ਗੱਲਬਾਤ URL ਨੂੰ ਕਲਿੱਪਬੋਰਡ 'ਤੇ ਕਾਪੀ ਕਰ ਦਿੱਤਾ!", "Copied to clipboard": "", "Copy": "ਕਾਪੀ ਕਰੋ", "Copy Formatted Text": "", "Copy last code block": "ਆਖਰੀ ਕੋਡ ਬਲਾਕ ਨੂੰ ਕਾਪੀ ਕਰੋ", "Copy last response": "ਆਖਰੀ ਜਵਾਬ ਨੂੰ ਕਾਪੀ ਕਰੋ", "Copy link": "", "Copy Link": "ਲਿੰਕ ਕਾਪੀ ਕਰੋ", "Copy to clipboard": "", "Copying to clipboard was successful!": "ਕਲਿੱਪਬੋਰਡ 'ਤੇ ਕਾਪੀ ਕਰਨਾ ਸਫਲ ਰਿਹਾ!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "", "Create": "", "Create a knowledge base": "", "Create a model": "ਇੱਕ ਮਾਡਲ ਬਣਾਓ", "Create Account": "ਖਾਤਾ ਬਣਾਓ", "Create Admin Account": "", "Create Channel": "", "Create Folder": "", "Create Group": "", "Create Knowledge": "", "Create new key": "ਨਵੀਂ ਕੁੰਜੀ ਬਣਾਓ", "Create new secret key": "ਨਵੀਂ ਗੁਪਤ ਕੁੰਜੀ ਬਣਾਓ", "Create Note": "", "Create your first note by clicking on the plus button below.": "", "Created at": "ਤੇ ਬਣਾਇਆ ਗਿਆ", "Created At": "ਤੇ ਬਣਾਇਆ ਗਿਆ", "Created by": "", "CSV Import": "", "Ctrl+Enter to Send": "", "Current Model": "ਮੌਜੂਦਾ ਮਾਡਲ", "Current Password": "ਮੌਜੂਦਾ ਪਾਸਵਰਡ", "Custom": "ਕਸਟਮ", "Custom description enabled": "", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "", "Dark": "ਗੂੜ੍ਹਾ", "Database": "ਡਾਟਾਬੇਸ", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "DD/MM/YYYY": "", "December": "ਦਸੰਬਰ", "Deepgram": "", "Default": "ਮੂਲ", "Default (Open AI)": "", "Default (SentenceTransformers)": "ਮੂਲ (ਸੈਂਟੈਂਸਟ੍ਰਾਂਸਫਾਰਮਰਸ)", "Default action buttons will be used.": "", "Default description enabled": "", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "", "Default Model": "ਡਿਫਾਲਟ ਮਾਡਲ", "Default model updated": "ਮੂਲ ਮਾਡਲ ਅੱਪਡੇਟ ਕੀਤਾ ਗਿਆ", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "ਮੂਲ ਪ੍ਰੰਪਟ ਸੁਝਾਅ", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "", "Default User Role": "ਮੂਲ ਉਪਭੋਗਤਾ ਭੂਮਿਕਾ", "Delete": "ਮਿਟਾਓ", "Delete a model": "ਇੱਕ ਮਾਡਲ ਮਿਟਾਓ", "Delete All Chats": "ਸਾਰੀਆਂ ਚੈਟਾਂ ਨੂੰ ਮਿਟਾਓ", "Delete All Models": "", "Delete chat": "ਗੱਲਬਾਤ ਮਿਟਾਓ", "Delete Chat": "ਗੱਲਬਾਤ ਮਿਟਾਓ", "Delete chat?": "", "Delete folder?": "", "Delete function?": "", "Delete Message": "", "Delete message?": "", "Delete note?": "", "Delete prompt?": "", "delete this link": "ਇਸ ਲਿੰਕ ਨੂੰ ਮਿਟਾਓ", "Delete tool?": "", "Delete User": "ਉਪਭੋਗਤਾ ਮਿਟਾਓ", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} ਮਿਟਾਇਆ ਗਿਆ", "Deleted {{name}}": "ਮਿਟਾ ਦਿੱਤਾ ਗਿਆ {{name}}", "Deleted User": "", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "", "Description": "ਵਰਣਨਾ", "Detect Artifacts Automatically": "", "Dictate": "", "Didn't fully follow instructions": "ਹਦਾਇਤਾਂ ਨੂੰ ਪੂਰੀ ਤਰ੍ਹਾਂ ਫਾਲੋ ਨਹੀਂ ਕੀਤਾ", "Direct": "", "Direct Connections": "", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "", "Direct Tool Servers": "", "Directory selection was cancelled": "", "Disable Code Interpreter": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "ਬੰਦ", "Discover a function": "", "Discover a model": "ਇੱਕ ਮਾਡਲ ਲੱਭੋ", "Discover a prompt": "ਇੱਕ ਪ੍ਰੰਪਟ ਖੋਜੋ", "Discover a tool": "", "Discover how to use Open WebUI and seek support from the community.": "", "Discover wonders": "", "Discover, download, and explore custom functions": "", "Discover, download, and explore custom prompts": "ਕਸਟਮ ਪ੍ਰੰਪਟਾਂ ਨੂੰ ਖੋਜੋ, ਡਾਊਨਲੋਡ ਕਰੋ ਅਤੇ ਪੜਚੋਲ ਕਰੋ", "Discover, download, and explore custom tools": "", "Discover, download, and explore model presets": "ਮਾਡਲ ਪ੍ਰੀਸੈਟਾਂ ਨੂੰ ਖੋਜੋ, ਡਾਊਨਲੋਡ ਕਰੋ ਅਤੇ ਪੜਚੋਲ ਕਰੋ", "Display": "", "Display Emoji in Call": "", "Display Multi-model Responses in Tabs": "", "Display the username instead of You in the Chat": "ਗੱਲਬਾਤ 'ਚ ਤੁਹਾਡੇ ਸਥਾਨ 'ਤੇ ਉਪਭੋਗਤਾ ਨਾਮ ਦਿਖਾਓ", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "", "Do not install tools from sources you do not fully trust.": "", "Docling": "", "Docling Server URL required.": "", "Document": "ਡਾਕੂਮੈਂਟ", "Document Intelligence": "", "Document Intelligence endpoint required.": "", "Documentation": "", "Documents": "ਡਾਕੂਮੈਂਟ", "does not make any external connections, and your data stays securely on your locally hosted server.": "ਕੋਈ ਬਾਹਰੀ ਕਨੈਕਸ਼ਨ ਨਹੀਂ ਬਣਾਉਂਦਾ, ਅਤੇ ਤੁਹਾਡਾ ਡਾਟਾ ਤੁਹਾਡੇ ਸਥਾਨਕ ਸਰਵਰ 'ਤੇ ਸੁਰੱਖਿਅਤ ਰਹਿੰਦਾ ਹੈ।", "Domain Filter List": "", "don't fetch random pipelines from sources you don't trust.": "ਅਣਵਿਸ਼ਵਾਸਯੋਗ ਸਰੋਤਾਂ ਤੋਂ ਰੈਂਡਮ ਪਾਈਪਲਾਈਨ ਨਾ ਲਓ।", "Don't have an account?": "ਖਾਤਾ ਨਹੀਂ ਹੈ?", "don't install random functions from sources you don't trust.": "", "don't install random tools from sources you don't trust.": "", "Don't like the style": "ਸਟਾਈਲ ਪਸੰਦ ਨਹੀਂ ਹੈ", "Done": "", "Download": "ਡਾਊਨਲੋਡ", "Download & Delete": "ਡਾਊਨਲੋਡ ਅਤੇ ਮਿਟਾਓ", "Download as SVG": "", "Download canceled": "ਡਾਊਨਲੋਡ ਰੱਦ ਕੀਤਾ ਗਿਆ", "Download Database": "ਡਾਟਾਬੇਸ ਡਾਊਨਲੋਡ ਕਰੋ", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "ਉਦਾਹਰਣ ਲਈ '30ਸ','10ਮਿ'. ਸਹੀ ਸਮਾਂ ਇਕਾਈਆਂ ਹਨ 'ਸ', 'ਮ', 'ਘੰ'.", "e.g. \"json\" or a JSON schema": "", "e.g. 60": "", "e.g. A filter to remove profanity from text": "", "e.g. en": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "e.g., westus (leave blank for eastus)": "", "Edit": "ਸੰਪਾਦਨ ਕਰੋ", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Folder": "", "Edit Memory": "", "Edit User": "ਉਪਭੋਗਤਾ ਸੰਪਾਦਨ ਕਰੋ", "Edit User Group": "", "Edited": "", "Editing": "", "Eject": "", "ElevenLabs": "", "Email": "ਈਮੇਲ", "Embark on adventures": "", "Embedding": "", "Embedding Batch Size": "", "Embedding Model": "ਐਮਬੈੱਡਿੰਗ ਮਾਡਲ", "Embedding Model Engine": "ਐਮਬੈੱਡਿੰਗ ਮਾਡਲ ਇੰਜਣ", "Embedding model set to \"{{embedding_model}}\"": "ਐਮਬੈੱਡਿੰਗ ਮਾਡਲ ਨੂੰ \"{{embedding_model}}\" 'ਤੇ ਸੈੱਟ ਕੀਤਾ ਗਿਆ", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Code Execution": "", "Enable Code Interpreter": "", "Enable Community Sharing": "ਕਮਿਊਨਿਟੀ ਸ਼ੇਅਰਿੰਗ ਨੂੰ ਸਮਰੱਥ ਕਰੋ", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity.": "", "Enable New Sign Ups": "ਨਵੇਂ ਸਾਈਨ ਅਪ ਯੋਗ ਕਰੋ", "Enable, disable, or customize the reasoning tags used by the model. \"Enabled\" uses default tags, \"Disabled\" turns off reasoning tags, and \"Custom\" lets you specify your own start and end tags.": "", "Enabled": "ਚਾਲੂ", "End Tag": "", "Endpoint URL": "", "Enforce Temporary Chat": "", "Enhance": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "ਸੁਨਿਸ਼ਚਿਤ ਕਰੋ ਕਿ ਤੁਹਾਡੀ CSV ਫਾਈਲ ਵਿੱਚ ਇਸ ਕ੍ਰਮ ਵਿੱਚ 4 ਕਾਲਮ ਹਨ: ਨਾਮ, ਈਮੇਲ, ਪਾਸਵਰਡ, ਭੂਮਿਕਾ।", "Enter {{role}} message here": "{{role}} ਸੁਨੇਹਾ ਇੱਥੇ ਦਰਜ ਕਰੋ", "Enter a detail about yourself for your LLMs to recall": "ਤੁਹਾਡੇ LLMs ਨੂੰ ਸੁਨੇਹਾ ਕਰਨ ਲਈ ਸੁਨੇਹਾ ਇੱਥੇ ਦਰਜ ਕਰੋ", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Bocha Search API Key": "", "Enter Brave Search API Key": "ਬਹਾਦਰ ਖੋਜ API ਕੁੰਜੀ ਦਾਖਲ ਕਰੋ", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "ਚੰਕ ਓਵਰਲੈਪ ਦਰਜ ਕਰੋ", "Enter Chunk Size": "ਚੰਕ ਆਕਾਰ ਦਰਜ ਕਰੋ", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter coordinates (e.g. 51.505, -0.09)": "", "Enter Datalab Marker API Base URL": "", "Enter Datalab Marker API Key": "", "Enter description": "", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "", "Enter Document Intelligence Endpoint": "", "Enter Document Intelligence Key": "", "Enter domains separated by commas (e.g., example.com,site.org)": "", "Enter Exa API Key": "", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter folder name": "", "Enter Github Raw URL": "Github ਕੱਚਾ URL ਦਾਖਲ ਕਰੋ", "Enter Google PSE API Key": "Google PSE API ਕੁੰਜੀ ਦਾਖਲ ਕਰੋ", "Enter Google PSE Engine Id": "Google PSE ਇੰਜਣ ID ਦਾਖਲ ਕਰੋ", "Enter hex color (e.g. #FF0000)": "", "Enter ID": "", "Enter Image Size (e.g. 512x512)": "ਚਿੱਤਰ ਆਕਾਰ ਦਰਜ ਕਰੋ (ਉਦਾਹਰਣ ਲਈ 512x512)", "Enter Jina API Key": "", "Enter JSON config (e.g., {\"disable_links\": true})": "", "Enter Jupyter Password": "", "Enter Jupyter Token": "", "Enter Jupyter URL": "", "Enter Kagi Search API Key": "", "Enter Key Behavior": "", "Enter language codes": "ਭਾਸ਼ਾ ਕੋਡ ਦਰਜ ਕਰੋ", "Enter Mistral API Key": "", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "ਮਾਡਲ ਟੈਗ ਦਰਜ ਕਰੋ (ਉਦਾਹਰਣ ਲਈ {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "ਕਦਮਾਂ ਦੀ ਗਿਣਤੀ ਦਰਜ ਕਰੋ (ਉਦਾਹਰਣ ਲਈ 50)", "Enter Perplexity API Key": "", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "", "Enter reasoning effort": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "ਸਕੋਰ ਦਰਜ ਕਰੋ", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "Searxng Query URL ਦਾਖਲ ਕਰੋ", "Enter Seed": "", "Enter SerpApi API Key": "", "Enter SerpApi Engine": "", "Enter Serper API Key": "Serper API ਕੁੰਜੀ ਦਾਖਲ ਕਰੋ", "Enter Serply API Key": "", "Enter Serpstack API Key": "Serpstack API ਕੁੰਜੀ ਦਾਖਲ ਕਰੋ", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter Sougou Search API sID": "", "Enter Sougou Search API SK": "", "Enter stop sequence": "ਰੋਕਣ ਦਾ ਕ੍ਰਮ ਦਰਜ ਕਰੋ", "Enter system prompt": "", "Enter system prompt here": "", "Enter Tavily API Key": "", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "", "Enter timeout in seconds": "", "Enter to Send": "", "Enter Top K": "ਸਿਖਰ K ਦਰਜ ਕਰੋ", "Enter Top K Reranker": "", "Enter URL (e.g. http://127.0.0.1:7860/)": "URL ਦਰਜ ਕਰੋ (ਉਦਾਹਰਣ ਲਈ http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "URL ਦਰਜ ਕਰੋ (ਉਦਾਹਰਣ ਲਈ http://localhost:11434)", "Enter value": "", "Enter value (true/false)": "", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your code here...": "ਆਪਣਾ ਕੋਡ ਇੱਥੇ ਦਰਜ ਕਰੋ...", "Enter your current password": "", "Enter Your Email": "ਆਪਣੀ ਈਮੇਲ ਦਰਜ ਕਰੋ", "Enter Your Full Name": "ਆਪਣਾ ਪੂਰਾ ਨਾਮ ਦਰਜ ਕਰੋ", "Enter your gender": "", "Enter your message": "", "Enter your name": "", "Enter Your Name": "", "Enter your new password": "", "Enter Your Password": "ਆਪਣਾ ਪਾਸਵਰਡ ਦਰਜ ਕਰੋ", "Enter Your Role": "ਆਪਣੀ ਭੂਮਿਕਾ ਦਰਜ ਕਰੋ", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "ਗਲਤੀ", "ERROR": "", "Error accessing directory": "", "Error accessing Google Drive: {{error}}": "", "Error accessing media devices.": "", "Error starting recording.": "", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "", "Error: A model with the ID '{{modelId}}' already exists. Please select a different ID to proceed.": "", "Error: Model ID cannot be empty. Please enter a valid ID to proceed.": "", "Evaluations": "", "Everyone": "", "Exa API Key": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: mail": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "", "Exclude": "", "Execute code for analysis": "", "Executing **{{NAME}}**...": "", "Expand": "", "Experimental": "ਪਰਮਾਣੂਕ੍ਰਿਤ", "Explain": "", "Explore the cosmos": "", "Export": "ਨਿਰਯਾਤ", "Export All Archived Chats": "", "Export All Chats (All Users)": "ਸਾਰੀਆਂ ਗੱਲਾਂ ਨਿਰਯਾਤ ਕਰੋ (ਸਾਰੇ ਉਪਭੋਗਤਾ)", "Export chat (.json)": "", "Export Chats": "ਗੱਲਾਂ ਨਿਰਯਾਤ ਕਰੋ", "Export Config to JSON File": "", "Export Functions": "", "Export Models": "ਨਿਰਯਾਤ ਮਾਡਲ", "Export Presets": "", "Export Prompt Suggestions": "", "Export Prompts": "ਪ੍ਰੰਪਟ ਨਿਰਯਾਤ ਕਰੋ", "Export to CSV": "", "Export Tools": "", "Export Users": "", "External": "", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "", "External Web Loader URL": "", "External Web Search API Key": "", "External Web Search URL": "", "Fade Effect for Streaming Text": "", "Failed to add file.": "", "Failed to connect to {{URL}} OpenAPI tool server": "", "Failed to copy link": "", "Failed to create API Key.": "API ਕੁੰਜੀ ਬਣਾਉਣ ਵਿੱਚ ਅਸਫਲ।", "Failed to delete note": "", "Failed to extract content from the file: {{error}}": "", "Failed to extract content from the file.": "", "Failed to fetch models": "", "Failed to generate title": "", "Failed to load chat preview": "", "Failed to load file content.": "", "Failed to move chat": "", "Failed to read clipboard contents": "ਕਲਿੱਪਬੋਰਡ ਸਮੱਗਰੀ ਪੜ੍ਹਣ ਵਿੱਚ ਅਸਫਲ", "Failed to save connections": "", "Failed to save conversation": "ਗੱਲਬਾਤ ਸੰਭਾਲਣ ਵਿੱਚ ਅਸਫਲ", "Failed to save models configuration": "", "Failed to update settings": "", "Failed to upload file.": "", "Features": "", "Features Permissions": "", "February": "ਫਰਵਰੀ", "Feedback Details": "", "Feedback History": "", "Feedbacks": "", "Feel free to add specific details": "ਖੁੱਲ੍ਹੇ ਦਿਲ ਨਾਲ ਖਾਸ ਵੇਰਵੇ ਸ਼ਾਮਲ ਕਰੋ", "Female": "", "File": "", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "ਫਾਈਲ ਮੋਡ", "File not found.": "ਫਾਈਲ ਨਹੀਂ ਮਿਲੀ।", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File Upload": "", "File uploaded successfully": "", "Files": "", "Filter": "", "Filter is now globally disabled": "", "Filter is now globally enabled": "", "Filters": "", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "ਫਿੰਗਰਪ੍ਰਿੰਟ ਸਪੂਫਿੰਗ ਪਾਈ ਗਈ: ਅਵਤਾਰ ਵਜੋਂ ਸ਼ੁਰੂਆਤੀ ਅੱਖਰ ਵਰਤਣ ਵਿੱਚ ਅਸਮਰੱਥ। ਮੂਲ ਪ੍ਰੋਫਾਈਲ ਚਿੱਤਰ 'ਤੇ ਡਿਫਾਲਟ।", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Floating Quick Actions": "", "Focus chat input": "ਗੱਲਬਾਤ ਇਨਪੁਟ 'ਤੇ ਧਿਆਨ ਦਿਓ", "Folder deleted successfully": "", "Folder Name": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Folder updated successfully": "", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "ਹਦਾਇਤਾਂ ਨੂੰ ਬਿਲਕੁਲ ਫਾਲੋ ਕੀਤਾ", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "", "Form": "", "Format Lines": "", "Format the lines in the output. Defaults to False. If set to True, the lines will be formatted to detect inline math and styles.": "", "Format your variables using brackets like this:": "", "Formatting may be inconsistent from source.": "", "Forwards system user session credentials to authenticate": "", "Full Context Mode": "", "Function": "", "Function Calling": "", "Function created successfully": "", "Function deleted successfully": "", "Function Description": "", "Function ID": "", "Function imported successfully": "", "Function is now globally disabled": "", "Function is now globally enabled": "", "Function Name": "", "Function updated successfully": "", "Functions": "", "Functions allow arbitrary code execution.": "", "Functions imported successfully": "", "Gemini": "", "Gemini API Config": "", "Gemini API Key is required.": "", "Gender": "", "General": "ਆਮ", "Generate": "", "Generate an image": "", "Generate Image": "", "Generate prompt pair": "", "Generating search query": "ਖੋਜ ਪੁੱਛਗਿੱਛ ਤਿਆਰ ਕਰਨਾ", "Generating...": "", "Get information on {{name}} in the UI": "", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "", "Good Response": "ਵਧੀਆ ਜਵਾਬ", "Google Drive": "", "Google PSE API Key": "Google PSE API ਕੁੰਜੀ", "Google PSE Engine Id": "ਗੂਗਲ PSE ਇੰਜਣ ID", "Gravatar": "", "Group": "ਗਰੁੱਪ", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "", "Height": "", "Hello, {{name}}": "ਸਤ ਸ੍ਰੀ ਅਕਾਲ, {{name}}", "Help": "ਮਦਦ", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "ਲੁਕਾਓ", "Hide from Sidebar": "", "Hide Model": "", "High": "", "High Contrast Mode": "", "Home": "", "Host": "", "How can I help you today?": "ਮੈਂ ਅੱਜ ਤੁਹਾਡੀ ਕਿਵੇਂ ਮਦਦ ਕਰ ਸਕਦਾ ਹਾਂ?", "How would you rate this response?": "", "HTML": "", "Hybrid Search": "ਹਾਈਬ੍ਰਿਡ ਖੋਜ", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "", "ID": "", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "", "Image": "", "Image Compression": "", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "", "Image Generation (Experimental)": "ਚਿੱਤਰ ਜਨਰੇਸ਼ਨ (ਪਰਮਾਣੂਕ੍ਰਿਤ)", "Image Generation Engine": "ਚਿੱਤਰ ਜਨਰੇਸ਼ਨ ਇੰਜਣ", "Image Max Compression Size": "", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "", "Image Prompt Generation Prompt": "", "Image Settings": "ਚਿੱਤਰ ਸੈਟਿੰਗਾਂ", "Images": "ਚਿੱਤਰ", "Import": "", "Import Chats": "ਗੱਲਾਂ ਆਯਾਤ ਕਰੋ", "Import Config from JSON File": "", "Import From Link": "", "Import Functions": "", "Import Models": "ਮਾਡਲ ਆਯਾਤ ਕਰੋ", "Import Notes": "", "Import Presets": "", "Import Prompt Suggestions": "", "Import Prompts": "ਪ੍ਰੰਪਟ ਆਯਾਤ ਕਰੋ", "Import Tools": "", "Important Update": "ਮਹੱਤਵਪੂਰਨ ਅੱਪਡੇਟ", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "", "Include `--api` flag when running stable-diffusion-webui": "ਸਟੇਬਲ-ਡਿਫਿਊਸ਼ਨ-ਵੈਬਯੂਆਈ ਚਲਾਉਣ ਸਮੇਂ `--api` ਝੰਡਾ ਸ਼ਾਮਲ ਕਰੋ", "Includes SharePoint": "SharePoint ਸ਼ਾਮਲ ਹੈ", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "", "Info": "ਜਾਣਕਾਰੀ", "Initials": "", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "", "Input": "", "Input commands": "ਇਨਪੁਟ ਕਮਾਂਡਾਂ", "Input Key (e.g. text, unet_name, steps)": "", "Input Variables": "", "Insert": "", "Insert Follow-Up Prompt to Input": "", "Insert Prompt as Rich Text": "", "Install from Github URL": "Github URL ਤੋਂ ਇੰਸਟਾਲ ਕਰੋ", "Instant Auto-Send After Voice Transcription": "", "Integration": "", "Interface": "ਇੰਟਰਫੇਸ", "Invalid file content": "", "Invalid file format.": "", "Invalid JSON file": "", "Invalid JSON format for ComfyUI Workflow.": "", "Invalid JSON format in Additional Config": "", "Invalid Tag": "ਗਲਤ ਟੈਗ", "is typing...": "", "Italic": "", "January": "ਜਨਵਰੀ", "Jina API Key": "", "join our Discord for help.": "ਮਦਦ ਲਈ ਸਾਡੇ ਡਿਸਕੋਰਡ ਵਿੱਚ ਸ਼ਾਮਲ ਹੋਵੋ।", "JSON": "JSON", "JSON Preview": "JSON ਪੂਰਵ-ਦਰਸ਼ਨ", "July": "ਜੁਲਾਈ", "June": "ਜੂਨ", "Jupyter Auth": "", "Jupyter URL": "", "JWT Expiration": "JWT ਮਿਆਦ ਖਤਮ", "JWT Token": "JWT ਟੋਕਨ", "Kagi Search API Key": "", "Keep Follow-Up Prompts in Chat": "", "Keep in Sidebar": "", "Key": "", "Key is required": "", "Keyboard shortcuts": "ਕੀਬੋਰਡ ਸ਼ਾਰਟਕਟ", "Knowledge": "", "Knowledge Access": "", "Knowledge Base": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge Description": "", "Knowledge Name": "", "Knowledge Public Sharing": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Kokoro.js (Browser)": "", "Kokoro.js Dtype": "", "Label": "", "Landing Page Mode": "", "Language": "ਭਾਸ਼ਾ", "Language Locales": "", "Last Active": "ਆਖਰੀ ਸਰਗਰਮ", "Last Modified": "", "Last reply": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Learn More": "", "Learn more about OpenAPI tool servers.": "", "Leave empty for no compression": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{url}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Leave model field empty to use the default model.": "", "lexical": "", "License": "", "Lift List": "", "Light": "ਹਲਕਾ", "Listening...": "", "Llama.cpp": "", "LLMs can make mistakes. Verify important information.": "LLMs ਗਲਤੀਆਂ ਕਰ ਸਕਦੇ ਹਨ। ਮਹੱਤਵਪੂਰਨ ਜਾਣਕਾਰੀ ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ।", "Loader": "", "Loading Kokoro.js...": "", "Loading...": "ਲੋਡ ਕੀਤਾ ਜਾ ਰਿਹਾ ਹੈ...", "Local": "", "Local Task Model": "", "Location access not allowed": "", "Lost": "", "Low": "", "LTR": "LTR", "Made by Open WebUI Community": "ਓਪਨਵੈਬਯੂਆਈ ਕਮਿਊਨਿਟੀ ਦੁਆਰਾ ਬਣਾਇਆ ਗਿਆ", "Make password visible in the user interface": "", "Make sure to enclose them with": "ਸੁਨਿਸ਼ਚਿਤ ਕਰੋ ਕਿ ਉਨ੍ਹਾਂ ਨੂੰ ਘੇਰੋ", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Male": "", "Manage": "", "Manage Direct Connections": "", "Manage Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "ਪਾਈਪਲਾਈਨਾਂ ਦਾ ਪ੍ਰਬੰਧਨ ਕਰੋ", "Manage Tool Servers": "", "Manage your account information.": "", "March": "ਮਾਰਚ", "Markdown": "", "Markdown (Header)": "", "Max Speakers": "", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "ਇੱਕ ਸਮੇਂ ਵਿੱਚ ਵੱਧ ਤੋਂ ਵੱਧ 3 ਮਾਡਲ ਡਾਊਨਲੋਡ ਕੀਤੇ ਜਾ ਸਕਦੇ ਹਨ। ਕਿਰਪਾ ਕਰਕੇ ਬਾਅਦ ਵਿੱਚ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "May": "ਮਈ", "Medium": "", "Memories accessible by LLMs will be shown here.": "LLMs ਲਈ ਸਮਰੱਥ ਕਾਰਨ ਇੱਕ ਸੂਚਨਾ ਨੂੰ ਸ਼ਾਮਲ ਕੀਤਾ ਗਿਆ ਹੈ।", "Memory": "ਮੀਮਰ", "Memory added successfully": "", "Memory cleared successfully": "", "Memory deleted successfully": "", "Memory updated successfully": "", "Merge Responses": "", "Merged Response": "ਮਿਲਾਇਆ ਗਿਆ ਜਵਾਬ", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "ਤੁਹਾਡਾ ਲਿੰਕ ਬਣਾਉਣ ਤੋਂ ਬਾਅਦ ਤੁਹਾਡੇ ਵੱਲੋਂ ਭੇਜੇ ਗਏ ਸੁਨੇਹੇ ਸਾਂਝੇ ਨਹੀਂ ਕੀਤੇ ਜਾਣਗੇ। URL ਵਾਲੇ ਉਪਭੋਗਤਾ ਸਾਂਝੀ ਚੈਟ ਨੂੰ ਵੇਖ ਸਕਣਗੇ।", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "ਮਾਡਲ '{{modelName}}' ਸਫਲਤਾਪੂਰਵਕ ਡਾਊਨਲੋਡ ਕੀਤਾ ਗਿਆ ਹੈ।", "Model '{{modelTag}}' is already in queue for downloading.": "ਮਾਡਲ '{{modelTag}}' ਪਹਿਲਾਂ ਹੀ ਡਾਊਨਲੋਡ ਲਈ ਕਤਾਰ ਵਿੱਚ ਹੈ।", "Model {{modelId}} not found": "ਮਾਡਲ {{modelId}} ਨਹੀਂ ਮਿਲਿਆ", "Model {{modelName}} is not vision capable": "ਮਾਡਲ {{modelName}} ਦ੍ਰਿਸ਼ਟੀ ਸਮਰੱਥ ਨਹੀਂ ਹੈ", "Model {{name}} is now {{status}}": "ਮਾਡਲ {{name}} ਹੁਣ {{status}} ਹੈ", "Model {{name}} is now hidden": "", "Model {{name}} is now visible": "", "Model accepts file inputs": "", "Model accepts image inputs": "", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "ਮਾਡਲ ਫਾਈਲਸਿਸਟਮ ਪੱਥ ਪਾਇਆ ਗਿਆ। ਅੱਪਡੇਟ ਲਈ ਮਾਡਲ ਸ਼ੌਰਟਨੇਮ ਦੀ ਲੋੜ ਹੈ, ਜਾਰੀ ਨਹੀਂ ਰੱਖ ਸਕਦੇ।", "Model Filtering": "", "Model ID": "ਮਾਡਲ ID", "Model ID is required.": "", "Model IDs": "", "Model Name": "", "Model name already exists, please choose a different one": "", "Model Name is required.": "", "Model not selected": "ਮਾਡਲ ਚੁਣਿਆ ਨਹੀਂ ਗਿਆ", "Model Params": "ਮਾਡਲ ਪਰਮਜ਼", "Model Permissions": "", "Model unloaded successfully": "", "Model updated successfully": "", "Model(s) do not support file upload": "", "Modelfile Content": "ਮਾਡਲਫਾਈਲ ਸਮੱਗਰੀ", "Models": "ਮਾਡਲ", "Models Access": "", "Models configuration saved successfully": "", "Models Public Sharing": "", "Mojeek Search API Key": "", "more": "", "More": "ਹੋਰ", "More Concise": "", "More Options": "", "Move": "", "Name": "ਨਾਮ", "Name and ID are required, please fill them out": "", "Name your knowledge base": "", "Native": "", "New Button": "", "New Chat": "ਨਵੀਂ ਗੱਲਬਾਤ", "New Folder": "", "New Function": "", "New Note": "", "New Password": "ਨਵਾਂ ਪਾਸਵਰਡ", "New Tool": "", "new-channel": "", "Next message": "", "No chats found": "", "No chats found for this user.": "", "No chats found.": "", "No content": "", "No content found": "", "No content found in file.": "", "No content to speak": "", "No conversation to save": "", "No distance available": "", "No feedbacks found": "", "No file selected": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No inference engine with management support found": "", "No knowledge found": "", "No memories to clear": "", "No model IDs": "", "No models found": "", "No models selected": "", "No Notes": "", "No results": "ਕੋਈ ਨਤੀਜੇ ਨਹੀਂ ਮਿਲੇ", "No results found": "ਕੋਈ ਨਤੀਜੇ ਨਹੀਂ ਮਿਲੇ", "No search query generated": "ਕੋਈ ਖੋਜ ਪੁੱਛਗਿੱਛ ਤਿਆਰ ਨਹੀਂ ਕੀਤੀ ਗਈ", "No source available": "ਕੋਈ ਸਰੋਤ ਉਪਲਬਧ ਨਹੀਂ", "No suggestion prompts": "ਕੋਈ ਸੁਝਾਏ ਪ੍ਰਾਂਪਟ ਨਹੀਂ", "No users were found.": "", "No valves": "", "No valves to update": "", "Node Ids": "", "None": "ਕੋਈ ਨਹੀਂ", "Not factually correct": "ਤੱਥਕ ਰੂਪ ਵਿੱਚ ਸਹੀ ਨਹੀਂ", "Not helpful": "", "Note deleted successfully": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "ਨੋਟ: ਜੇ ਤੁਸੀਂ ਘੱਟੋ-ਘੱਟ ਸਕੋਰ ਸੈੱਟ ਕਰਦੇ ਹੋ, ਤਾਂ ਖੋਜ ਸਿਰਫ਼ ਉਹੀ ਡਾਕੂਮੈਂਟ ਵਾਪਸ ਕਰੇਗੀ ਜਿਨ੍ਹਾਂ ਦਾ ਸਕੋਰ ਘੱਟੋ-ਘੱਟ ਸਕੋਰ ਦੇ ਬਰਾਬਰ ਜਾਂ ਵੱਧ ਹੋਵੇ।", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "ਸੂਚਨਾਵਾਂ", "November": "ਨਵੰਬਰ", "OAuth ID": "", "October": "ਅਕਤੂਬਰ", "Off": "ਬੰਦ", "Okay, Let's Go!": "ਠੀਕ ਹੈ, ਚੱਲੋ ਚੱਲੀਏ!", "OLED Dark": "OLED ਗੂੜ੍ਹਾ", "Ollama": "ਓਲਾਮਾ", "Ollama API": "Ollama API", "Ollama API settings updated": "", "Ollama Version": "ਓਲਾਮਾ ਵਰਜਨ", "On": "ਚਾਲੂ", "OneDrive": "", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "ਕਮਾਂਡ ਸਤਰ ਵਿੱਚ ਸਿਰਫ਼ ਅਲਫ਼ਾਨਯੂਮੈਰਿਕ ਅੱਖਰ ਅਤੇ ਹਾਈਫਨ ਦੀ ਆਗਿਆ ਹੈ।", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only markdown files are allowed": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "ਓਹੋ! ਲੱਗਦਾ ਹੈ ਕਿ URL ਗਲਤ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਦੁਬਾਰਾ ਜਾਂਚ ਕਰੋ ਅਤੇ ਮੁੜ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "ਓਹੋ! ਤੁਸੀਂ ਇੱਕ ਅਣਸਮਰਥਿਤ ਢੰਗ ਵਰਤ ਰਹੇ ਹੋ (ਸਿਰਫ਼ ਫਰੰਟਐਂਡ)। ਕਿਰਪਾ ਕਰਕੇ ਵੈਬਯੂਆਈ ਨੂੰ ਬੈਕਐਂਡ ਤੋਂ ਸਰਵ ਕਰੋ।", "Open file": "", "Open in full screen": "", "Open modal to configure connection": "", "Open Modal To Manage Floating Quick Actions": "", "Open new chat": "ਨਵੀਂ ਗੱਲਬਾਤ ਖੋਲ੍ਹੋ", "Open Sidebar": "", "Open User Profile Menu": "", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "", "OpenAI": "ਓਪਨਏਆਈ", "OpenAI API": "ਓਪਨਏਆਈ API", "OpenAI API Config": "ਓਪਨਏਆਈ API ਕਨਫਿਗ", "OpenAI API Key is required.": "ਓਪਨਏਆਈ API ਕੁੰਜੀ ਦੀ ਲੋੜ ਹੈ।", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "ਓਪਨਏਆਈ URL/ਕੁੰਜੀ ਦੀ ਲੋੜ ਹੈ।", "openapi.json URL or Path": "", "Optional": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "ਜਾਂ", "Ordered List": "", "Organize your users": "", "Other": "ਹੋਰ", "OUTPUT": "", "Output format": "", "Output Format": "", "Overview": "", "page": "", "Paginate": "", "Parameters": "", "Password": "ਪਾਸਵਰਡ", "Passwords do not match.": "", "Paste Large Text as File": "", "PDF document (.pdf)": "PDF ਡਾਕੂਮੈਂਟ (.pdf)", "PDF Extract Images (OCR)": "PDF ਚਿੱਤਰ ਕੱਢੋ (OCR)", "pending": "ਬਕਾਇਆ", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "", "Permission denied when accessing microphone": "", "Permission denied when accessing microphone: {{error}}": "ਮਾਈਕ੍ਰੋਫ਼ੋਨ ਤੱਕ ਪਹੁੰਚਣ ਸਮੇਂ ਆਗਿਆ ਰੱਦ ਕੀਤੀ ਗਈ: {{error}}", "Permissions": "", "Perplexity API Key": "", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "ਪਰਸੋਨਲਿਸ਼ਮ", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "", "Pinned": "", "Pioneer insights": "", "Pipe": "", "Pipeline deleted successfully": "", "Pipeline downloaded successfully": "", "Pipelines": "ਪਾਈਪਲਾਈਨਾਂ", "Pipelines are a plugin system with arbitrary code execution —": "Pipelines ਇੱਕ ਪਲੱਗਇਨ ਸਿਸਟਮ ਹੈ ਜਿਸ ਵਿੱਚ ਮਨਚਾਹਾ ਕੋਡ ਚੱਲ ਸਕਦਾ ਹੈ —", "Pipelines Not Detected": "", "Pipelines Valves": "ਪਾਈਪਲਾਈਨਾਂ ਵਾਲਵ", "Plain text (.md)": "", "Plain text (.txt)": "ਸਧਾਰਨ ਪਾਠ (.txt)", "Playground": "ਖੇਡ ਦਾ ਮੈਦਾਨ", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "", "Please do not close the settings page while loading the model.": "", "Please enter a message or attach a file.": "", "Please enter a prompt": "", "Please enter a valid path": "", "Please enter a valid URL": "", "Please fill in all fields.": "", "Please select a model first.": "", "Please select a model.": "", "Please select a reason": "", "Please wait until all files are uploaded.": "", "Port": "", "Positive attitude": "ਸਕਾਰਾਤਮਕ ਰਵੱਈਆ", "Prefer not to say": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Prevent file creation": "", "Preview": "", "Previous 30 days": "ਪਿਛਲੇ 30 ਦਿਨ", "Previous 7 days": "ਪਿਛਲੇ 7 ਦਿਨ", "Previous message": "", "Private": "", "Profile": "ਪ੍ਰੋਫ਼ਾਈਲ", "Prompt": "", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "ਪ੍ਰੰਪਟ (ਉਦਾਹਰਣ ਲਈ ਮੈਨੂੰ ਰੋਮਨ ਸਾਮਰਾਜ ਬਾਰੇ ਇੱਕ ਮਜ਼ੇਦਾਰ ਤੱਥ ਦੱਸੋ)", "Prompt Autocompletion": "", "Prompt Content": "ਪ੍ਰੰਪਟ ਸਮੱਗਰੀ", "Prompt created successfully": "", "Prompt suggestions": "ਪ੍ਰੰਪਟ ਸੁਝਾਅ", "Prompt updated successfully": "", "Prompts": "ਪ੍ਰੰਪਟ", "Prompts Access": "", "Prompts Public Sharing": "", "Public": "", "Pull \"{{searchValue}}\" from Ollama.com": "ਓਲਾਮਾ.ਕਾਮ ਤੋਂ \"{{searchValue}}\" ਖਿੱਚੋ", "Pull a model from Ollama.com": "ਓਲਾਮਾ.ਕਾਮ ਤੋਂ ਇੱਕ ਮਾਡਲ ਖਿੱਚੋ", "Query Generation Prompt": "", "Quick Actions": "", "RAG Template": "RAG ਟੈਮਪਲੇਟ", "Rating": "", "Re-rank models by topic similarity": "", "Read": "", "Read Aloud": "ਜੋਰ ਨਾਲ ਪੜ੍ਹੋ", "Reason": "", "Reasoning Effort": "", "Reasoning Tags": "", "Record": "", "Record voice": "ਆਵਾਜ਼ ਰਿਕਾਰਡ ਕਰੋ", "Redirecting you to Open WebUI Community": "ਤੁਹਾਨੂੰ ਓਪਨਵੈਬਯੂਆਈ ਕਮਿਊਨਿਟੀ ਵੱਲ ਰੀਡਾਇਰੈਕਟ ਕੀਤਾ ਜਾ ਰਿਹਾ ਹੈ", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "", "References from": "", "Refused when it shouldn't have": "ਜਦੋਂ ਇਹ ਨਹੀਂ ਹੋਣਾ ਚਾਹੀਦਾ ਸੀ ਤਾਂ ਇਨਕਾਰ ਕੀਤਾ", "Regenerate": "ਮੁੜ ਬਣਾਓ", "Regenerate Menu": "", "Reindex": "", "Reindex Knowledge Base Vectors": "", "Release Notes": "ਰਿਲੀਜ਼ ਨੋਟਸ", "Releases": "", "Relevance": "", "Relevance Threshold": "", "Remember Dismissal": "", "Remove": "ਹਟਾਓ", "Remove {{MODELID}} from list.": "", "Remove file": "", "Remove File": "", "Remove image": "", "Remove Model": "ਮਾਡਲ ਹਟਾਓ", "Remove this tag from list": "", "Rename": "ਨਾਮ ਬਦਲੋ", "Reorder Models": "", "Reply in Thread": "", "Reranking Engine": "", "Reranking Model": "ਮਾਡਲ ਮੁੜ ਰੈਂਕਿੰਗ", "Reset": "", "Reset All Models": "", "Reset Image": "ਚਿੱਤਰ ਰੀਸੈਟ ਕਰੋ", "Reset Upload Directory": "", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "", "Response splitting": "", "Response Watermark": "", "Result": "", "RESULT": "ਨਤੀਜਾ", "Retrieval": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "ਭੂਮਿਕਾ", "Rosé Pine": "ਰੋਜ਼ ਪਾਈਨ", "Rosé Pine Dawn": "ਰੋਜ਼ ਪਾਈਨ ਡਾਨ", "RTL": "RTL", "Run": "", "Running": "", "Running...": "ਚੱਲ ਰਿਹਾ ਹੈ...", "Save": "ਸੰਭਾਲੋ", "Save & Create": "ਸੰਭਾਲੋ ਅਤੇ ਬਣਾਓ", "Save & Update": "ਸੰਭਾਲੋ ਅਤੇ ਅੱਪਡੇਟ ਕਰੋ", "Save As Copy": "", "Save Chat": "", "Save Tag": "", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "ਤੁਹਾਡੇ ਬ੍ਰਾਊਜ਼ਰ ਦੇ ਸਟੋਰੇਜ ਵਿੱਚ ਸਿੱਧੇ ਗੱਲਬਾਤ ਲੌਗ ਸੰਭਾਲਣਾ ਹੁਣ ਸਮਰਥਿਤ ਨਹੀਂ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਹੇਠਾਂ ਦਿੱਤੇ ਬਟਨ 'ਤੇ ਕਲਿੱਕ ਕਰਕੇ ਆਪਣੇ ਗੱਲਬਾਤ ਲੌਗ ਡਾਊਨਲੋਡ ਅਤੇ ਮਿਟਾਉਣ ਲਈ ਕੁਝ ਸਮਾਂ ਲਓ। ਚਿੰਤਾ ਨਾ ਕਰੋ, ਤੁਸੀਂ ਆਪਣੇ ਗੱਲਬਾਤ ਲੌਗ ਨੂੰ ਬੈਕਐਂਡ ਵਿੱਚ ਆਸਾਨੀ ਨਾਲ ਮੁੜ ਆਯਾਤ ਕਰ ਸਕਦੇ ਹੋ", "Scroll On Branch Change": "", "Search": "ਖੋਜ", "Search a model": "ਇੱਕ ਮਾਡਲ ਖੋਜੋ", "Search all emojis": "", "Search Base": "", "Search Chats": "ਖੋਜ ਚੈਟਾਂ", "Search Collection": "", "Search Filters": "", "search for archived chats": "", "search for folders": "", "search for pinned chats": "", "search for shared chats": "", "search for tags": "", "Search Functions": "", "Search In Models": "", "Search Knowledge": "", "Search Models": "ਖੋਜ ਮਾਡਲ", "Search Notes": "", "Search options": "", "Search Prompts": "ਪ੍ਰੰਪਟ ਖੋਜੋ", "Search Result Count": "ਖੋਜ ਨਤੀਜੇ ਦੀ ਗਿਣਤੀ", "Search the internet": "", "Search Tools": "", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searching the web...": "", "Searxng Query URL": "Searxng Query URL", "See readme.md for instructions": "ਹਦਾਇਤਾਂ ਲਈ readme.md ਵੇਖੋ", "See what's new": "ਨਵਾਂ ਕੀ ਹੈ ਵੇਖੋ", "Seed": "ਬੀਜ", "Select": "", "Select a base model": "ਆਧਾਰ ਮਾਡਲ ਚੁਣੋ", "Select a base model (e.g. llama3, gpt-4o)": "", "Select a conversation to preview": "", "Select a engine": "", "Select a function": "", "Select a group": "", "Select a language": "", "Select a mode": "", "Select a model": "ਇੱਕ ਮਾਡਲ ਚੁਣੋ", "Select a model (optional)": "", "Select a pipeline": "ਪਾਈਪਲਾਈਨ ਚੁਣੋ", "Select a pipeline url": "ਪਾਈਪਲਾਈਨ URL ਚੁਣੋ", "Select a reranking model engine": "", "Select a role": "", "Select a theme": "", "Select a tool": "", "Select a voice": "", "Select an auth method": "", "Select an embedding model engine": "", "Select an engine": "", "Select an Ollama instance": "", "Select an output format": "", "Select dtype": "", "Select Engine": "", "Select how to split message text for TTS requests": "", "Select Knowledge": "", "Select only one model to call": "", "Selected model(s) do not support image inputs": "ਚੁਣੇ ਗਏ ਮਾਡਲ(ਆਂ) ਚਿੱਤਰ ਇਨਪੁੱਟਾਂ ਦਾ ਸਮਰਥਨ ਨਹੀਂ ਕਰਦੇ", "semantic": "", "Semantic distance to query": "", "Send": "ਭੇਜੋ", "Send a Message": "ਇੱਕ ਸੁਨੇਹਾ ਭੇਜੋ", "Send message": "ਸੁਨੇਹਾ ਭੇਜੋ", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "ਸਤੰਬਰ", "SerpApi API Key": "", "SerpApi Engine": "", "Serper API Key": "Serper API ਕੁੰਜੀ", "Serply API Key": "", "Serpstack API Key": "Serpstack API ਕੁੰਜੀ", "Server connection verified": "ਸਰਵਰ ਕਨੈਕਸ਼ਨ ਦੀ ਪੁਸ਼ਟੀ ਕੀਤੀ ਗਈ", "Session": "", "Set as default": "ਮੂਲ ਵਜੋਂ ਸੈੱਟ ਕਰੋ", "Set CFG Scale": "", "Set Default Model": "ਮੂਲ ਮਾਡਲ ਸੈੱਟ ਕਰੋ", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "ਐਮਬੈੱਡਿੰਗ ਮਾਡਲ ਸੈੱਟ ਕਰੋ (ਉਦਾਹਰਣ ਲਈ {{model}})", "Set Image Size": "ਚਿੱਤਰ ਆਕਾਰ ਸੈੱਟ ਕਰੋ", "Set reranking model (e.g. {{model}})": "ਮੁੜ ਰੈਂਕਿੰਗ ਮਾਡਲ ਸੈੱਟ ਕਰੋ (ਉਦਾਹਰਣ ਲਈ {{model}})", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "ਕਦਮ ਸੈੱਟ ਕਰੋ", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "ਆਵਾਜ਼ ਸੈੱਟ ਕਰੋ", "Set whisper model": "", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets how far back for the model to look back to prevent repetition.": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "", "Sets the size of the context window used to generate the next token.": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "ਸੈਟਿੰਗਾਂ", "Settings saved successfully!": "ਸੈਟਿੰਗਾਂ ਸਫਲਤਾਪੂਰਵਕ ਸੰਭਾਲੀਆਂ ਗਈਆਂ!", "Share": "ਸਾਂਝਾ ਕਰੋ", "Share Chat": "ਗੱਲਬਾਤ ਸਾਂਝੀ ਕਰੋ", "Share to Open WebUI Community": "ਓਪਨਵੈਬਯੂਆਈ ਕਮਿਊਨਿਟੀ ਨਾਲ ਸਾਂਝਾ ਕਰੋ", "Share your background and interests": "", "Sharing Permissions": "", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "ਦਿਖਾਓ", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "", "Show All": "", "Show Formatting Toolbar": "", "Show image preview": "", "Show Less": "", "Show Model": "", "Show shortcuts": "ਸ਼ਾਰਟਕਟ ਦਿਖਾਓ", "Show your support!": "", "Showcased creativity": "ਸਿਰਜਣਾਤਮਕਤਾ ਦਿਖਾਈ", "Sign in": "ਸਾਈਨ ਇਨ ਕਰੋ", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "ਸਾਈਨ ਆਊਟ ਕਰੋ", "Sign up": "ਰਜਿਸਟਰ ਕਰੋ", "Sign up to {{WEBUI_NAME}}": "", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to False.": "", "Signing in to {{WEBUI_NAME}}": "", "Sink List": "", "sk-1234": "", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Something went wrong :/": "", "Sonar": "", "Sonar Deep Research": "", "Sonar Pro": "", "Sonar Reasoning": "", "Sonar Reasoning Pro": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "ਸਰੋਤ", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "ਬੋਲ ਪਛਾਣ ਗਲਤੀ: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "ਬੋਲ-ਤੋਂ-ਪਾਠ ਇੰਜਣ", "Start of the channel": "ਚੈਨਲ ਦੀ ਸ਼ੁਰੂਆਤ", "Start Tag": "", "STDOUT/STDERR": "STDOUT/STDERR", "Stop": "", "Stop Generating": "", "Stop Sequence": "ਰੋਕੋ ਕ੍ਰਮ", "Stream Chat Response": "", "Stream Delta Chunk Size": "", "Strikethrough": "", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "", "STT Settings": "STT ਸੈਟਿੰਗਾਂ", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "ਉਪਸਿਰਲੇਖ (ਉਦਾਹਰਣ ਲਈ ਰੋਮਨ ਸਾਮਰਾਜ ਬਾਰੇ)", "Success": "ਸਫਲਤਾ", "Successfully imported {{userCount}} users.": "", "Successfully updated.": "ਸਫਲਤਾਪੂਰਵਕ ਅੱਪਡੇਟ ਕੀਤਾ ਗਿਆ।", "Suggest a change": "", "Suggested": "ਸੁਝਾਇਆ ਗਿਆ", "Support": "", "Support this plugin:": "", "Supported MIME Types": "", "Sync directory": "", "System": "ਸਿਸਟਮ", "System Instructions": "", "System Prompt": "ਸਿਸਟਮ ਪ੍ਰੰਪਟ", "Tags": "", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "", "Talk to model": "ਮਾਡਲ ਨਾਲ ਗੱਲਬਾਤ ਕਰੋ", "Tap to interrupt": "", "Task List": "", "Task Model": "", "Tasks": "", "Tavily API Key": "", "Tavily Extract Depth": "", "Tell us more:": "ਸਾਨੂੰ ਹੋਰ ਦੱਸੋ:", "Temperature": "ਤਾਪਮਾਨ", "Temporary Chat": "", "Temporary Chat by Default": "", "Text Splitter": "", "Text-to-Speech": "", "Text-to-Speech Engine": "ਪਾਠ-ਤੋਂ-ਬੋਲ ਇੰਜਣ", "Thanks for your feedback!": "ਤੁਹਾਡੇ ਫੀਡਬੈਕ ਲਈ ਧੰਨਵਾਦ!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The passwords you entered don't quite match. Please double-check and try again.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "ਸਕੋਰ 0.0 (0%) ਅਤੇ 1.0 (100%) ਦੇ ਵਿਚਕਾਰ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ।", "The stream delta chunk size for the model. Increasing the chunk size will make the model respond with larger pieces of text at once.": "", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "", "The Weight of BM25 Hybrid Search. 0 more lexical, 1 more semantic. Default 0.5": "", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "ਥੀਮ", "Thinking...": "", "This action cannot be undone. Do you wish to continue?": "", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "", "This chat won't appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "ਇਹ ਯਕੀਨੀ ਬਣਾਉਂਦਾ ਹੈ ਕਿ ਤੁਹਾਡੀਆਂ ਕੀਮਤੀ ਗੱਲਾਂ ਤੁਹਾਡੇ ਬੈਕਐਂਡ ਡਾਟਾਬੇਸ ਵਿੱਚ ਸੁਰੱਖਿਅਤ ਤੌਰ 'ਤੇ ਸੰਭਾਲੀਆਂ ਗਈਆਂ ਹਨ। ਧੰਨਵਾਦ!", "This feature is experimental and may be modified or discontinued without notice.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Thorough explanation": "ਵਿਸਥਾਰ ਨਾਲ ਵਿਆਖਿਆ", "Thought for {{DURATION}}": "", "Thought for {{DURATION}} seconds": "", "Thought for less than a second": "", "Thread": "ਥ੍ਰੇਡ", "Tika": "", "Tika Server URL required.": "", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "ਸਲਾਹ: ਹਰ ਬਦਲਾਅ ਦੇ ਬਾਅਦ ਗੱਲਬਾਤ ਇਨਪੁਟ ਵਿੱਚ ਟੈਬ ਕੀ ਦਬਾ ਕੇ ਲਗਾਤਾਰ ਕਈ ਵੈਰੀਏਬਲ ਸਲਾਟਾਂ ਨੂੰ ਅੱਪਡੇਟ ਕਰੋ।", "Title": "ਸਿਰਲੇਖ", "Title (e.g. Tell me a fun fact)": "ਸਿਰਲੇਖ (ਉਦਾਹਰਣ ਲਈ ਮੈਨੂੰ ਇੱਕ ਮਜ਼ੇਦਾਰ ਤੱਥ ਦੱਸੋ)", "Title Auto-Generation": "ਸਿਰਲੇਖ ਆਟੋ-ਜਨਰੇਸ਼ਨ", "Title cannot be an empty string.": "ਸਿਰਲੇਖ ਖਾਲੀ ਸਤਰ ਨਹੀਂ ਹੋ ਸਕਦਾ।", "Title Generation": "", "Title Generation Prompt": "ਸਿਰਲੇਖ ਜਨਰੇਸ਼ਨ ਪ੍ਰੰਪਟ", "TLS": "", "To access the available model names for downloading,": "ਡਾਊਨਲੋਡ ਕਰਨ ਲਈ ਉਪਲਬਧ ਮਾਡਲ ਨਾਮਾਂ ਤੱਕ ਪਹੁੰਚਣ ਲਈ,", "To access the GGUF models available for downloading,": "ਡਾਊਨਲੋਡ ਕਰਨ ਲਈ ਉਪਲਬਧ GGUF ਮਾਡਲਾਂ ਤੱਕ ਪਹੁੰਚਣ ਲਈ,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To learn more about powerful prompt variables, click here": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "", "To select toolkits here, add them to the \"Tools\" workspace first.": "", "Toast notifications for new updates": "", "Today": "ਅੱਜ", "Toggle search": "", "Toggle settings": "ਸੈਟਿੰਗਾਂ ਟੌਗਲ ਕਰੋ", "Toggle sidebar": "ਸਾਈਡਬਾਰ ਟੌਗਲ ਕਰੋ", "Toggle whether current connection is active.": "", "Token": "", "Too verbose": "", "Tool created successfully": "", "Tool deleted successfully": "", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "", "Tool Name": "", "Tool Servers": "", "Tool updated successfully": "", "Tools": "", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "", "Tools Function Calling Prompt": "", "Tools have a function calling system that allows arbitrary code execution.": "", "Tools Public Sharing": "", "Top K": "ਸਿਖਰ K", "Top K Reranker": "", "Transformers": "", "Trouble accessing Ollama?": "ਓਲਾਮਾ ਤੱਕ ਪਹੁੰਚਣ ਵਿੱਚ ਮੁਸ਼ਕਲ?", "Trust Proxy Environment": "", "Try Again": "", "TTS Model": "", "TTS Settings": "TTS ਸੈਟਿੰਗਾਂ", "TTS Voice": "", "Type": "ਕਿਸਮ", "Type Hugging Face Resolve (Download) URL": "Hugging Face Resolve (ਡਾਊਨਲੋਡ) URL ਟਾਈਪ ਕਰੋ", "Uh-oh! There was an issue with the response.": "", "UI": "", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Underline": "", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "", "Unpin": "", "Unravel secrets": "", "Unsupported file type.": "", "Untagged": "", "Untitled": "", "Update": "", "Update and Copy Link": "ਅੱਪਡੇਟ ਕਰੋ ਅਤੇ ਲਿੰਕ ਕਾਪੀ ਕਰੋ", "Update for the latest features and improvements.": "", "Update password": "ਪਾਸਵਰਡ ਅੱਪਡੇਟ ਕਰੋ", "Updated": "", "Updated at": "", "Updated At": "", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "", "Upload": "", "Upload a GGUF model": "ਇੱਕ GGUF ਮਾਡਲ ਅਪਲੋਡ ਕਰੋ", "Upload Audio": "", "Upload directory": "", "Upload files": "", "Upload Files": "ਫਾਇਲਾਂ ਅੱਪਲੋਡ ਕਰੋ", "Upload Pipeline": "", "Upload Progress": "ਅਪਲੋਡ ਪ੍ਰਗਤੀ", "Upload Progress: {{uploadedFiles}}/{{totalFiles}} ({{percentage}}%)": "", "URL": "", "URL is required": "", "URL Mode": "URL ਮੋਡ", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "", "Use groups to group your users and assign permissions.": "", "Use LLM": "", "Use no proxy to fetch page contents.": "", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "user": "ਉਪਭੋਗਤਾ", "User": "", "User Groups": "", "User location successfully retrieved.": "", "User menu": "", "User Webhooks": "", "Username": "", "Users": "ਉਪਭੋਗਤਾ", "Using Entire Document": "", "Using Focused Retrieval": "", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Valid time units:": "ਵੈਧ ਸਮਾਂ ਇਕਾਈਆਂ:", "Validate certificate": "", "Valves": "", "Valves updated": "", "Valves updated successfully": "", "variable": "ਵੈਰੀਏਬਲ", "Verify Connection": "", "Verify SSL Certificate": "", "Version": "ਵਰਜਨ", "Version {{selectedVersion}} of {{totalVersions}}": "", "View Replies": "", "View Result from **{{NAME}}**": "", "Visibility": "", "Vision": "", "Voice": "", "Voice Input": "", "Voice mode": "", "Warning": "ਚੇਤਾਵਨੀ", "Warning:": "ਚੇਤਾਵਨੀ:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "ਚੇਤਾਵਨੀ: ਜੇ ਤੁਸੀਂ ਆਪਣਾ ਐਮਬੈੱਡਿੰਗ ਮਾਡਲ ਅੱਪਡੇਟ ਜਾਂ ਬਦਲਦੇ ਹੋ, ਤਾਂ ਤੁਹਾਨੂੰ ਸਾਰੇ ਡਾਕੂਮੈਂਟ ਮੁੜ ਆਯਾਤ ਕਰਨ ਦੀ ਲੋੜ ਹੋਵੇਗੀ।", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "", "Web": "ਵੈਬ", "Web API": "", "Web Loader Engine": "", "Web Search": "ਵੈੱਬ ਖੋਜ", "Web Search Engine": "ਵੈੱਬ ਖੋਜ ਇੰਜਣ", "Web Search in Chat": "", "Web Search Query Generation": "", "Webhook URL": "ਵੈਬਹੁੱਕ URL", "WebUI Settings": "ਵੈਬਯੂਆਈ ਸੈਟਿੰਗਾਂ", "WebUI URL": "", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "What are you trying to achieve?": "", "What are you working on?": "", "What's New in": "ਨਵਾਂ ਕੀ ਹੈ", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "", "Why?": "", "Widescreen Mode": "", "Width": "", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "", "Workspace": "ਕਾਰਜਸਥਲ", "Workspace Permissions": "", "Write": "", "Write a prompt suggestion (e.g. Who are you?)": "ਇੱਕ ਪ੍ਰੰਪਟ ਸੁਝਾਅ ਲਿਖੋ (ਉਦਾਹਰਣ ਲਈ ਤੁਸੀਂ ਕੌਣ ਹੋ?)", "Write a summary in 50 words that summarizes [topic or keyword].": "50 ਸ਼ਬਦਾਂ ਵਿੱਚ ਇੱਕ ਸੰਖੇਪ ਲਿਖੋ ਜੋ [ਵਿਸ਼ਾ ਜਾਂ ਕੁੰਜੀ ਸ਼ਬਦ] ਨੂੰ ਸੰਖੇਪ ਕਰਦਾ ਹੈ।", "Write something...": "", "Write your model system prompt content here\ne.g.) You are Mario from Super Mario Bros, acting as an assistant.": "ਇੱਥੇ ਆਪਣੇ ਮਾਡਲ ਦੇ ਸਿਸਟਮ ਪ੍ਰੌਮਪਟ ਦੀ ਸਮੱਗਰੀ ਲਿਖੋ\nਉਦਾਹਰਨ: ਤੁਸੀਂ Super Mario Bros ਦੇ ਮਾਰਿਓ ਹੋ ਅਤੇ ਸਹਾਇਕ ਵਜੋਂ ਕੰਮ ਕਰ ਰਹੇ ਹੋ।", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "ਕੱਲ੍ਹ", "You": "ਤੁਸੀਂ", "You are currently using a trial license. Please contact support to upgrade your license.": "", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "", "You cannot upload an empty file.": "", "You do not have permission to upload files.": "", "You have no archived conversations.": "ਤੁਹਾਡੇ ਕੋਲ ਕੋਈ ਆਰਕਾਈਵ ਕੀਤੀਆਂ ਗੱਲਾਂ ਨਹੀਂ ਹਨ।", "You have shared this chat": "ਤੁਸੀਂ ਇਹ ਗੱਲਬਾਤ ਸਾਂਝੀ ਕੀਤੀ ਹੈ", "You're a helpful assistant.": "ਤੁਸੀਂ ਇੱਕ ਮਦਦਗਾਰ ਸਹਾਇਕ ਹੋ।", "You're now logged in.": "ਤੁਸੀਂ ਹੁਣ ਲੌਗ ਇਨ ਹੋ ਗਏ ਹੋ।", "Your Account": "", "Your account status is currently pending activation.": "", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "ਯੂਟਿਊਬ", "Youtube Language": "", "Youtube Proxy URL": ""}