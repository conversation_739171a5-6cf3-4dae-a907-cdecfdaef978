{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' או '-1' ללא תפוגה.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "", "(e.g. `sh webui.sh --api`)": "(למשל `sh webui.sh --api`)", "(latest)": "(הא<PERSON><PERSON><PERSON><PERSON>)", "(leave blank for to use commercial endpoint)": "", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "{{ מודלים }}", "{{COUNT}} Available Tools": "", "{{COUNT}} characters": "", "{{COUNT}} extracted lines": "", "{{COUNT}} hidden lines": "", "{{COUNT}} Replies": "", "{{COUNT}} words": "", "{{model}} download has been canceled": "", "{{user}}'s Chats": "צ'אטים של {{user}}", "{{webUIName}} Backend Required": "נדרש Backend של {{webUIName}}", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "מודל משימה משמש בעת ביצוע משימות כגון יצירת כותרות עבור צ'אטים ושאילתות חיפוש באינטרנט", "a user": "משת<PERSON>ש", "About": "אודות", "Accept autocomplete generation / Jump to prompt variable": "", "Access": "גישה", "Access Control": "בקרת גישה", "Accessible to all users": "", "Account": "<PERSON><PERSON><PERSON><PERSON>ן", "Account Activation Pending": "", "Accurate information": "מידע מדויק", "Action": "", "Action not found": "", "Action Required for Chat Log Storage": "נדרשת פעולה לשמירת יומן הצ'אט", "Actions": "פעולה", "Activate": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active": "פעיל", "Active Users": "משתמשים מחוברים", "Add": "הוסף", "Add a model ID": "", "Add a short description about what this model does": "הוסף תיאור קצר אודות אופן הפעולה של מודל זה", "Add a tag": "הוסף תג", "Add Arena Model": "", "Add Connection": "", "Add Content": "הוסף תוכן", "Add content here": "הוסף תוכן כאן", "Add Custom Parameter": "", "Add custom prompt": "הוסף פקודה מותאמת אישית", "Add Details": "", "Add Files": "הוסף קבצים", "Add Group": "הוסף קבוצה", "Add Memory": "הוסף זיכרון", "Add Model": "הוסף מודל", "Add Reaction": "", "Add Tag": "הוסף תג", "Add Tags": "הוסף תגים", "Add text content": "", "Add User": "הוסף משתמש", "Add User Group": "", "Additional Config": "", "Additional configuration options for marker. This should be a JSON string with key-value pairs. For example, '{\"key\": \"value\"}'. Supported keys include: disable_links, keep_pageheader_in_output, keep_pagefooter_in_output, filter_blank_pages, drop_repeated_text, layout_coverage_threshold, merge_threshold, height_tolerance, gap_threshold, image_threshold, min_line_length, level_count, default_level": "", "Adjusting these settings will apply changes universally to all users.": "התאמת הגדרות אלו תחול על כל המשתמשים.", "admin": "מנהל", "Admin": "מנהל", "Admin Panel": "לוח בקרה למנהל", "Admin Settings": "הגדרות מנהל", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "", "Advanced Parameters": "פרמטרים מתקדמים", "Advanced Params": "פרמטרים מתקדמים", "AI": "", "All": "", "All Documents": "כל המסמכים", "All models deleted successfully": "כל המודלים נמחקו בהצלחה", "Allow Call": "", "Allow Chat Controls": "", "Allow Chat Delete": "", "Allow Chat Deletion": "אפשר מחיקת צ'אט", "Allow Chat Edit": "אפשר עריכת צ'אט", "Allow Chat Export": "", "Allow Chat Params": "", "Allow Chat Share": "אפשר שיתוף צ'אט", "Allow Chat System Prompt": "", "Allow Chat Valves": "", "Allow Continue Response": "", "Allow Delete Messages": "", "Allow File Upload": "אפשר העלאת קובץ", "Allow Multiple Models in Chat": "", "Allow non-local voices": "", "Allow Rate Response": "", "Allow Regenerate Response": "", "Allow Speech to Text": "", "Allow Temporary Chat": "", "Allow Text to Speech": "", "Allow User Location": "", "Allow Voice Interruption in Call": "", "Allowed Endpoints": "", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "כבר יש לך חשבון?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "", "Always": "תמיד", "Always Collapse Code Blocks": "", "Always Expand Details": "", "Always Play Notification Sound": "", "Amazing": "מדהים", "an assistant": "עוזר", "An error occurred while fetching the explanation": "", "Analytics": "", "Analyzed": "", "Analyzing...": "", "and": "וגם", "and {{COUNT}} more": "", "and create a new shared link.": "וצור קישור משותף חדש.", "Android": "", "API": "", "API Base URL": "כתובת URL בסיסית ל-API", "API Base URL for Datalab Marker service. Defaults to: https://www.datalab.to/api/v1/marker": "", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "מפתח API", "API Key created.": "מפתח API נוצר.", "API Key Endpoint Restrictions": "", "API keys": "מפתחות API", "API Version": "", "API Version is required": "", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "אפריל", "Archive": "אר<PERSON><PERSON><PERSON>ן", "Archive All Chats": "אח<PERSON><PERSON> באר<PERSON><PERSON>ון את כל הצ'אטים", "Archived Chats": "צ'אטים מאורכבים", "archived-chat-export": "", "Are you sure you want to clear all memories? This action cannot be undone.": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "האם אתה בטוח?", "Arena Models": "", "Artifacts": "", "Ask": "", "Ask a question": "", "Assistant": "", "Attach file from knowledge": "", "Attention to detail": "תשומת לב לפרטים", "Attribute for Mail": "", "Attribute for Username": "", "Audio": "אודיו", "August": "אוגוסט", "Auth": "", "Authenticate": "", "Authentication": "", "Auto": "אוטו<PERSON>טי", "Auto-Copy Response to Clipboard": "העתקה אוטומטית של תגובה ללוח", "Auto-playback response": "תגובת השמעה אוטומטית", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "", "AUTOMATIC1111 Base URL": "כתובת URL בסיסית של AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "נדרשת כתובת URL בסיסית של AUTOMATIC1111", "Available list": "", "Available Tools": "כלים זמינים", "available users": "משתמשים זמינים", "available!": "זמין!", "Away": "נעדר", "Awful": "", "Azure AI Speech": "", "Azure OpenAI": "Azure OpenAI", "Azure Region": "", "Back": "חז<PERSON>ר", "Bad Response": "תגובה שגויה", "Banners": "באנרים", "Base Model (From)": "דגם בסיס (מ)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "Bearer": "", "before": "לפני", "Being lazy": "להיות עצלן", "Beta": "בטא", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Bio": "", "Birth Date": "", "BM25 Weight": "", "Bocha Search API Key": "", "Bold": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "מפתח API של חיפוש אמיץ", "Bullet List": "", "Button ID": "", "Button Label": "", "Button Prompt": "", "By {{name}}": "", "Bypass Embedding and Retrieval": "", "Bypass Web Loader": "", "Cache Base Model List": "", "Calendar": "לוח שנה", "Call": "", "Call feature is not supported when using Web STT engine": "", "Camera": "מצלמה", "Cancel": "בטל", "Capabilities": "יכולות", "Capture": "", "Capture Audio": "", "Certificate Path": "", "Change Password": "שנה סיסמה", "Channel deleted successfully": "", "Channel Name": "", "Channel updated successfully": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "צ'אט", "Chat Background Image": "", "Chat Bubble UI": "UI של תיבת הדיבור", "Chat Controls": "", "Chat Conversation": "", "Chat direction": "כיוון צ'אט", "Chat ID": "", "Chat moved successfully": "", "Chat Overview": "", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "צ'אטים", "Check Again": "<PERSON><PERSON><PERSON><PERSON> שוב", "Check for updates": "בד<PERSON><PERSON> עדכונים", "Checking for updates...": "בודק עדכונים...", "Choose a model before saving...": "בחר מודל לפני השמירה...", "Chunk Overlap": "חפי<PERSON>ת נתונים", "Chunk Size": "גודל נתונים", "Ciphers": "", "Citation": "ציטוט", "Citations": "", "Clear memory": "נקה זיכרון", "Clear Memory": "נקה", "click here": "לחץ פה", "Click here for filter guides.": "", "Click here for help.": "לחץ כאן לעזרה.", "Click here to": "לחץ כאן כדי", "Click here to download user import template file.": "", "Click here to learn more about faster-whisper and see the available models.": "לחץ כאן כדי ללמוד עוד על faster-whisper ולראות מודלים זמינים", "Click here to see available models.": "לחץ כאן כדי לראות מודלים זמינים", "Click here to select": "לחץ כאן לבחירה", "Click here to select a csv file.": "לחץ כאן לבחירת קובץ csv.", "Click here to select a py file.": "לחץ כאן כדי לבחירת קובץ py", "Click here to upload a workflow.json file.": "", "click here.": "לחץ כאן.", "Click on the user role button to change a user's role.": "לחץ על כפתור תפקיד המשתמש כדי לשנות את תפקיד המשתמש.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "", "Clone": "שיבוט", "Clone Chat": "", "Clone of {{TITLE}}": "", "Close": "סגור", "Close Banner": "", "Close Configure Connection Modal": "", "Close modal": "", "Close settings modal": "", "Close Sidebar": "", "CMU ARCTIC speaker embedding name": "", "Code Block": "", "Code execution": "הרצת קוד", "Code Execution": "הרצת קוד", "Code Execution Engine": "מנוע הרצת קוד", "Code Execution Timeout": "", "Code formatted successfully": "", "Code Interpreter": "", "Code Interpreter Engine": "", "Code Interpreter Prompt Template": "", "Collapse": "", "Collection": "אוסף", "Color": "צבע", "ComfyUI": "ComfyUI", "ComfyUI API Key": "מפתח API כל ComfyUI", "ComfyUI Base URL": "כתובת URL בסיסית של ComfyUI", "ComfyUI Base URL is required.": "נדרשת כתובת URL בסיסית של ComfyUI", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Comma separated Node Ids (e.g. 1 or 1,2)": "", "Command": "פקודה", "Comment": "", "Completions": "", "Compress Images in Channels": "", "Concurrent Requests": "בקשות בו-זמניות", "Config imported successfully": "", "Configure": "", "Confirm": "", "Confirm Password": "א<PERSON>ר סיסמה", "Confirm your action": "אשר את הפעולה שלך", "Confirm your new password": "אשר את הסיסמה החדשה שלך", "Confirm Your Password": "", "Connect to your own OpenAI compatible API endpoints.": "", "Connect to your own OpenAPI compatible external tool servers.": "", "Connection failed": "החי<PERSON><PERSON><PERSON> נכשל", "Connection successful": "החיבור הצליח", "Connection Type": "סוג חיבור", "Connections": "חיבורים", "Connections saved successfully": "החיבור נשמר בהצלחה", "Connections settings updated": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "", "Contact Admin for WebUI Access": "", "Content": "תו<PERSON><PERSON>", "Content Extraction Engine": "", "Continue Response": "המשך תגובה", "Continue with {{provider}}": "המשך עם {{provider}}", "Continue with Email": "המשך עם מייל", "Continue with LDAP": "המשך עם LDAP", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "", "Controls": "", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "", "Conversation saved successfully": "", "Copied": "הועתק", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "העתקת כתובת URL של צ'אט משותף ללוח!", "Copied to clipboard": "הועתק ללוח", "Copy": "העתק", "Copy Formatted Text": "", "Copy last code block": "העתק את בלוק הקוד האחרון", "Copy last response": "העתק את התגובה האחרונה", "Copy link": "", "Copy Link": "העת<PERSON> קישור", "Copy to clipboard": "", "Copying to clipboard was successful!": "ההעתקה ללוח הייתה מוצלחת!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "", "Create": "", "Create a knowledge base": "", "Create a model": "יצירת מודל", "Create Account": "<PERSON><PERSON><PERSON>ן", "Create Admin Account": "", "Create Channel": "", "Create Folder": "", "Create Group": "יצירת קבוצה", "Create Knowledge": "", "Create new key": "צור מפתח חדש", "Create new secret key": "צור מפתח סודי חדש", "Create Note": "יצירת פתק", "Create your first note by clicking on the plus button below.": "", "Created at": "נוצר ב", "Created At": "נוצר ב", "Created by": "", "CSV Import": "", "Ctrl+Enter to Send": "", "Current Model": "המודל הנוכחי", "Current Password": "הסיס<PERSON>ה הנוכחית", "Custom": "מותאם אישית", "Custom description enabled": "", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "", "Dark": "כהה", "Database": "מסד נתונים", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "DD/MM/YYYY": "", "December": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Deepgram": "", "Default": "ברירת מחדל", "Default (Open AI)": "", "Default (SentenceTransformers)": "ברירת מחדל (SentenceTransformers)", "Default action buttons will be used.": "", "Default description enabled": "", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "", "Default Model": "מודל ברירת מחדל", "Default model updated": "המודל המוגדר כברירת מחדל עודכן", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "הצעות ברירת מחדל לפקודות", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "", "Default User Role": "תפקיד משת<PERSON>ש ברירת מחדל", "Delete": "מחק", "Delete a model": "<PERSON><PERSON><PERSON> מודל", "Delete All Chats": "מחק את כל הצ'אטים", "Delete All Models": "", "Delete chat": "מחק צ'אט", "Delete Chat": "מחק צ'אט", "Delete chat?": "", "Delete folder?": "", "Delete function?": "", "Delete Message": "", "Delete message?": "", "Delete note?": "", "Delete prompt?": "", "delete this link": "מחק את הקישור הזה", "Delete tool?": "", "Delete User": "מח<PERSON> משתמש", "Deleted {{deleteModelTag}}": "נמחק {{deleteModelTag}}", "Deleted {{name}}": "נמחק {{name}}", "Deleted User": "", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "", "Description": "תיאור", "Detect Artifacts Automatically": "", "Dictate": "", "Didn't fully follow instructions": "לא עקב אחרי ההוראות באופן מלא", "Direct": "", "Direct Connections": "", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "", "Direct Tool Servers": "", "Directory selection was cancelled": "", "Disable Code Interpreter": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "מושבת", "Discover a function": "", "Discover a model": "גלה מודל", "Discover a prompt": "גלה פקודה", "Discover a tool": "", "Discover how to use Open WebUI and seek support from the community.": "", "Discover wonders": "", "Discover, download, and explore custom functions": "", "Discover, download, and explore custom prompts": "גלה, הורד, וחק<PERSON>ר פקודות מותאמות אישית", "Discover, download, and explore custom tools": "", "Discover, download, and explore model presets": "גלה, הורד, וחקור הגדרות מודל מוגדרות מראש", "Display": "", "Display Emoji in Call": "", "Display Multi-model Responses in Tabs": "", "Display the username instead of You in the Chat": "הצג את שם המשתמש במקום 'אתה' בצ'אט", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "", "Do not install tools from sources you do not fully trust.": "", "Docling": "", "Docling Server URL required.": "", "Document": "מסמך", "Document Intelligence": "", "Document Intelligence endpoint required.": "", "Documentation": "", "Documents": "מסמכים", "does not make any external connections, and your data stays securely on your locally hosted server.": "לא מבצע חיבורים חיצוניים, והנתונים שלך נשמרים באופן מאובטח בשרת המקומי שלך.", "Domain Filter List": "", "don't fetch random pipelines from sources you don't trust.": "אל תמשוך pipelines אקראיים ממקורות שאינך סומך עליהם.", "Don't have an account?": "אין לך חשבון?", "don't install random functions from sources you don't trust.": "", "don't install random tools from sources you don't trust.": "", "Don't like the style": "לא אוהב את הסגנון", "Done": "", "Download": "הורד", "Download & Delete": "הורדה ומחיקה", "Download as SVG": "", "Download canceled": "ההורדה בוטלה", "Download Database": "הורד מסד נתונים", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "למשל '30s', '10m'. יחידות זמן חוקיות הן 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "", "e.g. 60": "", "e.g. A filter to remove profanity from text": "", "e.g. en": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "e.g., westus (leave blank for eastus)": "", "Edit": "ערוך", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Folder": "", "Edit Memory": "", "Edit User": "ערוך משתמש", "Edit User Group": "", "Edited": "", "Editing": "", "Eject": "", "ElevenLabs": "", "Email": "דוא\"ל", "Embark on adventures": "", "Embedding": "", "Embedding Batch Size": "", "Embedding Model": "מודל הטמעה", "Embedding Model Engine": "מנוע מודל הטמעה", "Embedding model set to \"{{embedding_model}}\"": "מודל ההטמעה הוגדר ל-\"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Code Execution": "", "Enable Code Interpreter": "", "Enable Community Sharing": "הפיכת שיתוף קהילה לזמין", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity.": "", "Enable New Sign Ups": "אפשר הרשמות חדשות", "Enable, disable, or customize the reasoning tags used by the model. \"Enabled\" uses default tags, \"Disabled\" turns off reasoning tags, and \"Custom\" lets you specify your own start and end tags.": "", "Enabled": "מופעל", "End Tag": "", "Endpoint URL": "", "Enforce Temporary Chat": "", "Enhance": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "ודא שקובץ ה-CSV שלך כולל 4 עמודות בסדר הבא: שם, דוא\"ל, סיסמה, תפקיד.", "Enter {{role}} message here": "הזן הודעת {{role}} כאן", "Enter a detail about yourself for your LLMs to recall": "הזן פרטים על עצמך כדי שLLMs יזכור", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Bocha Search API Key": "", "Enter Brave Search API Key": "הזן מפתח API של חיפוש אמיץ", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "הזן חפיפת נתונים", "Enter Chunk Size": "הזן גודל נתונים", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter coordinates (e.g. 51.505, -0.09)": "", "Enter Datalab Marker API Base URL": "", "Enter Datalab Marker API Key": "", "Enter description": "", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "", "Enter Document Intelligence Endpoint": "", "Enter Document Intelligence Key": "", "Enter domains separated by commas (e.g., example.com,site.org)": "", "Enter Exa API Key": "", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter folder name": "", "Enter Github Raw URL": "הזן כתובת URL של Github Raw", "Enter Google PSE API Key": "הזן מפתח API של Google PSE", "Enter Google PSE Engine Id": "הזן את מזהה מנוע PSE של Google", "Enter hex color (e.g. #FF0000)": "", "Enter ID": "", "Enter Image Size (e.g. 512x512)": "הזן גודל תמונה (למשל 512x512)", "Enter Jina API Key": "", "Enter JSON config (e.g., {\"disable_links\": true})": "", "Enter Jupyter Password": "", "Enter Jupyter Token": "", "Enter Jupyter URL": "", "Enter Kagi Search API Key": "", "Enter Key Behavior": "", "Enter language codes": "הזן קודי שפה", "Enter Mistral API Key": "", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "הזן תג מודל (למ<PERSON><PERSON> {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "הזן מספר שלבים (למ<PERSON><PERSON> 50)", "Enter Perplexity API Key": "", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "", "Enter reasoning effort": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "הזן ציון", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "הזן כתובת URL של שאילתת Searxng", "Enter Seed": "", "Enter SerpApi API Key": "", "Enter SerpApi Engine": "", "Enter Serper API Key": "הזן מפתח API של Serper", "Enter Serply API Key": "", "Enter Serpstack API Key": "הזן מפתח API של Serpstack", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter Sougou Search API sID": "", "Enter Sougou Search API SK": "", "Enter stop sequence": "הזן רצף עצירה", "Enter system prompt": "", "Enter system prompt here": "", "Enter Tavily API Key": "", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "", "Enter timeout in seconds": "", "Enter to Send": "", "Enter Top K": "הזן Top K", "Enter Top K Reranker": "", "Enter URL (e.g. http://127.0.0.1:7860/)": "הזן כתובת URL (<PERSON><PERSON><PERSON><PERSON> http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "הזן כתובת URL (ל<PERSON><PERSON><PERSON> http://localhost:11434)", "Enter value": "", "Enter value (true/false)": "", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your code here...": "הזן את הקוד שלך כאן...", "Enter your current password": "", "Enter Your Email": "הזן את דוא\"ל שלך", "Enter Your Full Name": "הזן את שמך המלא", "Enter your gender": "", "Enter your message": "", "Enter your name": "", "Enter Your Name": "", "Enter your new password": "", "Enter Your Password": "הזן את הסיסמה שלך", "Enter Your Role": "הזן את התפקיד שלך", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "שגיאה", "ERROR": "", "Error accessing directory": "", "Error accessing Google Drive: {{error}}": "", "Error accessing media devices.": "", "Error starting recording.": "", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "", "Error: A model with the ID '{{modelId}}' already exists. Please select a different ID to proceed.": "", "Error: Model ID cannot be empty. Please enter a valid ID to proceed.": "", "Evaluations": "", "Everyone": "", "Exa API Key": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: mail": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "", "Exclude": "", "Execute code for analysis": "", "Executing **{{NAME}}**...": "", "Expand": "", "Experimental": "ניסיוני", "Explain": "", "Explore the cosmos": "", "Export": "ייצא", "Export All Archived Chats": "", "Export All Chats (All Users)": "ייצוא כל הצ'אטים (כל המשתמשים)", "Export chat (.json)": "", "Export Chats": "ייצוא צ'אטים", "Export Config to JSON File": "", "Export Functions": "", "Export Models": "ייצוא מודלים", "Export Presets": "", "Export Prompt Suggestions": "", "Export Prompts": "ייצוא פקודות", "Export to CSV": "", "Export Tools": "", "Export Users": "", "External": "", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "", "External Web Loader URL": "", "External Web Search API Key": "", "External Web Search URL": "", "Fade Effect for Streaming Text": "", "Failed to add file.": "", "Failed to connect to {{URL}} OpenAPI tool server": "", "Failed to copy link": "", "Failed to create API Key.": "יצירת מפתח API נכשלה.", "Failed to delete note": "", "Failed to extract content from the file: {{error}}": "", "Failed to extract content from the file.": "", "Failed to fetch models": "", "Failed to generate title": "", "Failed to load chat preview": "", "Failed to load file content.": "", "Failed to move chat": "", "Failed to read clipboard contents": "קריאת תוכן הלוח נכשלה", "Failed to save connections": "", "Failed to save conversation": "שמירת השיחה נכשלה", "Failed to save models configuration": "", "Failed to update settings": "", "Failed to upload file.": "", "Features": "", "Features Permissions": "", "February": "פבר<PERSON><PERSON><PERSON>", "Feedback Details": "", "Feedback History": "", "Feedbacks": "", "Feel free to add specific details": "נא להוסיף פרטים ספציפיים לפי רצון", "Female": "", "File": "", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "מצב קובץ", "File not found.": "הקובץ לא נמצא.", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File Upload": "", "File uploaded successfully": "", "Files": "", "Filter": "", "Filter is now globally disabled": "", "Filter is now globally enabled": "", "Filters": "", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "התגלתה הזיית טביעת אצבע: לא ניתן להשתמש בראשי תיבות כאווטאר. משתמש בתמונת פרופיל ברירת מחדל.", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Floating Quick Actions": "", "Focus chat input": "מיקוד הקלט לצ'אט", "Folder deleted successfully": "", "Folder Name": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Folder updated successfully": "", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "עקב אחר ההוראות במושלמות", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "", "Form": "", "Format Lines": "", "Format the lines in the output. Defaults to False. If set to True, the lines will be formatted to detect inline math and styles.": "", "Format your variables using brackets like this:": "", "Formatting may be inconsistent from source.": "", "Forwards system user session credentials to authenticate": "", "Full Context Mode": "", "Function": "", "Function Calling": "", "Function created successfully": "", "Function deleted successfully": "", "Function Description": "", "Function ID": "", "Function imported successfully": "", "Function is now globally disabled": "", "Function is now globally enabled": "", "Function Name": "", "Function updated successfully": "", "Functions": "", "Functions allow arbitrary code execution.": "", "Functions imported successfully": "", "Gemini": "", "Gemini API Config": "", "Gemini API Key is required.": "", "Gender": "", "General": "כללי", "Generate": "", "Generate an image": "", "Generate Image": "", "Generate prompt pair": "", "Generating search query": "יצירת שאילתת חיפוש", "Generating...": "מג'נרט...", "Get information on {{name}} in the UI": "", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "גלובלי", "Good Response": "תגובה טובה", "Google Drive": "", "Google PSE API Key": "מפתח API של Google PSE", "Google PSE Engine Id": "מזהה מנוע PSE של Google", "Gravatar": "", "Group": "קבוצה", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "קבוצות", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "", "Height": "", "Hello, {{name}}": "שלום, {{name}}", "Help": "עזרה", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "הסתר", "Hide from Sidebar": "", "Hide Model": "הסתר מודל", "High": "", "High Contrast Mode": "", "Home": "בית", "Host": "", "How can I help you today?": "כיצד אוכל לעזור לך היום?", "How would you rate this response?": "", "HTML": "", "Hybrid Search": "<PERSON>י<PERSON><PERSON><PERSON> היברידי", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "", "ID": "", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "", "Image": "תמונה", "Image Compression": "", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "", "Image Generation (Experimental)": "יצירת תמונות (ניסיוני)", "Image Generation Engine": "מנוע יצירת תמונות", "Image Max Compression Size": "", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "", "Image Prompt Generation Prompt": "", "Image Settings": "הגדרות תמונה", "Images": "תמונות", "Import": "", "Import Chats": "יבוא צ'אטים", "Import Config from JSON File": "", "Import From Link": "", "Import Functions": "", "Import Models": "ייבו<PERSON> דגמים", "Import Notes": "ייבוא פתקים", "Import Presets": "", "Import Prompt Suggestions": "", "Import Prompts": "ייבוא פקודות", "Import Tools": "ייבוא כלים", "Important Update": "ע<PERSON><PERSON><PERSON><PERSON> חשוב", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "", "Include `--api` flag when running stable-diffusion-webui": "כלול את הדגל `--api` בעת הרצת stable-diffusion-webui", "Includes SharePoint": "כולל SharePoint", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "", "Info": "מידע", "Initials": "", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "", "Input": "", "Input commands": "פקודות קלט", "Input Key (e.g. text, unet_name, steps)": "", "Input Variables": "", "Insert": "", "Insert Follow-Up Prompt to Input": "", "Insert Prompt as Rich Text": "", "Install from Github URL": "התקן מכתובת URL של Github", "Instant Auto-Send After Voice Transcription": "", "Integration": "", "Interface": "<PERSON><PERSON><PERSON><PERSON>", "Invalid file content": "", "Invalid file format.": "", "Invalid JSON file": "", "Invalid JSON format for ComfyUI Workflow.": "", "Invalid JSON format in Additional Config": "", "Invalid Tag": "תג לא חוקי", "is typing...": "מקליד...", "Italic": "", "January": "ינו<PERSON>ר", "Jina API Key": "", "join our Discord for help.": "הצטרף ל-Discord שלנו לעזרה.", "JSON": "JSON", "JSON Preview": "תצוגה מקדימה של JSON", "July": "יולי", "June": "יוני", "Jupyter Auth": "", "Jupyter URL": "", "JWT Expiration": "תפוגת JWT", "JWT Token": "אסימון JWT", "Kagi Search API Key": "", "Keep Follow-Up Prompts in Chat": "", "Keep in Sidebar": "", "Key": "", "Key is required": "", "Keyboard shortcuts": "קיצורי מקלדת", "Knowledge": "", "Knowledge Access": "", "Knowledge Base": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge Description": "", "Knowledge Name": "", "Knowledge Public Sharing": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Kokoro.js (Browser)": "", "Kokoro.js Dtype": "", "Label": "", "Landing Page Mode": "", "Language": "שפה", "Language Locales": "", "Last Active": "פעיל לאחרונה", "Last Modified": "", "Last reply": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Learn More": "", "Learn more about OpenAPI tool servers.": "", "Leave empty for no compression": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{url}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Leave model field empty to use the default model.": "", "lexical": "", "License": "רי<PERSON><PERSON><PERSON>ן", "Lift List": "", "Light": "<PERSON><PERSON><PERSON><PERSON>", "Listening...": "", "Llama.cpp": "", "LLMs can make mistakes. Verify important information.": "מודלים בש<PERSON>ה טבעית יכולים לטעות. אמת מידע חשוב.", "Loader": "", "Loading Kokoro.js...": "", "Loading...": "טוען...", "Local": "", "Local Task Model": "", "Location access not allowed": "", "Lost": "", "Low": "", "LTR": "LTR", "Made by Open WebUI Community": "נוצר על ידי קהילת OpenWebUI", "Make password visible in the user interface": "", "Make sure to enclose them with": "ודא להקיף אותם עם", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Male": "", "Manage": "", "Manage Direct Connections": "", "Manage Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "ניהול צינורות", "Manage Tool Servers": "", "Manage your account information.": "", "March": "מרץ", "Markdown": "", "Markdown (Header)": "", "Max Speakers": "", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "ניתן להוריד מקסימום 3 מודלים בו זמנית. אנא נסה שוב מאוחר יותר.", "May": "מאי", "Medium": "", "Memories accessible by LLMs will be shown here.": "מזכירים נגישים על ידי LLMs יוצגו כאן.", "Memory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Memory added successfully": "", "Memory cleared successfully": "", "Memory deleted successfully": "", "Memory updated successfully": "", "Merge Responses": "", "Merged Response": "תגובה ממוזגת", "Message rating should be enabled to use this feature": "דירוג הודעות צריך להיות מאופשר כדי להשתמש בפיצ'ר הזה", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "הודעות שתשלח לאחר יצירת הקישור לא ישותפו. משתמשים עם כתובת האתר יוכלו לצפות בצ'אט המשותף.", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "המודל '{{modelName}}' הורד בהצלחה.", "Model '{{modelTag}}' is already in queue for downloading.": "המודל '{{modelTag}}' כבר בתור להורדה.", "Model {{modelId}} not found": "המודל {{modelId}} לא נמצא", "Model {{modelName}} is not vision capable": "דגם {{modelName}} אינו בעל יכולת ראייה", "Model {{name}} is now {{status}}": "דגם {{name}} הוא כעת {{status}}", "Model {{name}} is now hidden": "", "Model {{name}} is now visible": "", "Model accepts file inputs": "", "Model accepts image inputs": "", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "נתיב מערכת הקבצים של המודל זוהה. נדרש שם קצר של המודל לעדכון, לא ניתן להמשיך.", "Model Filtering": "", "Model ID": "מזהה דגם", "Model ID is required.": "", "Model IDs": "", "Model Name": "שם המודל", "Model name already exists, please choose a different one": "", "Model Name is required.": "", "Model not selected": "לא נבחר מודל", "Model Params": "פרמט<PERSON>י המודל", "Model Permissions": "", "Model unloaded successfully": "", "Model updated successfully": "", "Model(s) do not support file upload": "", "Modelfile Content": "תוכן קובץ מודל", "Models": "מודלים", "Models Access": "", "Models configuration saved successfully": "", "Models Public Sharing": "", "Mojeek Search API Key": "", "more": "", "More": "עוד", "More Concise": "", "More Options": "", "Move": "", "Name": "שם", "Name and ID are required, please fill them out": "", "Name your knowledge base": "", "Native": "", "New Button": "", "New Chat": "צ'אט חדש", "New Folder": "תיקייה חדשה", "New Function": "פונקציה חדשה", "New Note": "פת<PERSON> חדש", "New Password": "סיסמה חדשה", "New Tool": "כ<PERSON>י חדש", "new-channel": "", "Next message": "", "No chats found": "", "No chats found for this user.": "לא נמצאו צ'אטים ליוזר הזה.", "No chats found.": "לא נמצאו צ'אטים", "No content": "<PERSON><PERSON><PERSON> תו<PERSON>ן", "No content found": "תו<PERSON><PERSON> לא נמצא", "No content found in file.": "לא נמצא תוכן בקובץ.", "No content to speak": "", "No conversation to save": "", "No distance available": "", "No feedbacks found": "", "No file selected": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No inference engine with management support found": "", "No knowledge found": "", "No memories to clear": "", "No model IDs": "", "No models found": "", "No models selected": "", "No Notes": "", "No results": "לא נמצאו תוצאות", "No results found": "לא נמצאו תוצאות", "No search query generated": "לא נוצרה שאילתת חיפוש", "No source available": "<PERSON><PERSON>ן <PERSON><PERSON> זמין", "No suggestion prompts": "אין פרומפטים מוצעים", "No users were found.": "לא נמצאו יוזרים", "No valves": "", "No valves to update": "", "Node Ids": "", "None": "ללא", "Not factually correct": "לא נכון מבחינה עובדתית", "Not helpful": "", "Note deleted successfully": "פתק נמחק בהצלחה", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "הערה: אם תקבע ציון מינימלי, החיפוש יחזיר רק מסמכים עם ציון שגבוה או שווה לציון המינימלי.", "Notes": "פתקים", "Notification Sound": "", "Notification Webhook": "", "Notifications": "התראות", "November": "נובמ<PERSON>ר", "OAuth ID": "", "October": "או<PERSON><PERSON><PERSON><PERSON><PERSON>", "Off": "כב<PERSON>י", "Okay, Let's Go!": "בסדר, בואו נתחיל!", "OLED Dark": "OLE<PERSON> כהה", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "", "Ollama Version": "גר<PERSON><PERSON>", "On": "פועל", "OneDrive": "", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "רק תווים אלפאנומריים ומקפים מותרים במחרוזת הפקודה.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only markdown files are allowed": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "אופס! נראה שהכתובת URL אינה תקינה. אנא בדוק שוב ונסה שנית.", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "אופס! אתה משתמש בשיטה לא נתמכת (רק חזית). אנא שרת את ממשק המשתמש האינטרנטי מהשרת האחורי.", "Open file": "", "Open in full screen": "", "Open modal to configure connection": "", "Open Modal To Manage Floating Quick Actions": "", "Open new chat": "פתח צ'אט חדש", "Open Sidebar": "", "Open User Profile Menu": "", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "", "OpenAI": "OpenAI", "OpenAI API": "API של OpenAI", "OpenAI API Config": "תצורת API של OpenAI", "OpenAI API Key is required.": "נדרש מפתח API של OpenAI.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "נדרשת כתובת URL/מפתח של OpenAI.", "openapi.json URL or Path": "", "Optional": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "או", "Ordered List": "", "Organize your users": "", "Other": "<PERSON><PERSON><PERSON>", "OUTPUT": "", "Output format": "", "Output Format": "", "Overview": "", "page": "עמוד", "Paginate": "", "Parameters": "", "Password": "סיסמה", "Passwords do not match.": "", "Paste Large Text as File": "", "PDF document (.pdf)": "מסמך PDF (.pdf)", "PDF Extract Images (OCR)": "חילוץ תמונות מ-PDF (OCR)", "pending": "ממתין", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "", "Permission denied when accessing microphone": "", "Permission denied when accessing microphone: {{error}}": "ההרשאה נדחתה בעת גישה למיקרופון: {{error}}", "Permissions": "", "Perplexity API Key": "", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "תאור", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "", "Pinned": "", "Pioneer insights": "", "Pipe": "", "Pipeline deleted successfully": "", "Pipeline downloaded successfully": "", "Pipelines": "צינורות", "Pipelines are a plugin system with arbitrary code execution —": "Pipelines הוא מערכת תוספים עם יכולת להריץ קוד שרירותי —", "Pipelines Not Detected": "", "Pipelines Valves": "צינורות שסתומים", "Plain text (.md)": "", "Plain text (.txt)": "טקסט פשוט (.txt)", "Playground": "אזור משחקים", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "", "Please do not close the settings page while loading the model.": "", "Please enter a message or attach a file.": "", "Please enter a prompt": "", "Please enter a valid path": "", "Please enter a valid URL": "", "Please fill in all fields.": "", "Please select a model first.": "", "Please select a model.": "", "Please select a reason": "", "Please wait until all files are uploaded.": "", "Port": "", "Positive attitude": "גישה חיובית", "Prefer not to say": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Prevent file creation": "", "Preview": "", "Previous 30 days": "30 הימים הקודמים", "Previous 7 days": "7 הימים הקודמים", "Previous message": "", "Private": "", "Profile": "פרופיל", "Prompt": "", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "פקודה (ל<PERSON><PERSON><PERSON>, ספר לי עובדה מעניינת על האימפריה הרומית)", "Prompt Autocompletion": "", "Prompt Content": "ת<PERSON><PERSON><PERSON> הפקודה", "Prompt created successfully": "", "Prompt suggestions": "הצעות לפקודות", "Prompt updated successfully": "", "Prompts": "פקודות", "Prompts Access": "", "Prompts Public Sharing": "", "Public": "", "Pull \"{{searchValue}}\" from Ollama.com": "משוך \"{{searchValue}}\" מ-Ollama.com", "Pull a model from Ollama.com": "משוך מודל מ-Ollama.com", "Query Generation Prompt": "", "Quick Actions": "", "RAG Template": "תבנית RAG", "Rating": "", "Re-rank models by topic similarity": "", "Read": "", "Read Aloud": "קרא בקול", "Reason": "", "Reasoning Effort": "", "Reasoning Tags": "", "Record": "", "Record voice": "הקל<PERSON> קול", "Redirecting you to Open WebUI Community": "מפנה אותך לקהילת OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "", "References from": "", "Refused when it shouldn't have": "נדחה כאשר לא היה צריך", "Regenerate": "הפ<PERSON> מחדש", "Regenerate Menu": "", "Reindex": "", "Reindex Knowledge Base Vectors": "", "Release Notes": "הערות שחרור", "Releases": "", "Relevance": "", "Relevance Threshold": "", "Remember Dismissal": "", "Remove": "הסר", "Remove {{MODELID}} from list.": "", "Remove file": "", "Remove File": "", "Remove image": "", "Remove Model": "ה<PERSON>ר מודל", "Remove this tag from list": "", "Rename": "שנה שם", "Reorder Models": "", "Reply in Thread": "", "Reranking Engine": "", "Reranking Model": "מודל דירוג מחדש", "Reset": "", "Reset All Models": "", "Reset Image": "איפוס תמונה", "Reset Upload Directory": "", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "", "Response splitting": "", "Response Watermark": "", "Result": "", "RESULT": "תוצאה", "Retrieval": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "תפקיד", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "", "Running": "", "Running...": "פועל...", "Save": "שמור", "Save & Create": "ש<PERSON><PERSON><PERSON> וצור", "Save & Update": "שמו<PERSON> ועדכן", "Save As Copy": "", "Save Chat": "", "Save Tag": "", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "שמירת יומני צ'אט ישירות באחסון הדפדפן שלך אינה נתמכת יותר. אנא הקדש רגע להוריד ולמחוק את יומני הצ'אט שלך על ידי לחיצה על הכפתור למטה. אל דאגה, באפשרותך לייבא מחדש בקלות את יומני הצ'אט שלך לשרת האחורי דרך", "Scroll On Branch Change": "", "Search": "ח<PERSON><PERSON>", "Search a model": "<PERSON><PERSON><PERSON> מודל", "Search all emojis": "", "Search Base": "", "Search Chats": "חיפוש צ'אטים", "Search Collection": "", "Search Filters": "", "search for archived chats": "", "search for folders": "", "search for pinned chats": "", "search for shared chats": "", "search for tags": "", "Search Functions": "", "Search In Models": "", "Search Knowledge": "", "Search Models": "חיפוש מודלים", "Search Notes": "", "Search options": "", "Search Prompts": "<PERSON><PERSON><PERSON> פקודות", "Search Result Count": "ספירת תוצאות חיפוש", "Search the internet": "", "Search Tools": "", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searching the web...": "", "Searxng Query URL": "כתובת URL של שאילתת Searxng", "See readme.md for instructions": "ראה את readme.md להוראות", "See what's new": "ראה מה חדש", "Seed": "זרע", "Select": "", "Select a base model": "בחירת מודל בסיס", "Select a base model (e.g. llama3, gpt-4o)": "", "Select a conversation to preview": "", "Select a engine": "", "Select a function": "", "Select a group": "", "Select a language": "", "Select a mode": "", "Select a model": "<PERSON><PERSON><PERSON> מודל", "Select a model (optional)": "", "Select a pipeline": "<PERSON><PERSON><PERSON> <PERSON>ו צינור", "Select a pipeline url": "בחר כתובת URL של קו צינור", "Select a reranking model engine": "", "Select a role": "", "Select a theme": "", "Select a tool": "<PERSON><PERSON><PERSON> כלי", "Select a voice": "", "Select an auth method": "", "Select an embedding model engine": "", "Select an engine": "", "Select an Ollama instance": "", "Select an output format": "", "Select dtype": "", "Select Engine": "", "Select how to split message text for TTS requests": "", "Select Knowledge": "", "Select only one model to call": "", "Selected model(s) do not support image inputs": "דגמים נבחרים אינם תומכים בקלט תמונה", "semantic": "", "Semantic distance to query": "", "Send": "שלח", "Send a Message": "שלח הודעה", "Send message": "שלח הודעה", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "ספט<PERSON><PERSON>ר", "SerpApi API Key": "", "SerpApi Engine": "", "Serper API Key": "מפתח Serper API", "Serply API Key": "", "Serpstack API Key": "מפתח API של Serpstack", "Server connection verified": "החיבור לשרת אומת", "Session": "", "Set as default": "הגדר כברירת מחדל", "Set CFG Scale": "", "Set Default Model": "הגדר מודל ברירת מחדל", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "הגדר מודל הטמעה (למשל {{model}})", "Set Image Size": "הגדר גודל תמונה", "Set reranking model (e.g. {{model}})": "הגדר מודל דירוג מחדש (למשל {{model}})", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "הגדר שלבים", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "ה<PERSON><PERSON><PERSON> קול", "Set whisper model": "", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets how far back for the model to look back to prevent repetition.": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "", "Sets the size of the context window used to generate the next token.": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "הגדרות", "Settings saved successfully!": "ההגדרות נשמרו בהצלחה!", "Share": "שתף", "Share Chat": "שתף צ'אט", "Share to Open WebUI Community": "שתף לקהילת OpenWebUI", "Share your background and interests": "", "Sharing Permissions": "", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "הצג", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "", "Show All": "", "Show Formatting Toolbar": "", "Show image preview": "", "Show Less": "", "Show Model": "", "Show shortcuts": "הצג קיצורי דרך", "Show your support!": "", "Showcased creativity": "הצגת יצירתיות", "Sign in": "הירשם", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "התנתקות", "Sign up": "הרשמה", "Sign up to {{WEBUI_NAME}}": "", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to False.": "", "Signing in to {{WEBUI_NAME}}": "", "Sink List": "", "sk-1234": "", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Something went wrong :/": "", "Sonar": "", "Sonar Deep Research": "", "Sonar Pro": "", "Sonar Reasoning": "", "Sonar Reasoning Pro": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "שגי<PERSON>ת תחקור שמע: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "מנוע תחקור שמע", "Start of the channel": "תחילת הערוץ", "Start Tag": "", "STDOUT/STDERR": "STDOUT/STDERR", "Stop": "", "Stop Generating": "", "Stop Sequence": "סידור עצירה", "Stream Chat Response": "", "Stream Delta Chunk Size": "", "Strikethrough": "", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "", "STT Settings": "הגדרות חקירה של TTS", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "תחקור (לדוגמה: על מעמד הרומי)", "Success": "הצלחה", "Successfully imported {{userCount}} users.": "", "Successfully updated.": "עדכון הצלחה.", "Suggest a change": "", "Suggested": "מומלץ", "Support": "", "Support this plugin:": "", "Supported MIME Types": "", "Sync directory": "", "System": "מערכת", "System Instructions": "", "System Prompt": "תגובת מערכת", "Tags": "", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "", "Talk to model": "דברו עם המודל", "Tap to interrupt": "", "Task List": "", "Task Model": "", "Tasks": "", "Tavily API Key": "", "Tavily Extract Depth": "", "Tell us more:": "תרשמו יותר:", "Temperature": "טמפרטורה", "Temporary Chat": "", "Temporary Chat by Default": "", "Text Splitter": "", "Text-to-Speech": "", "Text-to-Speech Engine": "מנוע טקסט לדיבור", "Thanks for your feedback!": "תודה על המשוב שלך!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The passwords you entered don't quite match. Please double-check and try again.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "ציון צריך להיות ערך בין 0.0 (0%) ל-1.0 (100%)", "The stream delta chunk size for the model. Increasing the chunk size will make the model respond with larger pieces of text at once.": "", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "", "The Weight of BM25 Hybrid Search. 0 more lexical, 1 more semantic. Default 0.5": "", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "נושא", "Thinking...": "חושב...", "This action cannot be undone. Do you wish to continue?": "", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "", "This chat won't appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "פעולה זו מבטיחה שהשיחות בעלות הערך שלך יישמרו באופן מאובטח במסד הנתונים העורפי שלך. תודה!", "This feature is experimental and may be modified or discontinued without notice.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Thorough explanation": "תיאור מפורט", "Thought for {{DURATION}}": "", "Thought for {{DURATION}} seconds": "", "Thought for less than a second": "", "Thread": "שרש<PERSON>ר", "Tika": "", "Tika Server URL required.": "", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "טיפ: עד<PERSON><PERSON> חריצים משתנים מרובים ברציפות על-ידי לחיצה על מקש Tab בקלט הצ'אט לאחר כל החלפה.", "Title": "שם", "Title (e.g. Tell me a fun fact)": "שם (לדוגמה: תרגום)", "Title Auto-Generation": "יצירת שם אוטומטית", "Title cannot be an empty string.": "שם לא יכול להיות מחרוזת ריקה.", "Title Generation": "", "Title Generation Prompt": "פרומפט ליצירת כותרת", "TLS": "", "To access the available model names for downloading,": "כדי לגשת לשמות הדגמים הזמינים להורדה,", "To access the GGUF models available for downloading,": "כדי לגשת לדגמי GGUF הזמינים להורדה,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To learn more about powerful prompt variables, click here": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "", "To select toolkits here, add them to the \"Tools\" workspace first.": "", "Toast notifications for new updates": "", "Today": "היום", "Toggle search": "", "Toggle settings": "החלפת מצב של הגדרות", "Toggle sidebar": "החלפת מצב של סרגל הצד", "Toggle whether current connection is active.": "", "Token": "", "Too verbose": "", "Tool created successfully": "", "Tool deleted successfully": "", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "", "Tool Name": "", "Tool Servers": "", "Tool updated successfully": "", "Tools": "כלים", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "", "Tools Function Calling Prompt": "", "Tools have a function calling system that allows arbitrary code execution.": "", "Tools Public Sharing": "", "Top K": "Top K", "Top K Reranker": "", "Transformers": "", "Trouble accessing Ollama?": "קשה לגשת לOllama?", "Trust Proxy Environment": "", "Try Again": "", "TTS Model": "", "TTS Settings": "הגדרות TTS", "TTS Voice": "", "Type": "סוג", "Type Hugging Face Resolve (Download) URL": "הקלד כתובת URL של פתרון פנים מחבק (הורד)", "Uh-oh! There was an issue with the response.": "", "UI": "", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Underline": "", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "", "Unpin": "", "Unravel secrets": "", "Unsupported file type.": "", "Untagged": "", "Untitled": "", "Update": "", "Update and Copy Link": "ע<PERSON><PERSON><PERSON> ושכ<PERSON>ל קישור", "Update for the latest features and improvements.": "", "Update password": "עד<PERSON><PERSON> סיסמה", "Updated": "", "Updated at": "", "Updated At": "", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "", "Upload": "", "Upload a GGUF model": "העלה מודל GGUF", "Upload Audio": "", "Upload directory": "", "Upload files": "", "Upload Files": "העלאת קבצים", "Upload Pipeline": "", "Upload Progress": "תקדמות העלאה", "Upload Progress: {{uploadedFiles}}/{{totalFiles}} ({{percentage}}%)": "", "URL": "", "URL is required": "", "URL Mode": "מצב URL", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "", "Use groups to group your users and assign permissions.": "", "Use LLM": "", "Use no proxy to fetch page contents.": "", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "user": "משת<PERSON>ש", "User": "", "User Groups": "", "User location successfully retrieved.": "", "User menu": "", "User Webhooks": "", "Username": "", "Users": "משתמשים", "Using Entire Document": "", "Using Focused Retrieval": "", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Valid time units:": "יחידות זמן תקינות:", "Validate certificate": "", "Valves": "", "Valves updated": "", "Valves updated successfully": "", "variable": "משתנה", "Verify Connection": "", "Verify SSL Certificate": "", "Version": "גרסה", "Version {{selectedVersion}} of {{totalVersions}}": "", "View Replies": "", "View Result from **{{NAME}}**": "", "Visibility": "", "Vision": "", "Voice": "", "Voice Input": "", "Voice mode": "", "Warning": "אזהרה", "Warning:": "אזהרה:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "אזהרה: אם תעדכן או תשנה את מודל ההטבעה שלך, יהיה עליך לייבא מחדש את כל המסמכים.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "", "Web": "רשת", "Web API": "", "Web Loader Engine": "", "Web Search": "<PERSON><PERSON><PERSON><PERSON><PERSON> באינטרנט", "Web Search Engine": "מנוע חיפוש באינטרנט", "Web Search in Chat": "", "Web Search Query Generation": "", "Webhook URL": "URL Webhook", "WebUI Settings": "הגדרות WebUI", "WebUI URL": "", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "What are you trying to achieve?": "", "What are you working on?": "", "What's New in": "מה חדש ב", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "", "Why?": "", "Widescreen Mode": "", "Width": "", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "", "Workspace": "סביבה", "Workspace Permissions": "", "Write": "", "Write a prompt suggestion (e.g. Who are you?)": "כתוב הצעה מהירה (למשל, מי אתה?)", "Write a summary in 50 words that summarizes [topic or keyword].": "כתוב סיכום ב-50 מילים שמסכם [נושא או מילת מפתח].", "Write something...": "", "Write your model system prompt content here\ne.g.) You are Mario from Super Mario Bros, acting as an assistant.": "כתבו כאן את תוכן ה-system prompt של המודל שלכם\nלדוגמה: אתם מריו מ־Super Mario Bros ופועלים כעוזר.", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "אתמול", "You": "אתה", "You are currently using a trial license. Please contact support to upgrade your license.": "", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "", "You cannot upload an empty file.": "", "You do not have permission to upload files.": "", "You have no archived conversations.": "אין לך שיחות בארכיון.", "You have shared this chat": "שיתפת את השיחה הזו", "You're a helpful assistant.": "אתה עוזר מועיל.", "You're now logged in.": "כעת אתה מחובר.", "Your Account": "", "Your account status is currently pending activation.": "", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "Youtube", "Youtube Language": "", "Youtube Proxy URL": ""}