{"-1 for no limit, or a positive integer for a specific limit": "-1 war talast, neɣ aɣerwaḍ ilaw i talast tusdidt", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' neɣ '-1' i wakud war tilas.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(amedya. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(amedya `sh webui.sh --api`)", "(latest)": "(<PERSON><PERSON><PERSON>)", "(leave blank for to use commercial endpoint)": "", "[Last] dddd [at] h:mm A": "[<PERSON><PERSON><PERSON><PERSON>] dddd [ɣef] h:mm A", "[Today at] h:mm A": "[Ass-a ɣef] h:mm A", "[Yesterday at] h:mm A": "[<PERSON><PERSON><PERSON>] h:mm A", "{{ models }}": "{{ models }}", "{{COUNT}} Available Tools": "<PERSON><PERSON>an n yifecka i yellan {{COUNT}}", "{{COUNT}} characters": "{{COUNT}} n isekkilen", "{{COUNT}} extracted lines": "", "{{COUNT}} hidden lines": "{{COUNT}} n yizirigen yeffren", "{{COUNT}} Replies": "{{COUNT}} n tririyin", "{{COUNT}} words": "{{COUNT}} n wawalen", "{{model}} download has been canceled": "", "{{user}}'s Chats": "Asqerdec n {{user}}", "{{webUIName}} Backend Required": "", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "<PERSON><PERSON>em amaynut n (v{{LATEST_VERSION}}), yella akka tura.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Tamudemt n temsekra tettuseqdec mi ara tgeḍ timsekra am usirew n yizwal i yidiwenniyen akked tuttriwin n unadi deg web", "a user": "aseqdac", "About": "Ɣef", "Accept autocomplete generation / Jump to prompt variable": "<PERSON>qbal n tsuta tafulmant tummidt / Neggez ɣer usen<PERSON>li n umutti", "Access": "<PERSON><PERSON><PERSON>", "Access Control": "Asenqed n unekcum", "Accessible to all users": "<PERSON><PERSON> i yiseqdacen i meṛṛa", "Account": "<PERSON><PERSON><PERSON><PERSON>", "Account Activation Pending": "<PERSON><PERSON> n umiḍan deg uṛaǧu", "Accurate information": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "Action": "Tigawt", "Action not found": "", "Action Required for Chat Log Storage": "Isefk tigawt i usekles n uɣmis n udiwenni", "Actions": "Tigawin", "Activate": "<PERSON><PERSON>", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Xdem lameṛ-a s tira n \"/{COMMAND}}\" akken ad tqeṣṣreḍ asekcem.", "Active": "<PERSON><PERSON><PERSON>", "Active Users": "Iseq<PERSON><PERSON><PERSON> urmiden", "Add": "Rnu", "Add a model ID": "Rnu asulay n timudemt", "Add a short description about what this model does": "Rnu aglam wezzilen ɣef wayen i txeddem tmudemt-a", "Add a tag": "<PERSON>nu tabzimt", "Add Arena Model": "<PERSON><PERSON> tamudemt Arena", "Add Connection": "<PERSON><PERSON> tuqqna", "Add Content": "<PERSON><PERSON> agbur", "Add content here": "<PERSON><PERSON> agbur da", "Add Custom Parameter": "<PERSON><PERSON>", "Add custom prompt": "<PERSON><PERSON> anef<PERSON> u<PERSON>", "Add Details": "", "Add Files": "<PERSON><PERSON>", "Add Group": "<PERSON><PERSON> agraw", "Add Memory": "<PERSON><PERSON> cfawat", "Add Model": "<PERSON><PERSON> tamude<PERSON>t", "Add Reaction": "<PERSON><PERSON> ta<PERSON>t", "Add Tag": "<PERSON>nu tabzimt", "Add Tags": "<PERSON><PERSON> t<PERSON>", "Add text content": "<PERSON><PERSON> a<PERSON>bur <PERSON>", "Add User": "<PERSON><PERSON>", "Add User Group": "Rnu agraw n iseqdacen", "Additional Config": "", "Additional configuration options for marker. This should be a JSON string with key-value pairs. For example, '{\"key\": \"value\"}'. Supported keys include: disable_links, keep_pageheader_in_output, keep_pagefooter_in_output, filter_blank_pages, drop_repeated_text, layout_coverage_threshold, merge_threshold, height_tolerance, gap_threshold, image_threshold, min_line_length, level_count, default_level": "", "Adjusting these settings will apply changes universally to all users.": "<PERSON><PERSON> n yiɣewwaren-a ad yessuter ibeddilen s wudem ameɣradan i yiseqdacen akk.", "admin": "anedbal", "Admin": "<PERSON><PERSON><PERSON>", "Admin Panel": "Agalis n tedbelt", "Admin Settings": "Iɣewwaṛen n unedbal", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Inedbalen sɛan anekcum ɣer meṛṛa ifecka melmi i bɣan; iseqdacen ilaq ad asen-ttwamudden yifecka i yal tamudemt deg tallunt n umahil.", "Advanced Parameters": "<PERSON><PERSON><PERSON><PERSON><PERSON> le<PERSON>n", "Advanced Params": "<PERSON><PERSON><PERSON><PERSON><PERSON> le<PERSON>n", "AI": "TT", "All": "Akk", "All Documents": "Akk isemliyen", "All models deleted successfully": "Akk timudmiwin ttwakksent akken iwata", "Allow Call": "<PERSON><PERSON>", "Allow Chat Controls": "Sireg isenqaden n usqerdec", "Allow Chat Delete": "Sireg tukksa n yidiwen<PERSON>yen", "Allow Chat Deletion": "Sireg tukksa n uzray n usqerdec", "Allow Chat Edit": "Sireg asenfel n usqerdec", "Allow Chat Export": "Sireg asifeḍ n usqerdec", "Allow Chat Params": "<PERSON><PERSON> iɣ<PERSON>waren n udiwenni", "Allow Chat Share": "<PERSON><PERSON> beṭṭu n usqerdec", "Allow Chat System Prompt": "Sireg aneftaɣ n unagraw n udiwenni", "Allow Chat Valves": "", "Allow Continue Response": "", "Allow Delete Messages": "", "Allow File Upload": "<PERSON><PERSON> asali n yifuyla", "Allow Multiple Models in Chat": "Sireg ugar n timudmiwin deg usqerdec", "Allow non-local voices": "<PERSON><PERSON> tu<PERSON><PERSON> tir<PERSON>", "Allow Rate Response": "", "Allow Regenerate Response": "", "Allow Speech to Text": "Sireg aɛqal n taɣect", "Allow Temporary Chat": "<PERSON><PERSON>q<PERSON> i kra n wakud", "Allow Text to Speech": "<PERSON><PERSON> a<PERSON> ar um<PERSON>lay", "Allow User Location": "Sireg adig n useqdac", "Allow Voice Interruption in Call": "Sireg anegzum n tavuct lawan n usiwel", "Allowed Endpoints": "", "Allowed File Extensions": "Isiɣzaf n ufaylu i yettwasirgen", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "Ɣur-k·m yakan ami<PERSON>an?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "", "Always": "<PERSON><PERSON> tik<PERSON>t", "Always Collapse Code Blocks": "Fneẓ yal tikkelt iḥedren n tengalt", "Always Expand Details": "", "Always Play Notification Sound": "<PERSON><PERSON> yal tikkelt alɣu s <PERSON>ut", "Amazing": "<PERSON><PERSON><PERSON>", "an assistant": "d amallal", "An error occurred while fetching the explanation": "", "Analytics": "<PERSON><PERSON><PERSON><PERSON>", "Analyzed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Analyzing...": "La yettwasekyad…", "and": "akked", "and {{COUNT}} more": "d {{COUNT}} nniḍen", "and create a new shared link.": "rnu ase<PERSON>wen n beṭṭu amaynut.", "Android": "Android", "API": "API", "API Base URL": "", "API Base URL for Datalab Marker service. Defaults to: https://www.datalab.to/api/v1/marker": "", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "Tasarutt API", "API Key created.": "Tasarut API tennulfa-d.", "API Key Endpoint Restrictions": "Agaz n ugaz n tsarut API", "API keys": "Tisura API", "API Version": "Lqem n API", "API Version is required": "", "Application DN": "DN n wesnas", "Application DN Password": "Awal n uɛeddi n DN n usnas", "applies to all users with the \"user\" role": "yeɛna akk iseqdacen yesɛan tamlilt \"user\"", "April": "Yebrir", "Archive": "Seɣ<PERSON>", "Archive All Chats": "Ḥrez akk idiwenniyen", "Archived Chats": "Idiwenniyen i yettwaḥerzen", "archived-chat-export": "asifeḍ n yidiwenniyen i yettwaḥerzen", "Are you sure you want to clear all memories? This action cannot be undone.": "Tetḥe<PERSON>qeḍ tebɣiḍ ad tekkseḍ akk aktayen? Tigawt-a ur tettwakkes ara.", "Are you sure you want to delete this channel?": "Tetḥeqqeḍ tebɣiḍ ad tekkseḍ targa-a?", "Are you sure you want to delete this message?": "Tetḥe<PERSON>qeḍ tebɣiḍ ad tekkseḍ izen-a?", "Are you sure you want to unarchive all archived chats?": "Tetḥeqqemt tebɣamt ad d-tekksemt akk iqecwalen iarkasen?", "Are you sure?": "S tidett?", "Arena Models": "Timudmiwin n Arena", "Artifacts": "Tarkisant", "Ask": "<PERSON><PERSON>", "Ask a question": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "Assistant": "<PERSON><PERSON><PERSON>", "Attach file from knowledge": "Seddu afaylu seg taffa n tmusniwin", "Attention to detail": "", "Attribute for Mail": "", "Attribute for Username": "", "Audio": "<PERSON><PERSON>", "August": "Ɣuct", "Auth": "<PERSON><PERSON><PERSON>", "Authenticate": "<PERSON><PERSON><PERSON>", "Authentication": "<PERSON><PERSON><PERSON>", "Auto": "<PERSON><PERSON><PERSON>", "Auto-Copy Response to Clipboard": "Anɣal n tiririt tawurmant ɣer tecfawt", "Auto-playback response": "Taɣu<PERSON>i tawurmant n tririt", "Autocomplete Generation": "<PERSON><PERSON><PERSON> n yisumar", "Autocomplete Generation Input Max Length": "<PERSON><PERSON><PERSON>t Input Max Length", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "Azrir AUTOMATIC1111 n usesteb n API", "AUTOMATIC1111 Base URL": "", "AUTOMATIC1111 Base URL is required.": "L'URL n uzadur {AUTOMATIC1111} yettwasra.", "Available list": "<PERSON><PERSON><PERSON><PERSON> i yellan", "Available Tools": "<PERSON><PERSON><PERSON> i <PERSON>an", "available users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yellan", "available!": "yella!", "Away": "Ulac", "Awful": "D tawaɣit", "Azure AI Speech": "Azure AI Speech", "Azure OpenAI": "Azure OpenAI", "Azure Region": "Tamnaḍṭ n Azure", "Back": "<PERSON><PERSON><PERSON><PERSON>", "Bad Response": "<PERSON><PERSON> tiri<PERSON>", "Banners": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Base Model (From)": "Tamudemt tazadurt (Seg)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "Umuɣ n uzadur n tebdart Cache yessazzel anekcum s tmudmin tidusanin kan deg ubeddu neɣ deg yiɣewwaren i d-yettwasellken — amestir, maca yezmer lḥal ur d-yesskan ara ibeddilen ineggura n tmudemt azadur.", "Bearer": "", "before": "send", "Being lazy": "Ili-k d ameɛdaz", "Beta": "Bi<PERSON>a", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "Tasarut n umulteɣ Bing Search V7", "Bio": "", "Birth Date": "", "BM25 Weight": "", "Bocha Search API Key": "Tasarut API n Bocha Search", "Bold": "Azuran", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "Aserkem neɣ angal n yiqenṭaren ulmisen i tririt yettwaḥeṛsen. Azalen ibirusanen ad ttwakecfen gar 100 d 100 (asekcam). Lmut: ala", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "Tasarut API n Brave Search", "Bullet List": "<PERSON><PERSON><PERSON><PERSON> s tlilac", "Button ID": "", "Button Label": "", "Button Prompt": "", "By {{name}}": "<PERSON>-<PERSON><PERSON> {{name}}", "Bypass Embedding and Retrieval": "<PERSON><PERSON> ajmak akked tririt", "Bypass Web Loader": "<PERSON><PERSON>", "Cache Base Model List": "Ffer tabdart n tmudmiwin n taffa", "Calendar": "<PERSON><PERSON><PERSON><PERSON>", "Call": "<PERSON><PERSON><PERSON>", "Call feature is not supported when using Web STT engine": "Tam<PERSON>lt n usiwel ur tettwasefrak ara mi ara tesqedceḍ amsedday Web STT", "Camera": "<PERSON><PERSON><PERSON><PERSON>", "Cancel": "<PERSON><PERSON><PERSON>", "Capabilities": "<PERSON><PERSON><PERSON><PERSON>", "Capture": "Tuṭ<PERSON><PERSON>", "Capture Audio": "Asekles n umeslaw", "Certificate Path": "<PERSON><PERSON><PERSON> n <PERSON><PERSON>in", "Change Password": "<PERSON><PERSON><PERSON> awal n uɛeddi", "Channel deleted successfully": "", "Channel Name": "<PERSON><PERSON> n ubadu", "Channel updated successfully": "", "Channels": "Ibuda", "Character": "asekkil", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "Asqerdec", "Chat Background Image": "Tugna n ugilal n udiwenni", "Chat Bubble UI": "Agrudem n tembulin n udiwenni", "Chat Controls": "Isenqaden n udiwenni", "Chat Conversation": "", "Chat direction": "<PERSON><PERSON> n udiwenni", "Chat ID": "", "Chat moved successfully": "", "Chat Overview": "Taskant n udiwenni", "Chat Permissions": "<PERSON><PERSON><PERSON> n yidiwen<PERSON>yen", "Chat Tags Auto-Generation": "<PERSON><PERSON><PERSON> awu<PERSON> n tebzimin", "Chats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Check Again": "<PERSON><PERSON><PERSON> tikelt nni<PERSON>en", "Check for updates": "<PERSON><PERSON>", "Checking for updates...": "<PERSON><PERSON> n y<PERSON>...", "Choose a model before saving...": "Fren tamudemt uqbel ad tsellkeḍ...", "Chunk Overlap": "", "Chunk Size": "", "Ciphers": "<PERSON>z<PERSON><PERSON>", "Citation": "Tanebdurt", "Citations": "<PERSON><PERSON><PERSON><PERSON>", "Clear memory": "<PERSON><PERSON><PERSON>", "Clear Memory": "<PERSON><PERSON><PERSON>", "click here": "sit da", "Click here for filter guides.": "Tekki da i yimniren n tṣeffayt.", "Click here for help.": "Sit da i wawway n tallalt.", "Click here to": "Sit da akken", "Click here to download user import template file.": "Sit da akken ad d-tzedmeḍ afaylu n tmudemt n uketer n iseqdac.", "Click here to learn more about faster-whisper and see the available models.": "Tekki da akken ad tissineḍ ugar ɣef wamek i tettɣawaleḍ ugar, twali<PERSON> timudmin yellan.", "Click here to see available models.": "Sit da akken ad twaliḍ timudmiwin i yellan.", "Click here to select": "Sit da akken ad tferneḍ", "Click here to select a csv file.": "Sit da akken ad tferneḍ afaylu csv.", "Click here to select a py file.": "Sit da akken ad tferneḍ afaylu n py.", "Click here to upload a workflow.json file.": "Sit da akken ad tzedmeḍ afaylu workflow.json.", "click here.": "sit da.", "Click on the user role button to change a user's role.": "<PERSON><PERSON><PERSON> ɣef tqeffalt n temlilt n useqdac akken ad tbeddleḍ tamlilt n useqdac.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Yett<PERSON>g<PERSON> unekcum ɣer tira. <PERSON><PERSON><PERSON>ed iɣewwaṛen-ik n yiminig ma ulac aɣilif akken ad tkecmeḍ ɣer wayen ilaqen.", "Clone": "Sleg", "Clone Chat": "<PERSON><PERSON> adi<PERSON>ni", "Clone of {{TITLE}}": "Aslag n {{TITLE}}", "Close": "<PERSON><PERSON>", "Close Banner": "", "Close Configure Connection Modal": "<PERSON><PERSON> as<PERSON><PERSON>u n tawila n tuqqna", "Close modal": "<PERSON><PERSON>", "Close settings modal": "<PERSON><PERSON> as<PERSON> n iɣew<PERSON>en", "Close Sidebar": "<PERSON><PERSON> agalis adisan", "CMU ARCTIC speaker embedding name": "", "Code Block": "<PERSON><PERSON><PERSON> n tengalt", "Code execution": "Aselkem n tengalt", "Code Execution": "Aselkem n tengalt", "Code Execution Engine": "Amsadday n uselkem n tengalt", "Code Execution Timeout": "Tanzagt n uselkem n tengalt, tɛedda", "Code formatted successfully": "Tangalt tettwamsel akken iwata", "Code Interpreter": "Asegzay n tengalt", "Code Interpreter Engine": "Amsedday n usegzay n tengalt", "Code Interpreter Prompt Template": "Tamudemt n uneftaɣ n usegzay n tengalt", "Collapse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Collection": "Tag<PERSON><PERSON>", "Color": "Ini", "ComfyUI": "ComfyUI", "ComfyUI API Key": "Tasarut API n ComfyUI", "ComfyUI Base URL": "URL n uzadur n ComfyUI", "ComfyUI Base URL is required.": "URL n taffa ComfyUI, yettwasra.", "ComfyUI Workflow": "<PERSON><PERSON><PERSON> n umahil n ComfyUI", "ComfyUI Workflow Nodes": "Taneddict n usuddem n umahil n ComfyUI", "Comma separated Node Ids (e.g. 1 or 1,2)": "", "Command": "<PERSON><PERSON><PERSON><PERSON>", "Comment": "<PERSON><PERSON><PERSON><PERSON>", "Completions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Compress Images in Channels": "Skussem tugniwin deg Yibuda", "Concurrent Requests": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "Config imported successfully": "", "Configure": "Swel", "Confirm": "Sentem", "Confirm Password": "Sentem awal n uɛeddi", "Confirm your action": "<PERSON><PERSON> tigawt-a", "Confirm your new password": "Sentem awal-ik·im n uɛeddi amaynut", "Confirm Your Password": "<PERSON><PERSON> awal-ik⋅im uffir", "Connect to your own OpenAI compatible API endpoints.": "<PERSON><PERSON><PERSON>er wagazen-ik n taggara n API yemṣaban OpenAI.", "Connect to your own OpenAPI compatible external tool servers.": "<PERSON><PERSON><PERSON> ɣer yiqedd<PERSON>n-ik n yifecka imeṛ<PERSON>a yeldin.", "Connection failed": "<PERSON><PERSON><PERSON><PERSON> d-tawe<PERSON><PERSON><PERSON>", "Connection successful": "<PERSON><PERSON><PERSON><PERSON> tedda akken iwata", "Connection Type": "Anaw n tuqqna", "Connections": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Connections saved successfully": "<PERSON><PERSON><PERSON><PERSON><PERSON> ttwas<PERSON><PERSON> akken iwata", "Connections settings updated": "Iɣewwaren n tuqqna ttwaleqqmen", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "Yessefk ad tḥeṛ<PERSON>ḍ ussis ɣef ssebba n tmudmin n usseɣẓen. D ayen izemren kan ad yesseɣẓen timudmin seg yisaǧǧawen ulmisen i yettɛawanen ussisen n usseɣẓen.", "Contact Admin for WebUI Access": "Admin n unermis i WebUI", "Content": "<PERSON><PERSON><PERSON>", "Content Extraction Engine": "Amsedday n uselkem n ugbur", "Continue Response": "<PERSON><PERSON><PERSON> tiririt", "Continue with {{provider}}": "Kemmel s {{provider}}", "Continue with Email": "<PERSON><PERSON><PERSON> s yimayl", "Continue with LDAP": "Kemmel s LDAP", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "Ssenqed amek ara tɛawdeḍ ad d-tɛawdeḍ tiseddarin n usekkil deg uḍris-nni i d-yettwalalen. <PERSON><PERSON> (amedya, 1.5) ad iɛawed ad iɛawed ad iɛawed ad iɛeddi s lǧehd, ma d azal n wadda (am<PERSON><PERSON>, 1.1) ad yili d win yettɛeddin ugar. Ɣef 1, d ameɛdur.", "Controls": "Isenqaden", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "Ssneqdet amnekni garkoerenza d tanḍa n tuffɣa. <PERSON><PERSON> n wadda ad d-yeglu s wugar n tmuɣli d u<PERSON>ris udwis.", "Conversation saved successfully": "", "Copied": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Copied link to clipboard": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>t", "Copied shared chat URL to clipboard!": "Nɣel tansa URL n udiwenni yebḍan ɣef afus!", "Copied to clipboard": "<PERSON><PERSON><PERSON><PERSON><PERSON> u<PERSON>", "Copy": "<PERSON><PERSON><PERSON>", "Copy Formatted Text": "<PERSON><PERSON><PERSON> a<PERSON>a", "Copy last code block": "<PERSON><PERSON><PERSON> iḥder n tengalt aneggaru", "Copy last response": "<PERSON><PERSON><PERSON> tiririt tan<PERSON>ut", "Copy link": "<PERSON><PERSON><PERSON>", "Copy Link": "<PERSON><PERSON><PERSON>", "Copy to clipboard": "<PERSON><PERSON><PERSON>", "Copying to clipboard was successful!": "Yes<PERSON><PERSON><PERSON> unɣel ɣer tfelwit n uklip!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "CORS ilaq ad yeswel akken iwata sɣur usaǧǧaw akken ad yeǧǧ issutren seg WebUI yeldin.", "Create": "Snulfu-d", "Create a knowledge base": "Rnu taffa n tmuss<PERSON>win", "Create a model": "Snu<PERSON><PERSON>-d tamudemt", "Create Account": "<PERSON><PERSON><PERSON><PERSON>-<PERSON> ami<PERSON>an", "Create Admin Account": "Snulfu-d ami<PERSON>an n unedbal", "Create Channel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> abadu", "Create Folder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "Create Group": "Snu<PERSON><PERSON>-d a<PERSON>w", "Create Knowledge": "Snulf<PERSON>-<PERSON> ta<PERSON><PERSON>", "Create new key": "Snulfu-d tasarut tamaynut", "Create new secret key": "Snulfu-d tasarut tuffirt tamaynut", "Create Note": "Snulfu-d tazmilt", "Create your first note by clicking on the plus button below.": "<PERSON><PERSON> tazmilt-ik<PERSON>im tamezwarut s usiti ɣef tqeffalt ddaw.", "Created at": "<PERSON><PERSON><PERSON><PERSON> di", "Created At": "<PERSON><PERSON><PERSON><PERSON> di", "Created by": "<PERSON><PERSON>-<PERSON>", "CSV Import": "Kter CSV", "Ctrl+Enter to Send": "Ctrl+Enter i tuzna", "Current Model": "<PERSON><PERSON><PERSON><PERSON> tamir<PERSON>", "Current Password": "Awal n uɛeddi amiran", "Custom": "<PERSON><PERSON><PERSON><PERSON>", "Custom description enabled": "Aglam u<PERSON><PERSON><PERSON> yettwarmed", "Custom Parameter Name": "<PERSON><PERSON> n uɣ<PERSON> ud<PERSON>", "Custom Parameter Value": "<PERSON><PERSON> n uɣew<PERSON> ud<PERSON>wan", "Danger Zone": "Tamnaḍt i iweɛren", "Dark": "Aberkan", "Database": "Taffa n isefka", "Datalab Marker API": "API n Datalab Marker", "Datalab Marker API Key required.": "API n isefkalab Marker Tesri tasarut.", "DD/MM/YYYY": "JJ/MM/AAAA", "December": "Duǧambeṛ", "Deepgram": "", "Default": "<PERSON><PERSON><PERSON>", "Default (Open AI)": "<PERSON><PERSON><PERSON> (Open AI)", "Default (SentenceTransformers)": "<PERSON> wudem <PERSON> (SentenceTransformers)", "Default action buttons will be used.": "Tiqeffalin n tigawt tamezwart, ad tettwseqdac.", "Default description enabled": "<PERSON><PERSON><PERSON> am<PERSON> yermed", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "", "Default Model": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "Default model updated": "<PERSON><PERSON><PERSON><PERSON>, tettwaleqqem", "Default Models": "<PERSON><PERSON><PERSON><PERSON>", "Default permissions": "<PERSON><PERSON><PERSON>", "Default permissions updated successfully": "<PERSON><PERSON><PERSON> ttwaleqqment akken iwata", "Default Prompt Suggestions": "<PERSON><PERSON><PERSON> n yinefta<PERSON>en s wudem amezwar", "Default to 389 or 636 if TLS is enabled": "Amezwer 389 neɣ 636 ma yella TLS yettwarmed", "Default to ALL": "S wudem amezwar i meṛṛa", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "Ma nmuqel amek ara nɛawed ad nɛawed ad nɛawed ad nɛawed ad d-nekkes ayen yesɛan azal d wayen icudden ɣer ugbur, ilaq-as i tuget n tegnatin.", "Default User Role": "<PERSON><PERSON><PERSON> n useqdac am<PERSON>", "Delete": "Kkes", "Delete a model": "Kkes tamudemt", "Delete All Chats": "Kkes akk idiwenniyen", "Delete All Models": "Kkes akk timudmiwin", "Delete chat": "Kkes asqerd<PERSON>", "Delete Chat": "Kkes asqerd<PERSON>", "Delete chat?": "Kkes asqerdec?", "Delete folder?": "<PERSON><PERSON> aka<PERSON>?", "Delete function?": "Kkes tasɣent?", "Delete Message": "Kkes izen", "Delete message?": "Kkes izen?", "Delete note?": "Kkes tazmilt?", "Delete prompt?": "Kkes aneftaɣ?", "delete this link": "k<PERSON> aseɣwen-a", "Delete tool?": "<PERSON><PERSON> afecku?", "Delete User": "<PERSON><PERSON>", "Deleted {{deleteModelTag}}": "Yettwakkes {{deleteModelTag}}", "Deleted {{name}}": "Yettwakkes {{name}}", "Deleted User": "Yettwakkes useqdac", "Deployment names are required for Azure OpenAI": "Ismawen n usleɣmu laqen i Azure OpenAIAI", "Describe Pictures in Documents": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> deg yise<PERSON>n", "Describe your knowledge base and objectives": "<PERSON><PERSON><PERSON>d a<PERSON><PERSON>-nwen n tmussni d yiswan-nwen", "Description": "Aglam", "Detect Artifacts Automatically": "", "Dictate": "Zubet", "Didn't fully follow instructions": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> ara akk i<PERSON>en", "Direct": "<PERSON><PERSON>", "Direct Connections": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "<PERSON><PERSON><PERSON><PERSON><PERSON> tusridin ttaǧǧant iseqdacen ad qqnen ɣer wagazen-nsen n taggara API imṣadan OpenAI.", "Direct Tool Servers": "Iqeddacen n ifecka usriden", "Directory selection was cancelled": "", "Disable Code Interpreter": "Sens asegza<PERSON> n tengalt", "Disable Image Extraction": "<PERSON>s a<PERSON> n tug<PERSON>win", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "Kkes-d asufeɣ n tugna seg PDF. Ma yella aseqdec n LLM yermed, tug<PERSON><PERSON> ad ttwakelsent s wudem awurman. Imezwura ɣer False.", "Disabled": "<PERSON>nsa", "Discover a function": "Af-d tas<PERSON>ent", "Discover a model": "<PERSON><PERSON><PERSON> tamude<PERSON>t", "Discover a prompt": "Snirem anef<PERSON>ɣ", "Discover a tool": "Snirem kra n ufecku", "Discover how to use Open WebUI and seek support from the community.": "Wali amek ara tesqedceḍ Open WebUI udiɣ amek ara ad tnadiḍ tallelt ar temɣiwant.", "Discover wonders": "<PERSON><PERSON><PERSON>", "Discover, download, and explore custom functions": "Af-d, zdem-d, tes<PERSON><PERSON><PERSON> tisɣunin yemganen", "Discover, download, and explore custom prompts": "Af-d, zdem-d, tesnirmeḍ-d ineftaɣen ud<PERSON>wanen", "Discover, download, and explore custom tools": "Af-d, zdem-d, tes<PERSON><PERSON><PERSON> if<PERSON>a ud<PERSON>en", "Discover, download, and explore model presets": "Af-d, zdem-d, tesnirmeḍ-d iferdisen n tmudemt", "Display": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Display Emoji in Call": "Sken imujitin lawan n usiwel", "Display Multi-model Responses in Tabs": "Sken tririyin n waget-tmud<PERSON>win deg waccaren", "Display the username instead of You in the Chat": "Sken isem n useqdac deg wadeg n \"Kečč⋅Kemm\" deg yidiwenniyen", "Displays citations in the response": "<PERSON><PERSON> tibdarin deg tririt", "Dive into knowledge": "Kcem daxel n tmussniwin", "Do not install functions from sources you do not fully trust.": "Ur sebdad ara tisɣunin seg iɣbula ur tettamneḍ ara akken iwata.", "Do not install tools from sources you do not fully trust.": "Ur srusuy ara ifecka seg iɣbula ur tettamneḍ ara akken iwata.", "Docling": "<PERSON><PERSON>", "Docling Server URL required.": "Tansa URL n uqeddac tuḥwaǧ.", "Document": "<PERSON><PERSON><PERSON>", "Document Intelligence": "Tigzi n tsemlit", "Document Intelligence endpoint required.": "", "Documentation": "<PERSON><PERSON><PERSON><PERSON>", "Documents": "Isemliyen", "does not make any external connections, and your data stays securely on your locally hosted server.": "ur teqqen ara ɣer tuqq<PERSON><PERSON> tiz<PERSON>n, akka isefka-k⋅m ad qqimen d iɣellsanen ɣef uqeddac-ik⋅im adigan.", "Domain Filter List": "Tabdart n taɣulin ara yettwasizedgen", "don't fetch random pipelines from sources you don't trust.": "ur d-awi ara pipelines igacuranen seg yiɣbula ur tettamneḍ ara.", "Don't have an account?": "Ur tesɛiḍ ara amiḍan?", "don't install random functions from sources you don't trust.": "ur sebdad ara timahilin tiga<PERSON>ranin i d-yekkan seg yiɣbula ur tettamneḍ ara.", "don't install random tools from sources you don't trust.": "ur srusuy ara ifecka igacuranen seg yiɣbula ur tettamneḍ ara.", "Don't like the style": "<PERSON>r t<PERSON><PERSON><PERSON><PERSON><PERSON> ara a<PERSON>", "Done": "Immed", "Download": "Zdem", "Download & Delete": "Zdem & Kkes", "Download as SVG": "Zdem am SVG", "Download canceled": "Ittwasefsex usider", "Download Database": "Zdem-d tafa n isefka", "Drag and drop a file to upload or select a file to view": "<PERSON><PERSON><PERSON><PERSON> sakin ssers afaylu akken ad saliḍṭ neɣ fren afaylu akken ad twaliḍṭ", "Draw": "<PERSON><PERSON>", "Drop any files here to upload": "Ssers-d if<PERSON><PERSON> da akken ad ten-tessaliḍ", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "e.g. '30s','10m'. <PERSON><PERSON><PERSON><PERSON> n wakud ameɣtu d 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "amedya: \"json\" neɣ azenziɣ JSON", "e.g. 60": "amedya. 60", "e.g. A filter to remove profanity from text": "e.g. Imzizdig akken ad yekkes tukksa n sser seg uḍris", "e.g. en": "am<PERSON>ya kab", "e.g. My Filter": "amedya Imsizdeg-iw", "e.g. My Tools": "<PERSON><PERSON><PERSON>u", "e.g. my_filter": "amedya amsizdeg_iw", "e.g. my_tools": "am<PERSON>ya if<PERSON>_inu", "e.g. pdf, docx, txt": "amedya: pdf, docx, txt", "e.g. Tools for performing various operations": "g. <PERSON><PERSON> i usexdem n tigawin yem<PERSON>den", "e.g., 3, 4, 5 (leave blank for default)": "e.g., 3, 4, 5 (iɣes n temtunt d ilem i tazwara)", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "amedya, audio/wav,audio/mpeg,video/* (anef-as d ilem i yime<PERSON>)", "e.g., en-US,ja-JP (leave blank for auto-detect)": "e.g", "e.g., westus (leave blank for eastus)": "lɣe<PERSON>b, lɣeṛb (iɣes n temtunt ) ilem i cceṛq", "Edit": "Ẓreg", "Edit Arena Model": "Ẓreg tamudemt n Arena", "Edit Channel": "Ẓreg abadu", "Edit Connection": "Ẓreg tuqqna", "Edit Default Permissions": "<PERSON><PERSON><PERSON> tisi<PERSON>", "Edit Folder": "Ẓreg akaram", "Edit Memory": "Ẓreg takatut", "Edit User": "Ẓreg aseqdac", "Edit User Group": "Ẓreg agraw n iseqdacen", "Edited": "Yettwaẓrag", "Editing": "Asiẓreg", "Eject": "Ḍeqqer-d", "ElevenLabs": "ElevenLabs", "Email": "<PERSON><PERSON><PERSON>", "Embark on adventures": "Kcem deg tmseksalin", "Embedding": "<PERSON><PERSON><PERSON><PERSON>", "Embedding Batch Size": "Teɣzi n tesmelt n ujmak", "Embedding Model": "Tamudemt n ujmak", "Embedding Model Engine": "Amsedday n tmudemt n ujmak", "Embedding model set to \"{{embedding_model}}\"": "Tettusbadu tmudemt n ujmak ɣef \"{{embedding_model}}\"\"", "Enable API Key": "<PERSON>med tasarut <PERSON>", "Enable autocomplete generation for chat messages": "<PERSON><PERSON> tasuta tawurmant tummidt i udiwenni iznan", "Enable Code Execution": "Rmed as<PERSON> n tengalt", "Enable Code Interpreter": "Rmed <PERSON> n tengalt", "Enable Community Sharing": "<PERSON><PERSON> be<PERSON>u n temɣiwent", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Rmed Mapping (map) n usali n yisefka n tmudemt. Tifrat-a ad teǧǧ anagraw ad yesseqdec asigez n uḍebsi d asiɣzef n RAM s udawi n yifuyla n uḍebsi amzun deg RAM i llan. Aya yezmer ad yesnerni aswir n tmudemt s usireg n unekcum ɣer yisefka arurad ugar. D acu kan, yezmer lḥal ur yetteddu ara akken iwata s yinagrawen akk yernu yezmer ad yečč aṭas n tallunt n uḍebsi.", "Enable Message Rating": "Rmed aktazal n yiznan", "Enable Mirostat sampling for controlling perplexity.": "Rmed askar n Mirostat akken ad tḥekmed deg lbaṭel.", "Enable New Sign Ups": "<PERSON><PERSON>", "Enable, disable, or customize the reasoning tags used by the model. \"Enabled\" uses default tags, \"Disabled\" turns off reasoning tags, and \"Custom\" lets you specify your own start and end tags.": "", "Enabled": "D urmid", "End Tag": "", "Endpoint URL": "URL n wagaz n uzgu", "Enforce Temporary Chat": "Ḥettem idiwenniyen iskudanen", "Enhance": "Yesnernay", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "<PERSON>sefqed afaylu-inek CSV deg-s 4 n tgejda deg uswir-a: <PERSON><PERSON>, <PERSON><PERSON>, awal uffir, Role.", "Enter {{role}} message here": "Sekcem izen n {{role}} dagi", "Enter a detail about yourself for your LLMs to recall": "Ssekcem-d ttfa<PERSON>il ɣef yiman-nnek akken ad d-temmektid LLMs-nnek akken ad d-temmektid", "Enter a title for the pending user info overlay. Leave empty for default.": "Sekcem azwel i ugrudem n useqdac la yettraǧun. Eǧǧ-it d ilem i umezwar.", "Enter a watermark for the response. Leave empty for none.": "Sekcem ticreḍt tafrawant i tririt. Eǧǧ-it d ilem i wulac.", "Enter api auth string (e.g. username:password)": "Sekcem azrir n usesteb n api (amedya. isem n useqdac:awal n uɛeddi)", "Enter Application DN": "Sekcem DN n usnas", "Enter Application DN Password": "Sekcem awal uffir n usnas DN", "Enter Bing Search V7 Endpoint": "Sekcem agaz n taggara n Bing Search V7", "Enter Bing Search V7 Subscription Key": "Sekcem tasarut n umultaɣ Bing Search V7", "Enter Bocha Search API Key": "Sekcem tasarut API n Bocha Search", "Enter Brave Search API Key": "Sekcem-d tasarut n API Brave Search", "Enter certificate path": "Sekcem abrid n <PERSON>lkin", "Enter CFG Scale (e.g. 7.0)": "Kcem ɣer CFG Scale (amedya 7.0)", "Enter Chunk Overlap": "Sekcem ambiwel n yifendasen", "Enter Chunk Size": "Sekcem teɣzi n yife<PERSON>en", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "<PERSON><PERSON><PERSON> ɣer tyugiwin \"token:bias_value\" i d-yezgan gar-asent (amedya: 5432:100, 413:-100)", "Enter Config in JSON format": "<PERSON><PERSON>m ɣer Config s umasal JSON", "Enter content for the pending user info overlay. Leave empty for default.": "Sekcem agbur n telɣut n useqdac yett<PERSON>ṛjan. Eǧǧ ilem i tazwara.", "Enter coordinates (e.g. 51.505, -0.09)": "", "Enter Datalab Marker API Base URL": "", "Enter Datalab Marker API Key": "Sekcem API n isefkalab Marker Tasarut", "Enter description": "Sekcem aglam", "Enter Docling OCR Engine": "Kcem ɣer umsedday n OCR", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "Sekcem tansa URL n uqeddac", "Enter Document Intelligence Endpoint": "Sekcem agaz n tagara n yisemli", "Enter Document Intelligence Key": "<PERSON><PERSON><PERSON> ɣer tsarut n wulli", "Enter domains separated by commas (e.g., example.com,site.org)": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON> yeb<PERSON>an s tefrayin (amedya: example.com,site.org)", "Enter Exa API Key": "<PERSON><PERSON><PERSON> ɣer <PERSON>sar<PERSON>", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "Sekcem tansa URL n ukaram n isemli imeṛṛa", "Enter External Web Loader API Key": "Sekcem API n isebtar web imeṛṛa Tasarut", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "Sekcem-d tasarut API n unadi teffɣen ɣef Web", "Enter External Web Search URL": "Sekcem-d tansa URL yeffɣen n unadi ɣef Web", "Enter Firecrawl API Base URL": "Sekcem tansa URL n taffa n isefka API", "Enter Firecrawl API Key": "Sekcem API n Firecrawl Tasarut", "Enter folder name": "Sekcem isem n ukaram", "Enter Github Raw URL": "Sekcem tansa URL tazegzawt n Github", "Enter Google PSE API Key": "Sekcem tasarut API n Google PSE", "Enter Google PSE Engine Id": "Sekcem Google PSE Engine Id", "Enter hex color (e.g. #FF0000)": "", "Enter ID": "", "Enter Image Size (e.g. 512x512)": "<PERSON><PERSON><PERSON><PERSON> (amedya 512x512)", "Enter Jina API Key": "Sekcem tasarut API n Jina", "Enter JSON config (e.g., {\"disable_links\": true})": "", "Enter Jupyter Password": "Sekcem-d awal n uɛeddi n <PERSON>", "Enter Jupyter Token": "Sek<PERSON>m<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> n <PERSON>", "Enter Jupyter URL": "Sekcem URL n Jupyter", "Enter Kagi Search API Key": "Sekcem-d tasarut API n Kagi Search", "Enter Key Behavior": "<PERSON><PERSON><PERSON>er tsar<PERSON>", "Enter language codes": "Sekcem-d tangalin n tutlayin", "Enter Mistral API Key": "Enter Mistral API Tasarut", "Enter Model ID": "Senkcem-d asulay n tmudemt", "Enter model tag (e.g. {{modelTag}})": "Sekcem tabzimt n tmudemt (amedya. {{modelTag}})", "Enter Mojeek Search API Key": "Sekcem-d tasarut API n Mojeek Search", "Enter name": "Sekcem-d isem", "Enter New Password": "Sekcem-d awal n uɛeddi amaynut", "Enter Number of Steps (e.g. 50)": "<PERSON><PERSON><PERSON><PERSON> uṭṭun n yisurifen (amedya 50)", "Enter Perplexity API Key": "Sekcem-d tasarut API n Perplexity", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "Sekcem-d URL n Playwright WebSocket", "Enter proxy URL (e.g. **************************:port)": "Sekcem URL apṛuksi (amedya. **************************:port)", "Enter reasoning effort": "Sekcem ussis n uẓeɣẓen", "Enter Sampler (e.g. Euler a)": "<PERSON><PERSON><PERSON><PERSON> (amedya: Euler a)", "Enter Scheduler (e.g. Karras)": "<PERSON><PERSON><PERSON> (amedya. Ka<PERSON>)", "Enter Score": "<PERSON>k<PERSON>m agmuḍ-ik⋅im", "Enter SearchApi API Key": "Sekcem-d tasarut API n SearchApi", "Enter SearchApi Engine": "Sekcem-d amsadday SearchApi", "Enter Searxng Query URL": "Sekcem tansa URL n Searxng Query", "Enter Seed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>d", "Enter SerpApi API Key": "Kcem ar Serp Tasarut API", "Enter SerpApi Engine": "Sekcem-d amsedday n SerpApi", "Enter Serper API Key": "Sekcem API n uqeddac Tasarut", "Enter Serply API Key": "Sekcem API Tasarut", "Enter Serpstack API Key": "Sekcem tasarut API n Serpstack", "Enter server host": "Sekcem asenneftaɣ n uqeddac", "Enter server label": "Sekcem tabzimt n uqeddac", "Enter server port": "Sekcem-d tawwurt n uqeddac", "Enter Sougou Search API sID": "Sekcem-d asulay sID n API n unadi n Sougou", "Enter Sougou Search API SK": "Sekcem-d SK n API n unadi n Sougou", "Enter stop sequence": "Sekcem tagzemt n uneḥbus", "Enter system prompt": "Sekcem-d aneftaɣ n unagraw", "Enter system prompt here": "Sekcem-d aneftaɣ n unagraw da", "Enter Tavily API Key": "Sekcem API Tavilyant Tasarut", "Enter Tavily Extract Depth": "<PERSON><PERSON><PERSON>", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "Sekcem tansa URL tazayezt n WebUI-inek. Tansa-a URL ad tettwaseqdec i usuffeɣ n iseɣwan deg ilɣa.", "Enter the URL of the function to import": "Sekcem-d tansa URL n tesɣent akken ad tketreḍ", "Enter the URL to import": "Sekcem tansa URL akken ad tketreḍ", "Enter Tika Server URL": "Sekcem tansa URL n uqeddac Tika", "Enter timeout in seconds": "<PERSON><PERSON><PERSON> ɣer wakud deg tsinin", "Enter to Send": "<PERSON> ɣer Enter i tuzna", "Enter Top K": "Sekcem-d azal n Top K", "Enter Top K Reranker": "<PERSON><PERSON><PERSON><PERSON>ranker", "Enter URL (e.g. http://127.0.0.1:7860/)": "Sekcem tansa URL (amedya. http://*********.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Sekcem tansa URL (amedya. http://localhost:11434)", "Enter value": "", "Enter value (true/false)": "", "Enter Yacy Password": "Sekcem-d awal n uɛeddi n Yacy", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "Sekcem tansa URL n Yacy (amedya. http://yacy.example.com:8090)", "Enter Yacy Username": "Sekcem-d isem n useqdac n Yacy", "Enter your code here...": "Sekcem tangalt-ik dagi...", "Enter your current password": "Sekcem awal-ik·im n uɛeddi amiran", "Enter Your Email": "Sekcem-d imayl-ik·im", "Enter Your Full Name": "Sekcem isem n uneftaɣ-ik⋅im", "Enter your gender": "", "Enter your message": "Sek<PERSON>m-d izen-ik⋅im", "Enter your name": "Sekcem-d isem-ik·im", "Enter Your Name": "Sekcem-d isem-ik·im", "Enter your new password": "Sekcem awal-ik·im n uɛeddi amaynut", "Enter Your Password": "Sekcem-d awal-ik·im n uɛeddi", "Enter Your Role": "Sek<PERSON>m tamlilt-ik⋅im", "Enter Your Username": "Sekcem-d isem-ik·im n useqdac", "Enter your webhook URL": "Sekcem tansa URL n webhook-ik", "Error": "Tuccḍa", "ERROR": "TUCCḌA", "Error accessing directory": "", "Error accessing Google Drive: {{error}}": "Tuccḍa Google Drive: {{error}}", "Error accessing media devices.": "Tuccḍa deg unekcum ɣer yibenkan n yiẓeḍwa.", "Error starting recording.": "Tucc<PERSON>a deg beddu n usekles.", "Error unloading model: {{error}}": "<PERSON><PERSON><PERSON>a deg usali n tmudemt: {{error}}", "Error uploading file: {{error}}": "<PERSON><PERSON><PERSON>a deg usali n ufaylu: {{error}}", "Error: A model with the ID '{{modelId}}' already exists. Please select a different ID to proceed.": "", "Error: Model ID cannot be empty. Please enter a valid ID to proceed.": "", "Evaluations": "Iktazalen", "Everyone": "<PERSON>l yiwen", "Exa API Key": "Tasarut API n Exa", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Amedya: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Amedya: AKK", "Example: mail": "Amedya: imayl", "Example: ou=users,dc=foo,dc=example": "Amedya: ou=iseqdacen,dc=foo,dc=amedya", "Example: sAMAccountName or uid or userPrincipalName": "Amedya: sAMAccountName neɣ uid neɣ userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "Ḍḍef-d amḍan n tesɣimin deg turagt-nnek. Ma ul<PERSON> a<PERSON>, n<PERSON><PERSON> akken ad ternuḍ amḍan n yikersiyen.", "Exclude": "Sisled", "Execute code for analysis": "<PERSON><PERSON><PERSON> tangalt i uslaḍ", "Executing **{{NAME}}**...": "Aselkem n **{{NAME}}**…", "Expand": "<PERSON><PERSON><PERSON><PERSON>", "Experimental": "Armitan", "Explain": "<PERSON><PERSON><PERSON>", "Explore the cosmos": "<PERSON><PERSON><PERSON>", "Export": "<PERSON><PERSON><PERSON>", "Export All Archived Chats": "<PERSON><PERSON> akk <PERSON>ts", "Export All Chats (All Users)": "Kter akk idiwenniyen (n yiseqdacen akk)", "Export chat (.json)": "<PERSON><PERSON> (.json)", "Export Chats": "<PERSON><PERSON><PERSON> id<PERSON>n", "Export Config to JSON File": "<PERSON><PERSON><PERSON> u<PERSON>", "Export Functions": "Asifeḍ n tesɣunin", "Export Models": "<PERSON><PERSON><PERSON> timud<PERSON>win", "Export Presets": "<PERSON><PERSON><PERSON> is<PERSON> uz<PERSON>n", "Export Prompt Suggestions": "<PERSON><PERSON><PERSON> isumar n uneftaɣ", "Export Prompts": "<PERSON><PERSON><PERSON> inefta<PERSON>en", "Export to CSV": "<PERSON><PERSON>", "Export Tools": "<PERSON><PERSON><PERSON>", "Export Users": "<PERSON><PERSON><PERSON>", "External": "Azɣaray", "External Document Loader URL required.": "URL n uslay n yisemli azɣ<PERSON>y, yettwasra.", "External Task Model": "Tamudemt n temsekrit tazɣarayt", "External Web Loader API Key": "API n isebtar web imeṛṛa Tasarut", "External Web Loader URL": "Tansa URL tuffiɣt", "External Web Search API Key": "Tasarut API n unadi yeffɣen ɣef Web", "External Web Search URL": "Tansa URL yeffɣen n unadi deg Web", "Fade Effect for Streaming Text": "", "Failed to add file.": "Tecceḍ tmerna n ufaylu.", "Failed to connect to {{URL}} OpenAPI tool server": "Ur yessaweḍ ara ad yeqqen ɣer {{URL}} n uqeddac n yifecka OpenAPI", "Failed to copy link": "Ur yessaweḍ ara ad yessukken aseɣwen", "Failed to create API Key.": "Ur yessaweḍ ara ad d-yesnulfu tasarut API.", "Failed to delete note": "Ur yessaweḍ ara ad yekkes tazmilt", "Failed to extract content from the file: {{error}}": "Ur yessa<PERSON><PERSON> ara ad d-yekkes agbur seg ufaylu: {{error}}", "Failed to extract content from the file.": "Ur yessaweḍ ara ad d-yekkes agbur seg ufaylu-nni.", "Failed to fetch models": "Ur ssawḍent ara ad d-awint timudmin tigennawin", "Failed to generate title": "Ur yessaweḍ ara ad d-yawi azwel", "Failed to load chat preview": "Yecceḍ usali n teskant n udiwenni", "Failed to load file content.": "Ur yessaweḍ ara ad d-yessali agbur n yifuyla.", "Failed to move chat": "", "Failed to read clipboard contents": "Ur yessaweḍ ara ad iɣer agbur n tfelwit", "Failed to save connections": "Yecceḍ uklas n tuqqniwin", "Failed to save conversation": "Yecceḍ uklas n udiwenni", "Failed to save models configuration": "Ur yessaweḍ ara ad d-yessukkes tamudemt n usneftaɣ", "Failed to update settings": "<PERSON><PERSON><PERSON><PERSON> uleqqem n yiɣewwaren", "Failed to upload file.": "Yecceḍ uzdam n ufaylu.", "Features": "<PERSON><PERSON><PERSON>", "Features Permissions": "Tisirag n tmehilin", "February": "<PERSON><PERSON><PERSON>", "Feedback Details": "Talqayt n tamawin", "Feedback History": "Azray n tamawin", "Feedbacks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Feel free to add specific details": "Ur ttkukru ara ad ternuḍ ttfaṣil ulmisen", "Female": "", "File": "<PERSON><PERSON><PERSON><PERSON>", "File added successfully.": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> i<PERSON>.", "File content updated successfully.": "Agbur n ufaylu yettwale<PERSON>qem akken iwata.", "File Mode": "Askar n ufaylu", "File not found.": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> ara u<PERSON>nni.", "File removed successfully.": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>ken iwata.", "File size should not exceed {{maxSize}} MB.": "Tiddi n ufaylu ur ilaq ara ad tɛeddi nnig {{maxSize}} MB.", "File Upload": "<PERSON><PERSON> n ufaylu", "File uploaded successfully": "<PERSON><PERSON><PERSON><PERSON><PERSON>nni yuli akken iwata", "Files": "<PERSON><PERSON><PERSON>", "Filter": "Imsizdeg", "Filter is now globally disabled": "<PERSON><PERSON><PERSON><PERSON> tura d ameɛdur ama<PERSON>", "Filter is now globally enabled": "<PERSON><PERSON><PERSON><PERSON> yettwarmed deg uma<PERSON>al tura", "Filters": "Imsizdigen", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Tban-d tɣenǧawt n uskenjbir Ur yezmir yiwen ad yesseqdec inizjali d avatar. Ma nmuqel tugna n umaɣnu amezwer.", "Firecrawl API Base URL": "Tansa URL n taffa n isefka", "Firecrawl API Key": "Tasarut API n Firecrawl", "Floating Quick Actions": "<PERSON><PERSON><PERSON> tirura<PERSON>", "Focus chat input": "Err asa<PERSON>es ɣer unekcum n udiwenni", "Folder deleted successfully": "Akaram-nni yettwakkes akken iwata", "Folder Name": "Isem n ukaram", "Folder name cannot be empty.": "Is<PERSON> n ukaram ur yezmir ara ad yili d ilem.", "Folder name updated successfully": "Isem n ukaram yettwaleqqem akken iwata", "Folder updated successfully": "<PERSON><PERSON><PERSON> akken iwata", "Follow up": "<PERSON><PERSON><PERSON>ṛ", "Follow Up Generation": "Ḍfer asirew", "Follow Up Generation Prompt": "Ḍfer tiwtilin n tsuta", "Follow-Up Auto-Generation": "<PERSON>ḍ<PERSON><PERSON> n usirew awurman", "Followed instructions perfectly": "<PERSON><PERSON><PERSON><PERSON> iwellihen akken iwatan", "Force OCR": "Ḥettem OCR", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "<PERSON><PERSON> i<PERSON>dan im<PERSON>", "Form": "Tiferkit", "Format Lines": "Izirigen n umasal", "Format the lines in the output. Defaults to False. If set to True, the lines will be formatted to detect inline math and styles.": "", "Format your variables using brackets like this:": "<PERSON><PERSON> timuttiyin-ik⋅im s useqdec n tacciwin am:", "Formatting may be inconsistent from source.": "", "Forwards system user session credentials to authenticate": "Welleh inekcam n tɣimit n useqdac i usesteb", "Full Context Mode": "Askar n usatal aččuran", "Function": "<PERSON><PERSON><PERSON><PERSON>", "Function Calling": "Asiwel n twuriwin", "Function created successfully": "<PERSON><PERSON><PERSON><PERSON> tennulfa-d ak<PERSON> i<PERSON>a", "Function deleted successfully": "Tasɣent tettwakkes akken iwata", "Function Description": "Aglam n tesɣent", "Function ID": "Asulay n tesɣent", "Function imported successfully": "Tawuri tettwaktar akken iwata", "Function is now globally disabled": "<PERSON><PERSON><PERSON> tensa akka s umata", "Function is now globally enabled": "<PERSON><PERSON><PERSON> termed akka s umata", "Function Name": "Isem n tesɣent", "Function updated successfully": "Tas<PERSON>ent tettwaleqqem a<PERSON>ken i<PERSON>a", "Functions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Functions allow arbitrary code execution.": "<PERSON><PERSON><PERSON><PERSON><PERSON> ssirigent aselkem n tengalt tagacurant.", "Functions imported successfully": "Tisɣunin ttwaketren akken iwata", "Gemini": "Gemini", "Gemini API Config": "Tawila n API Gemini", "Gemini API Key is required.": "API Yessefk tsarut.", "Gender": "", "General": "<PERSON><PERSON><PERSON>", "Generate": "<PERSON><PERSON>", "Generate an image": "<PERSON><PERSON>na", "Generate Image": "<PERSON><PERSON>-d tugna", "Generate prompt pair": "<PERSON><PERSON> tayuga n yineftaɣen", "Generating search query": "Asirew n tuttra n unadi", "Generating...": "<PERSON><PERSON><PERSON>…", "Get information on {{name}} in the UI": "A<PERSON>-d til<PERSON>a ɣef {{name}} deg UI", "Get started": "<PERSON>a bdu tura", "Get started with {{WEBUI_NAME}}": "Bdu s {{WEBUI_NAME}}", "Global": "<PERSON><PERSON><PERSON>", "Good Response": "<PERSON><PERSON><PERSON> yelhan", "Google Drive": "Google Drive", "Google PSE API Key": "Tasarut API n Google PSE", "Google PSE Engine Id": "Asulay n umsadday n unadi PSE n Google", "Gravatar": "", "Group": "<PERSON><PERSON><PERSON>", "Group created successfully": "<PERSON><PERSON><PERSON> yennulfa-d akken iwata", "Group deleted successfully": "Agraw yettwakkes akken iwata", "Group Description": "Aglam n ugraw", "Group Name": "Isem n ugraw", "Group updated successfully": "<PERSON><PERSON><PERSON> a<PERSON> iwata", "Groups": "<PERSON><PERSON><PERSON>", "H1": "H1", "H2": "H2", "H3": "H3", "Haptic Feedback": "", "Height": "", "Hello, {{name}}": "Azul a {{name}}", "Help": "<PERSON><PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "<PERSON><PERSON>", "Hex Color - Leave empty for default color": "", "Hide": "<PERSON><PERSON><PERSON><PERSON>", "Hide from Sidebar": "Ffer seg ufeggag n yidis", "Hide Model": "Ffer tamudemt", "High": "", "High Contrast Mode": "", "Home": "<PERSON><PERSON><PERSON>", "Host": "Asneftaɣ", "How can I help you today?": "Amek i zemreɣ ad k·kem-ɛiwneɣ ass-a?", "How would you rate this response?": "Amek ara d-teske<PERSON>ḍ tiririt-a?", "HTML": "HTML", "Hybrid Search": "<PERSON><PERSON>", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Steɛṛfeɣ belli ɣriɣ yerna fehmeɣ d acu i d ssebba n tigawt-iw. Ẓriɣ timijwin ay yeqqnen ɣer usenger n tengalt tazurant yernu sfeqdeɣ tinfulin n uɣbalu-nni.", "ID": "<PERSON><PERSON><PERSON>", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "Sker lḥir", "Image": "Tugna", "Image Compression": "Tussda n tugna", "Image Compression Height": "Askussel n teɣzi n tugna", "Image Compression Width": "Askuss<PERSON> n tehri n tugna", "Image Generation": "<PERSON><PERSON><PERSON> n tugniwin", "Image Generation (Experimental)": "<PERSON><PERSON><PERSON> n tug<PERSON> (d armitan)", "Image Generation Engine": "Amsedday n usirew n tugniwin", "Image Max Compression Size": "<PERSON><PERSON><PERSON> n tugna Max Compression", "Image Max Compression Size height": "Tugna Max Compression Teɣzi", "Image Max Compression Size width": "Tugna Max Compression Teɣzi", "Image Prompt Generation": "<PERSON><PERSON><PERSON> n yinefta<PERSON>en n tugniwin", "Image Prompt Generation Prompt": "Aneftaɣ n usirew n yineftaɣen n tugniwin", "Image Settings": "Iɣewwaṛen n usirew n tugniwin", "Images": "<PERSON><PERSON><PERSON>", "Import": "<PERSON><PERSON>", "Import Chats": "Asifeḍ n yidiwen<PERSON>yen", "Import Config from JSON File": "<PERSON><PERSON>-d <PERSON>fig seg u<PERSON><PERSON>u JSON", "Import From Link": "<PERSON><PERSON> seg <PERSON>", "Import Functions": "<PERSON><PERSON>", "Import Models": "<PERSON><PERSON>", "Import Notes": "<PERSON><PERSON>", "Import Presets": "<PERSON><PERSON> u<PERSON>n", "Import Prompt Suggestions": "<PERSON><PERSON> isumar n uneftaɣ", "Import Prompts": "<PERSON><PERSON>", "Import Tools": "<PERSON><PERSON>", "Important Update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Include": "<PERSON><PERSON><PERSON>", "Include `--api-auth` flag when running stable-diffusion-webui": "Seddu annay `--api-auth` lawan n uselkem n stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "Seddu takbabt ''-api' mi ara teslekmeḍ stable-diffusion-webui", "Includes SharePoint": "Igber SharePoint", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "", "Info": "Talɣut", "Initials": "", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "", "Input": "<PERSON><PERSON><PERSON>", "Input commands": "Tiludna n unekcum", "Input Key (e.g. text, unet_name, steps)": "", "Input Variables": "Timuttiyin n unekcum", "Insert": "<PERSON><PERSON>", "Insert Follow-Up Prompt to Input": "", "Insert Prompt as Rich Text": "<PERSON>serset Prompt am uḍris <PERSON>", "Install from Github URL": "Sebded seg tansa URL n Github", "Instant Auto-Send After Voice Transcription": "<PERSON><PERSON><PERSON> a<PERSON>rman ticki Voice Transcription", "Integration": "<PERSON><PERSON><PERSON>", "Interface": "<PERSON><PERSON><PERSON><PERSON>", "Invalid file content": "Agbur n ufaylu d arame<PERSON>tu", "Invalid file format.": "Amasal n ufaylu d arame<PERSON>.", "Invalid JSON file": "Afaylu JSON arameɣtu", "Invalid JSON format for ComfyUI Workflow.": "", "Invalid JSON format in Additional Config": "Amasal JSON arameɣtu deg <PERSON>teb ni<PERSON>en", "Invalid Tag": "Tabzimt d tarameɣ<PERSON>t", "is typing...": "yet<PERSON><PERSON>…", "Italic": "Uknan", "January": "<PERSON><PERSON><PERSON>", "Jina API Key": "Tasarut API n Jina", "join our Discord for help.": "nadi-d <PERSON>ef tall<PERSON>t-nne<PERSON>.", "JSON": "JSON", "JSON Preview": "Taskant JSON", "July": "<PERSON><PERSON><PERSON>", "June": "<PERSON><PERSON>", "Jupyter Auth": "<PERSON><PERSON><PERSON>", "Jupyter URL": "Tansa URL n Jupyter", "JWT Expiration": "Afakki n JWT", "JWT Token": "Token JWT", "Kagi Search API Key": "Tasarut API n Kagi Search", "Keep Follow-Up Prompts in Chat": "Kemmel tiwsatin n uḍfaṛ-Up deg Chat", "Keep in Sidebar": "Senṭeḍ-it deg ufeggag n yidis", "Key": "<PERSON><PERSON><PERSON>", "Key is required": "", "Keyboard shortcuts": "Inegzumen n unasiw", "Knowledge": "<PERSON><PERSON><PERSON>", "Knowledge Access": "<PERSON><PERSON><PERSON> t<PERSON>", "Knowledge Base": "Taffa n tmusni", "Knowledge created successfully.": "<PERSON><PERSON><PERSON> tennu<PERSON>a-d ak<PERSON> iwata.", "Knowledge deleted successfully.": "Tamussni tettwakkes akken iwata.", "Knowledge Description": "", "Knowledge Name": "", "Knowledge Public Sharing": "Beṭṭu azayaz n tmussniwin", "Knowledge reset successfully.": "Tamussni tettuwennez akken iwata.", "Knowledge updated successfully": "Timussniwin ttwaleqqment akken iwata", "Kokoro.js (Browser)": "Kokoro.js (Iminig)", "Kokoro.js Dtype": "Kokoro.js Dtype", "Label": "Tabzimt", "Landing Page Mode": "Askar n usebter agejdan", "Language": "<PERSON><PERSON><PERSON><PERSON>", "Language Locales": "Iɣewwaren n usideg n tutlayt", "Last Active": "<PERSON><PERSON>", "Last Modified": "<PERSON><PERSON><PERSON>", "Last reply": "<PERSON><PERSON><PERSON> tan<PERSON>", "LDAP": "LDAP", "LDAP server updated": "Aqeddac LDAP, yettwaleqqem", "Leaderboard": "<PERSON><PERSON><PERSON>", "Learn More": "<PERSON><PERSON>", "Learn more about OpenAPI tool servers.": "<PERSON><PERSON> ugar <PERSON> y<PERSON>n n yifecka OpenAPI.", "Leave empty for no compression": "Eǧǧ-it d ilem i wakken ur tettili ara tussda", "Leave empty for unlimited": "Eǧǧ ilem i war tilisa", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{url}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "Eǧǧ-it d ilem akken ad ternuḍ akk timudmiwin neɣ ad tferneḍ timudmiwin tulmisin", "Leave empty to use the default prompt, or enter a custom prompt": "Eǧǧ ilem akken ad tesqedceḍ tawelt-nni tamezwarut, neɣ ad tkecmeḍ ɣer tannumi s tɣawla n tannumi", "Leave model field empty to use the default model.": "Eǧǧ iger n tmudemt d ilem i useqdec n tmudemt tamezwarut.", "lexical": "Am<PERSON><PERSON>", "License": "<PERSON><PERSON><PERSON>", "Lift List": "Tabdart n usali", "Light": "<PERSON><PERSON><PERSON>", "Listening...": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "LLMs yezmer ad yecceḍ. <PERSON><PERSON>ed talɣut yesɛan azal.", "Loader": "<PERSON><PERSON><PERSON>", "Loading Kokoro.js...": "Aêbbi n Kokoro.js…", "Loading...": "<PERSON><PERSON><PERSON><PERSON> n...", "Local": "<PERSON><PERSON>", "Local Task Model": "Tamudemt n temsekrit tadigant", "Location access not allowed": "<PERSON><PERSON><PERSON>er tuddna", "Lost": "<PERSON><PERSON><PERSON>", "Low": "", "LTR": "LTR", "Made by Open WebUI Community": "Texdem-it-id temɣiwant n Open WebUI", "Make password visible in the user interface": "Sken-d awal n uɛeddi deg ugrudem n useqdac", "Make sure to enclose them with": "Ur ttettu ara ad ten-tɣunzuḍ", "Make sure to export a workflow.json file as API format from ComfyUI.": "Ur ttettumt ara ad tessiǧwemt aman n umahil. afaylu json d amasal API seg ComfyUI.", "Male": "", "Manage": "<PERSON><PERSON><PERSON>", "Manage Direct Connections": "<PERSON><PERSON><PERSON> tuq<PERSON><PERSON><PERSON> tus<PERSON>in", "Manage Models": "<PERSON><PERSON><PERSON> t<PERSON>", "Manage Ollama": "<PERSON><PERSON><PERSON>", "Manage Ollama API Connections": "Se<PERSON><PERSON> tuqqniwin API Ollama", "Manage OpenAI API Connections": "Sefrek tuqqniwin API n OpenAI", "Manage Pipelines": "<PERSON><PERSON><PERSON>", "Manage Tool Servers": "<PERSON><PERSON><PERSON> i<PERSON> n ifecka", "Manage your account information.": "", "March": "<PERSON><PERSON><PERSON>", "Markdown": "<PERSON><PERSON>", "Markdown (Header)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Aqerru)", "Max Speakers": "<PERSON><PERSON><PERSON> afellay n wid d-yemm<PERSON><PERSON>en", "Max Upload Count": "<PERSON><PERSON><PERSON> afellay n uzdam", "Max Upload Size": "<PERSON><PERSON><PERSON> tafellayt n uzdam", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Maximum n 3 n tmudmin yezmer ad d-yettwasider seg-a ɣer da. Ttxil-k, ɛ<PERSON><PERSON> tikkelt niḍen ticki.", "May": "<PERSON><PERSON>", "Medium": "", "Memories accessible by LLMs will be shown here.": "Da ara d-banent teḥkayin ara yaweḍ yiwen ɣer LLMs.", "Memory": "Ta<PERSON><PERSON>", "Memory added successfully": "Asmekti yet<PERSON>warna akken iwata", "Memory cleared successfully": "Tettwasfeḍ tkatut akken iwata", "Memory deleted successfully": "Asmekti yettwakkes akken iwata", "Memory updated successfully": "Takatut tettwaleqqem akken iwata", "Merge Responses": "<PERSON><PERSON><PERSON><PERSON> tiri<PERSON>n", "Merged Response": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Message rating should be enabled to use this feature": "A win yufan, tazmilt n yizen ad tettwasireg i useqdec n tmahilt-a", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Izen ara d-tazneḍ mi ara d-tesnulfuḍ aseɣwen-ik ur yettwabḍu ara. Iseqdacen yesɛan tansa URL ad izmiren ad walin adiwenni-nni yettwabḍan.", "Microsoft OneDrive": "Microsoft OneDrive", "Microsoft OneDrive (personal)": "Microsoft OneDrive (udmawan)", "Microsoft OneDrive (work/school)": "Microsoft OneDrive (axeddim/aɣerbaz)", "Mistral OCR": "Mistral OCR", "Mistral OCR API Key required.": "API Mistral OCR Tesri tasarut.", "Model": "Tamudemt", "Model '{{modelName}}' has been successfully downloaded.": "Tettwasider-d tmudemt '{{modelName}}' akken iwata.", "Model '{{modelTag}}' is already in queue for downloading.": "Tamudemt '{{modelTag}}' ha-t-an yakan deg tebdart n usader.", "Model {{modelId}} not found": "Tamudemt {{modelId}} ulac-itt", "Model {{modelName}} is not vision capable": "<PERSON><PERSON>}} ma<PERSON><PERSON><PERSON> d tamuɣli izemren ad tili", "Model {{name}} is now {{status}}": "Tamudemt {{name}} tura {{status}}", "Model {{name}} is now hidden": "Tamudemt {{name}} tettwaffer tura", "Model {{name}} is now visible": "Tamudemt {{name}} tettbin-d tura", "Model accepts file inputs": "<PERSON><PERSON> iq<PERSON><PERSON> inekcamen n yifuyla", "Model accepts image inputs": "<PERSON><PERSON> i<PERSON><PERSON> inekcamen n tugna", "Model can execute code and perform calculations": "Mudell yezmer ad d-yesseḍru tangalt u ad yexdem leḥsabat", "Model can generate images based on text prompts": "<PERSON><PERSON><PERSON> ye<PERSON><PERSON> ad d-yawi tugniwin yebnan ɣef teɣratin n yiḍrisen", "Model can search the web for information": "Tamudemt-a tezmer ad tnadi deg Web ɣef isallen", "Model created successfully!": "<PERSON><PERSON><PERSON>t tennulfa-d akken iwata!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "<PERSON><PERSON><PERSON> n unagraw n wammud iban-d. <PERSON><PERSON> n umudil yettwasra i uleqqem, ur yezmir ara ad ikemmel.", "Model Filtering": "Asizdeg n tmudmiwin", "Model ID": "Asulay n tmudemt", "Model ID is required.": "<PERSON><PERSON>y n timudemt yettwasra.", "Model IDs": "Isulayen n tmudmiwin", "Model Name": "Isem n tmudemt", "Model name already exists, please choose a different one": "", "Model Name is required.": "Isem n timudemt yettwasra.", "Model not selected": "Ur tettwafran ara tmudemt", "Model Params": "Iɣewwaren n timudemt", "Model Permissions": "Tisirag n timudemt", "Model unloaded successfully": "<PERSON><PERSON><PERSON><PERSON> ur d-uli ara akken iwata", "Model updated successfully": "Tamudemt tettwaleqqem akken iwata", "Model(s) do not support file upload": "<PERSON>dd(s) ur s<PERSON><PERSON> ara afaylu usali n ufaylu", "Modelfile Content": "Agbur n ufaylu n timudemt", "Models": "<PERSON><PERSON><PERSON><PERSON>", "Models Access": "<PERSON><PERSON><PERSON> tmu<PERSON>win", "Models configuration saved successfully": "Asneftaɣ n yimudam yettwas<PERSON>sen akken iwata", "Models Public Sharing": "Beṭṭu azayaz n tmudmiwin", "Mojeek Search API Key": "Tasarut API n Mojeek", "more": "ugar", "More": "<PERSON><PERSON>", "More Concise": "", "More Options": "Ugar n textiṛiyin", "Move": "", "Name": "<PERSON><PERSON>", "Name and ID are required, please fill them out": "", "Name your knowledge base": "Mudd isem i taffa-k⋅m n tmussniwin", "Native": "<PERSON><PERSON>", "New Button": "<PERSON><PERSON><PERSON><PERSON><PERSON> tamaynut", "New Chat": "Asqerdec amaynut", "New Folder": "<PERSON><PERSON><PERSON> amay<PERSON>", "New Function": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "New Note": "Tazmilt tamaynut", "New Password": "Awal n uɛeddi amaynut", "New Tool": "<PERSON><PERSON><PERSON><PERSON> amay<PERSON>", "new-channel": "abadu amay<PERSON>", "Next message": "<PERSON><PERSON>", "No chats found": "Ulac <PERSON>", "No chats found for this user.": "<PERSON><PERSON><PERSON> adiwenni i useqdac-a.", "No chats found.": "Ulac kra n usqerdec.", "No content": "<PERSON><PERSON><PERSON>", "No content found": "Ulac agbur yettwafen", "No content found in file.": "Ulac agbur yettwafen sdaxel ufaylu.", "No content to speak": "Ulac ara d-yettwabedren", "No conversation to save": "", "No distance available": "Ulac ameccaq yettwafen", "No feedbacks found": "Ulac awennit i yettwafen", "No file selected": "Ulac afaylu i yettwafernen", "No groups with access, add a group to grant access": "<PERSON>la<PERSON> agraw i yesɛan anekcum, rnu agraw i umuddu n unekcum", "No HTML, CSS, or JavaScript content found.": "Ulac agbur <PERSON>, CSS neɣ JavaScript i d-yufraren.", "No inference engine with management support found": "Ulac amsedday n unalkam s tallelt n usefrek i yettwafen", "No knowledge found": "Ulac tamussni i yettwafen", "No memories to clear": "Ulac aktayen ibanen", "No model IDs": "Ulac asulay n tmudemt", "No models found": "<PERSON>lac tim<PERSON><PERSON>win <PERSON>", "No models selected": "<PERSON><PERSON><PERSON> t<PERSON><PERSON>", "No Notes": "<PERSON>lac tiz<PERSON>lin", "No results": "Ulac <PERSON><PERSON>", "No results found": "Ulac <PERSON><PERSON>", "No search query generated": "Ulac tuttra n unadi yettusirwen", "No source available": "<PERSON><PERSON><PERSON>", "No suggestion prompts": "Ulac isumar n prompt", "No users were found.": "Ulac aqeddac i yettwafen.", "No valves": "", "No valves to update": "", "Node Ids": "", "None": "<PERSON><PERSON> d yiwen", "Not factually correct": "", "Not helpful": "Ur infiɛ ara", "Note deleted successfully": "Tazmilt tettwakkes akken iwata", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "", "Notes": "Tizmilin", "Notification Sound": "<PERSON><PERSON><PERSON> n tilɣa", "Notification Webhook": "Webhook n ulɣu", "Notifications": "Tilɣa", "November": "Wambeṛ", "OAuth ID": "<PERSON><PERSON><PERSON>", "October": "Tubeṛ", "Off": "<PERSON>nsa", "Okay, Let's Go!": "Yerbaḥ, aha yya!", "OLED Dark": "Aberkan OLED", "Ollama": "Ollama", "Ollama API": "API Ollama", "Ollama API settings updated": "Iɣewwaren n API Olama ttwaleqqmen", "Ollama Version": "Lqem n <PERSON>", "On": "Irmed", "OneDrive": "OneDrive", "Only active when \"Paste Large Text as File\" setting is toggled on.": "D urmid kan mi ara d-yili usbadu n \"Paste Great Text as File\".", "Only active when the chat input is in focus and an LLM is generating a response.": "D urmid kan mi ara yerr udraw n udiwenni, ad d-yawi LLM tiririt.", "Only alphanumeric characters and hyphens are allowed": "Ala iwudam ifenyanen d tfenṭazit i yettusirgen", "Only alphanumeric characters and hyphens are allowed in the command string.": "Ala iwudam ifenyanen d tfendiwin i yettusirgen deg uzrar n ukman.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Ala tigrummiwin i izemren ad ttwabeddlent, ad d-snulfunt azadur amaynut n tmussni i ubeddel/ad arraten.", "Only markdown files are allowed": "Ala ifuyla n tuccar i yettusirgen", "Only select users and groups with permission can access": "Ala iseqdacen akked yegrawen yesɛan tisirag i izemren ad kecmen", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Ayhuh! Yettban-d dakken URL-nni ur tṣeḥḥa ara. Ttxil-k, ssefqed snat n tikkal yernu ɛreḍ tikkelt niḍen.", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "Ayhuh! Te<PERSON>ra-d tuccḍa deg tririt-nni yezrin.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Ayhuh! Aql-ik tesseqdaceḍ tarrayt ur yettwas<PERSON>en ara (mazwar kan). <PERSON> <PERSON><PERSON>, mudd-d <PERSON><PERSON> seg uɛrur.", "Open file": "<PERSON><PERSON>", "Open in full screen": "Ldi deg ugdil ačč<PERSON>", "Open modal to configure connection": "Ldi asfaylu akken ad teswel tuqqna", "Open Modal To Manage Floating Quick Actions": "", "Open new chat": "Ldi asqerdec amaynut", "Open Sidebar": "<PERSON><PERSON> agalis adisan", "Open User Profile Menu": "Ldi umuɣ n umaɣnu n useqdac", "Open WebUI can use tools provided by any OpenAPI server.": "Open WebUI yezmer ad yesseqdec ifecka i d-yettak yal aqeddac OpenAPI.", "Open WebUI uses faster-whisper internally.": "Open WebUI yesseqdac faster-whisper sdaxel-is.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Ldi WebUI yesseqdac SpeechT5 akked CMU Arktik.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "", "OpenAI": "OpenAI", "OpenAI API": "API OpenAI", "OpenAI API Config": "Tawila n API OpenAI", "OpenAI API Key is required.": "Tasarut API n OpenAI tettwasra.", "OpenAI API settings updated": "Iɣewwaṛen n API OpenAI", "OpenAI URL/Key required.": "URL/Tasarut OpenAI tettwasra.", "openapi.json URL or Path": "", "Optional": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "neɣ", "Ordered List": "Tabdart n usmizwer", "Organize your users": "<PERSON><PERSON><PERSON> is<PERSON>-ik·im", "Other": "<PERSON><PERSON>", "OUTPUT": "TUFFƔA", "Output format": "Amasal n tuffɣa", "Output Format": "Amasal n tuffɣa", "Overview": "<PERSON><PERSON><PERSON><PERSON> s umata", "page": "as<PERSON><PERSON>", "Paginate": "<PERSON><PERSON><PERSON><PERSON>", "Parameters": "Isefranen", "Password": "Awal n uɛeddi", "Passwords do not match.": "<PERSON><PERSON><PERSON> n uɛeddi ur mṣadan ara.", "Paste Large Text as File": "<PERSON><PERSON><PERSON> a<PERSON>ris meqqren am ufaylu", "PDF document (.pdf)": "Isemli PDF (.pdf)", "PDF Extract Images (OCR)": "Tugniwin n ugemmay PDF", "pending": "yettṛaǧu", "Pending": "<PERSON><PERSON><PERSON>", "Pending User Overlay Content": "", "Pending User Overlay Title": "Titre n useqdac nnig wakal", "Permission denied when accessing media devices": "Ttwagedlent tsirag lawan n unekcum ɣer yibenkan n yimidyaten", "Permission denied when accessing microphone": "<PERSON><PERSON><PERSON><PERSON><PERSON> unekcum ɣer usawaḍ", "Permission denied when accessing microphone: {{error}}": "<PERSON><PERSON><PERSON><PERSON><PERSON> unekcum ɣer usawaḍ: {{error}}", "Permissions": "Tisirag", "Perplexity API Key": "Tasarut API n Perplexity", "Perplexity Model": "Tamudemt n Perplexity", "Perplexity Search Context Usage": "", "Personalization": "<PERSON><PERSON><PERSON>", "Picture Description API Config": "Tawlaft n API", "Picture Description Local Config": "Aglam Akun<PERSON> adigan", "Picture Description Mode": "Askar n uglam n tugniwin", "Pin": "<PERSON><PERSON>", "Pinned": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pioneer insights": "", "Pipe": "<PERSON><PERSON><PERSON>", "Pipeline deleted successfully": "<PERSON><PERSON><PERSON> akken iwata", "Pipeline downloaded successfully": "<PERSON><PERSON><PERSON>-d ak<PERSON> i<PERSON>a", "Pipelines": "<PERSON><PERSON><PERSON><PERSON>", "Pipelines are a plugin system with arbitrary code execution —": "Pipelines d unagraw n izegrar s uselkem n tengalt awurman —", "Pipelines Not Detected": "Ulac aselda i yettwafen", "Pipelines Valves": "", "Plain text (.md)": "<PERSON><PERSON><PERSON> (.md)", "Plain text (.txt)": "<PERSON><PERSON><PERSON> (.txt)", "Playground": "Adeg n tarimt", "Playwright Timeout (ms)": "Tanza<PERSON> n ugani n <PERSON>wright (ms)", "Playwright WebSocket URL": "URL n websocket Playwright", "Please carefully review the following warnings:": "", "Please do not close the settings page while loading the model.": "", "Please enter a message or attach a file.": "", "Please enter a prompt": "Ttxil-k·m, sekcem-d aneftaɣ", "Please enter a valid path": "Ttxil-k·m, sekcem-d abrid iṣeḥḥan", "Please enter a valid URL": "<PERSON>, sek<PERSON>m URL tameɣtut", "Please fill in all fields.": "", "Please select a model first.": "Ttxil-k·m, fren tamudemt di tazwara.", "Please select a model.": "Ttxil-k, fren tamudemt.", "Please select a reason": "<PERSON> ulac aɣilif ini-d acuɣeṛ", "Please wait until all files are uploaded.": "", "Port": "Tawwurt", "Positive attitude": "", "Prefer not to say": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "<PERSON><PERSON>y n uzwir yettusexdem i wakken ur d-yettili ara umennuɣ akked tuqqna-nniḍen s tmerna n usewgelhen i yimuhal n tmudemt - eǧǧ-iten d ilmawen i tukksa n tuqqna", "Prevent file creation": "Gmen timerna n ufaylu", "Preview": "Taskant", "Previous 30 days": "30 n wussan yezrin", "Previous 7 days": "7 n wussan ye<PERSON>rin", "Previous message": "<PERSON><PERSON> u<PERSON>", "Private": "<PERSON><PERSON><PERSON>", "Profile": "<PERSON><PERSON><PERSON><PERSON>", "Prompt": "Aneftaɣ", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Aneftaɣ (amedya. Ini-yi-d kra yessedhayen ɣef <PERSON>)", "Prompt Autocompletion": "<PERSON><PERSON> a<PERSON> n uneftaɣ", "Prompt Content": "Agbur n uneftaɣ", "Prompt created successfully": "Anef<PERSON>ɣ <PERSON>na akken iwata", "Prompt suggestions": "<PERSON><PERSON><PERSON> i unefta<PERSON>", "Prompt updated successfully": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> i<PERSON>a", "Prompts": "Ineftaɣen", "Prompts Access": "<PERSON><PERSON><PERSON>er y<PERSON>en", "Prompts Public Sharing": "Be<PERSON><PERSON><PERSON> a<PERSON>z n yineftaɣen", "Public": "<PERSON><PERSON><PERSON><PERSON>", "Pull \"{{searchValue}}\" from Ollama.com": "Awway n \"{{searchValue}}\" seg Ollama.com", "Pull a model from Ollama.com": "Zdem-d tamudemt seg Ollama.com", "Query Generation Prompt": "Aneftaɣ n usirew n tuttra", "Quick Actions": "<PERSON><PERSON><PERSON> tiruradin", "RAG Template": "Tamudemt RAG", "Rating": "<PERSON><PERSON><PERSON>", "Re-rank models by topic similarity": "I<PERSON><PERSON> i d-yettuɣalen ɣer sdat s usentel yecban wa", "Read": "Ɣeṛ", "Read Aloud": "Ɣeṛ-it-id s taɣect ɛlayen", "Reason": "<PERSON><PERSON><PERSON>", "Reasoning Effort": "", "Reasoning Tags": "", "Record": "<PERSON><PERSON><PERSON>", "Record voice": "<PERSON><PERSON> ta<PERSON>", "Redirecting you to Open WebUI Community": "", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Err iman-ik d \"Aseqdac\" (amedya, \"Aseqdac ilemmed taspenyu<PERSON>\")", "References from": "", "Refused when it shouldn't have": "", "Regenerate": "<PERSON><PERSON><PERSON>", "Regenerate Menu": "", "Reindex": "", "Reindex Knowledge Base Vectors": "<PERSON><PERSON><PERSON>", "Release Notes": "Tizmilin n lqem", "Releases": "<PERSON><PERSON><PERSON><PERSON>", "Relevance": "<PERSON><PERSON><PERSON>", "Relevance Threshold": "", "Remember Dismissal": "Ccfawa <PERSON><PERSON>", "Remove": "Kkes", "Remove {{MODELID}} from list.": "Kkes {{MODELID}} seg wumuɣ.", "Remove file": "<PERSON><PERSON>", "Remove File": "<PERSON><PERSON>", "Remove image": "Kkes tugna", "Remove Model": "Kkes tamudemt", "Remove this tag from list": "Kkes tabzimt-a seg wumuɣ", "Rename": "<PERSON><PERSON>l isem", "Reorder Models": "Ales n umizwer n tmudmiwin", "Reply in Thread": "Err deg usqerdec", "Reranking Engine": "", "Reranking Model": "", "Reset": "Wen<PERSON>z", "Reset All Models": "Ales akk timud<PERSON>win", "Reset Image": "Ales awennez n tugna", "Reset Upload Directory": "Wennez akaram n uzdam", "Reset Vector Storage/Knowledge": "", "Reset view": "<PERSON><PERSON><PERSON>an", "Response": "<PERSON><PERSON><PERSON>", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Ilɣa n tririt ur zmiren ara ad ttwasremden imi ugin ttesriḥat n usmel web. Ttxil-k, rzu ɣef yiɣewwaren-ik n yiminig akken ad tkecmeḍ ɣer wayen ilaqen.", "Response splitting": "Beṭṭu n tririt", "Response Watermark": "<PERSON><PERSON><PERSON><PERSON>t tafrawant n tririt", "Result": "<PERSON><PERSON><PERSON><PERSON>", "RESULT": "<PERSON><PERSON><PERSON><PERSON>", "Retrieval": "<PERSON><PERSON>", "Retrieval Query Generation": "Asirew n tuttra n RAG", "Rich Text Input for Chat": "<PERSON><PERSON><PERSON>", "RK": "RK", "Role": "<PERSON><PERSON><PERSON>", "Rosé Pine": "", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "<PERSON><PERSON><PERSON>", "Running": "Aselkem", "Running...": "Aselkem...", "Save": "<PERSON><PERSON>", "Save & Create": "<PERSON><PERSON> rnu snulfu-d", "Save & Update": "<PERSON><PERSON> rnu leqqem", "Save As Copy": "<PERSON><PERSON> d an<PERSON>al", "Save Chat": "", "Save Tag": "Sekles tabzimt", "Saved": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "", "Scroll On Branch Change": "Abeddel n Branch", "Search": "<PERSON><PERSON>", "Search a model": "<PERSON><PERSON> ta<PERSON>t", "Search all emojis": "", "Search Base": "Taffa n unadi", "Search Chats": "<PERSON><PERSON>", "Search Collection": "<PERSON><PERSON> talkensit", "Search Filters": "Imsizedgen n unadi", "search for archived chats": "", "search for folders": "anadi <PERSON><PERSON>", "search for pinned chats": "", "search for shared chats": "", "search for tags": "nadi <PERSON><PERSON>", "Search Functions": "<PERSON><PERSON>", "Search In Models": "<PERSON>di deg tmud<PERSON>win", "Search Knowledge": "<PERSON><PERSON>", "Search Models": "<PERSON><PERSON>", "Search Notes": "<PERSON><PERSON>", "Search options": "Tixtiṛiyin n unadi", "Search Prompts": "<PERSON><PERSON>", "Search Result Count": "Amḍan n yigmaḍ n unadi", "Search the internet": "<PERSON><PERSON>", "Search Tools": "<PERSON><PERSON>", "SearchApi API Key": "Tasarut API n SearchApi", "SearchApi Engine": "Amsadday n unadi SearchApi", "Searched {{count}} sites": "Inuda deg {{count}} n yismal web", "Searching \"{{searchQuery}}\"": "<PERSON><PERSON> \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "<PERSON><PERSON> n tmes<PERSON>in ɣef \"{{searchQuery}}\"", "Searching the web...": "<PERSON><PERSON> deg web…", "Searxng Query URL": "URL n unadi Searxng", "See readme.md for instructions": "Ẓer taɣuṛi i lewṣaya", "See what's new": "Wali d acu i yellan d amaynut", "Seed": "Seed", "Select": "", "Select a base model": "<PERSON>en tamudemt azadur", "Select a base model (e.g. llama3, gpt-4o)": "", "Select a conversation to preview": "Fren adiwenni i teskant", "Select a engine": "<PERSON><PERSON> amsedday", "Select a function": "<PERSON><PERSON> ta<PERSON>", "Select a group": "Fren agraw", "Select a language": "", "Select a mode": "", "Select a model": "Fren tamudemt", "Select a model (optional)": "", "Select a pipeline": "<PERSON><PERSON><PERSON>a", "Select a pipeline url": "Fren tansa URL n uselda", "Select a reranking model engine": "", "Select a role": "", "Select a theme": "", "Select a tool": "<PERSON><PERSON> afecku", "Select a voice": "", "Select an auth method": "Fren tarrayt n diri", "Select an embedding model engine": "", "Select an engine": "", "Select an Ollama instance": "<PERSON><PERSON> am<PERSON><PERSON>", "Select an output format": "", "Select dtype": "", "Select Engine": "<PERSON>en amsadday", "Select how to split message text for TTS requests": "", "Select Knowledge": "<PERSON><PERSON>", "Select only one model to call": "", "Selected model(s) do not support image inputs": "Ammud(s) yettwafernen ur yessefrak ara inekcamen n tugniwin yettwafernen", "semantic": "tasnamekt", "Semantic distance to query": "", "Send": "Tuzna", "Send a Message": "<PERSON><PERSON><PERSON><PERSON> izen", "Send message": "Azen izen", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "Ctembeṛ", "SerpApi API Key": "Tasarut API n SerpApi", "SerpApi Engine": "Amsedday SerpApi", "Serper API Key": "Tasarut API n Serper", "Serply API Key": "Tasarut API n Serply", "Serpstack API Key": "Tasarut API n Serpstack", "Server connection verified": "<PERSON><PERSON><PERSON><PERSON>, te<PERSON><PERSON><PERSON><PERSON><PERSON>", "Session": "", "Set as default": "<PERSON><PERSON><PERSON>-t d am<PERSON><PERSON>u", "Set CFG Scale": "Sbadu asefran CFG", "Set Default Model": "<PERSON><PERSON><PERSON> tamudemt tamez<PERSON>", "Set embedding model": "Sbadu tamudemt n ujmak", "Set embedding model (e.g. {{model}})": "<PERSON><PERSON>u tamudemt n ujmak (amedya. {{model}})", "Set Image Size": "<PERSON><PERSON><PERSON> tiddi n tugna", "Set reranking model (e.g. {{model}})": "<PERSON><PERSON>u tamudemt n usenker (amedya. {{model}})", "Set Sampler": "", "Set Scheduler": "<PERSON><PERSON><PERSON> am<PERSON>", "Set Steps": "<PERSON><PERSON>u amḍan n takkayin", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "<PERSON><PERSON>u am<PERSON>an n tnelli n yixeddamen yettwasqedcen i umṣada. Tifrat-a teḥkem acḥal n tnelli i yennumen ttḥerriken issutren i d-iteddun akka tura. Asenqes n wazal-a yezmer ad yesnerni aswir deg usali n yisali n umahil n uḥezzeb meqqren maca yezmer daɣen ad yečč ugar n teɣbula CPU.", "Set Voice": "<PERSON><PERSON> ta<PERSON>", "Set whisper model": "<PERSON>en tamudemt W<PERSON>per", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets how far back for the model to look back to prevent repetition.": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "", "Sets the size of the context window used to generate the next token.": "<PERSON><PERSON>u tiddi n tzewwut tasatalant yellan zik tetteg-d asken ay d-yetteddun.", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "<PERSON><PERSON><PERSON> tiseddarin n uḥbas i useqdec. Mi ara d-temlil temɛawdit-a, LLM ad teḥbes asegrew n uḍris d tuɣalin. <PERSON><PERSON><PERSON><PERSON> n uḥbas yeggten zemrent ad ttwasbeddent s usbadu n waṭas n yizamulen n uḥbas yemgaraden deg tmudemt.", "Settings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Settings saved successfully!": "Iɣewwaṛen ttwakelsen akken iwata!", "Share": "Bḍ<PERSON>", "Share Chat": "<PERSON><PERSON><PERSON>", "Share to Open WebUI Community": "Bḍu i tkebbanit WebUI yeldin", "Share your background and interests": "", "Sharing Permissions": "Tisirag n beṭṭu", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "Sken-d", "Show \"What's New\" modal on login": "Sken-d \"D acu i d askar amaynut\" deg uɣmis", "Show Admin Details in Account Pending Overlay": "", "Show All": "Sken-iten-id akk", "Show Formatting Toolbar": "Skan amsal n ufeggag n yifecka", "Show image preview": "Sken taskant n tugna", "Show Less": "Sken-d drus", "Show Model": "<PERSON><PERSON>-d tamude<PERSON>t", "Show shortcuts": "Sken-d inegzumen", "Show your support!": "Sken tallalt-ik⋅im!", "Showcased creativity": "", "Sign in": "<PERSON><PERSON><PERSON>", "Sign in to {{WEBUI_NAME}}": "<PERSON><PERSON><PERSON>er {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "<PERSON><PERSON><PERSON>er {{WEBUI_NAME}} s LDAP", "Sign Out": "<PERSON><PERSON><PERSON><PERSON>", "Sign up": "Jerred", "Sign up to {{WEBUI_NAME}}": "Jerred ɣer {{WEBUI_NAME}}", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to False.": "", "Signing in to {{WEBUI_NAME}}": "<PERSON><PERSON><PERSON><PERSON>er {{WEBUI_NAME}}", "Sink List": "", "sk-1234": "sk-1234", "Skip Cache": "<PERSON>r sseqdac ara tuffirt", "Skip the cache and re-run the inference. Defaults to False.": "<PERSON><PERSON><PERSON>nni, tɛaw<PERSON><PERSON>-as assefreg. <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>.", "Something went wrong :/": "", "Sonar": "", "Sonar Deep Research": "", "Sonar Pro": "", "Sonar Reasoning": "", "Sonar Reasoning Pro": "", "Sougou Search API sID": "Asulay API n Sougou Search (sID)", "Sougou Search API SK": "Tasarut tuffirt n API Sougou Search (SK)", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Speech Playback Speed": "<PERSON><PERSON><PERSON> n tɣuri n umeslay", "Speech recognition error: {{error}}": "<PERSON>cc<PERSON>a n uɛqal n wawal: {{error}}", "Speech-to-Text": "Aɛqal n taɣect", "Speech-to-Text Engine": "Amsadday n uɛqal n taɣect", "Start of the channel": "Tazwara n wabadu", "Start Tag": "", "STDOUT/STDERR": "STDOUT/STDERR", "Stop": "<PERSON><PERSON><PERSON>", "Stop Generating": "<PERSON><PERSON><PERSON> as<PERSON>w", "Stop Sequence": "Tagzemt n uḥbas", "Stream Chat Response": "Suddem tiririt n udiwenni", "Stream Delta Chunk Size": "", "Strikethrough": "Yettujerreḍ", "Strip Existing OCR": "Kkes ORC i yellan", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "<PERSON><PERSON><PERSON> aḍris OCR yellan yakan seg PDF akked OCR i d-yettuɣalen. Ur ttettu ara ma yella Force OCR tettwarmed. Imezwura ɣer False.", "STT Model": "Tamudemt n uɛqal n taɣect", "STT Settings": "Iɣewwaren n uɛqal n tavect", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "Addad amaruz (amedya ɣef tgelda Tarumanit)", "Success": "<PERSON><PERSON><PERSON>", "Successfully imported {{userCount}} users.": "", "Successfully updated.": "Yettwaleqq<PERSON> a<PERSON>ken iwata.", "Suggest a change": "<PERSON><PERSON>", "Suggested": "Yet<PERSON><PERSON>umer-d", "Support": "<PERSON><PERSON><PERSON>", "Support this plugin:": "Ɛiwen asiɣzef-a:", "Supported MIME Types": "<PERSON><PERSON> ttwasefraken", "Sync directory": "<PERSON><PERSON><PERSON> akaram", "System": "<PERSON><PERSON><PERSON>", "System Instructions": "Tanaḍin n unagraw", "System Prompt": "Aneftaɣ n unagraw", "Tags": "<PERSON><PERSON><PERSON><PERSON>", "Tags Generation": "Asirew n tebzimin", "Tags Generation Prompt": "Aneftaɣ n usirew n tebzimin", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "", "Talk to model": "<PERSON><PERSON><PERSON> d tmudemt", "Tap to interrupt": "Sit i unegzum", "Task List": "Tabdart n temsekra", "Task Model": "Tamudemt n temsekra", "Tasks": "Timsekra", "Tavily API Key": "Tasarut API n Tavily", "Tavily Extract Depth": "Talqayt n <PERSON><PERSON><PERSON><PERSON>", "Tell us more:": "Ini-aɣ-d ugar:", "Temperature": "Taz<PERSON>elt", "Temporary Chat": "Asqerdec akudan", "Temporary Chat by Default": "", "Text Splitter": "", "Text-to-Speech": "<PERSON><PERSON><PERSON>-<PERSON>er-taɣ<PERSON>t", "Text-to-Speech Engine": "Amsadday n TTS", "Thanks for your feedback!": "<PERSON><PERSON><PERSON>t ɣef tikti-inek·inem!", "The Application Account DN you bind with for search": "", "The base to search for users": "Taffa n unadi ɣef yise<PERSON>n", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The format to return a response in. Format can be json or a JSON schema.": "Amasal ideg ilaq ad d-tettwarr tririt. Amasal yezmer ad yili d json neɣ d azenziɣ n JSON.", "The height in pixels to compress images to. Leave empty for no compression.": "Tiddi n yipiksilen akken ad ssedhun tugniwin. Eǧǧ ilem war aḥezzeb.", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "Imyerr LDAP i d-ttawint tgertilin ɣer tebratin i sseqdacen yiseqdacen akken ad zemlen.", "The LDAP attribute that maps to the username that users use to sign in.": "<PERSON><PERSON><PERSON>r LDA<PERSON> i d-yeqqaren tika<PERSON> i yisem n useqdac i sseqdacen yiseqdacen akken ad zemlen.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "<PERSON><PERSON><PERSON><PERSON><PERSON> n uɣella attan akka tura deg beta, dɣa nezmer ad neswati leḥ<PERSON>bat n ṭṭubba akken ara nessinef alguritm.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "T<PERSON><PERSON> n ufaylu afellay deg MB. Ma yella te<PERSON>zi n ufaylu tɛedda i talast-a, afaylu ur yettali ara.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Amḍan afellay n yifuyla i izemren ad ttwasqedcen ɣef tikkelt deg udiwenni. Ma yella amḍan n yifuyla iɛedda i talast-a, ifuyla ur ttalin ara.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "Amasal n tuffɣa i uḍris. Tzemreḍ ad tiliḍ d 'json', d 'markdown' neɣ d 'html'. Imezwura ɣer 'markdown'.", "The passwords you entered don't quite match. Please double-check and try again.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "", "The stream delta chunk size for the model. Increasing the chunk size will make the model respond with larger pieces of text at once.": "", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "Taẓɣelt n tmudemt. <PERSON>enqes n teẓɣelt ad yerr tamudemt d tiririt s tesɣent ugar.", "The Weight of BM25 Hybrid Search. 0 more lexical, 1 more semantic. Default 0.5": "", "The width in pixels to compress images to. Leave empty for no compression.": "Ta<PERSON><PERSON>a deg yip<PERSON>len akken ad te<PERSON>hu tug<PERSON>. Eǧǧ ilem war aḥezzeb.", "Theme": "Asentel", "Thinking...": "Ttxemmimeɣ…", "This action cannot be undone. Do you wish to continue?": "Tigawt-a ur tettwakkes ara. Tebɣiḍ ad tkemmleḍ?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "Abadu-a yettwasnulaf-d deg {{created} Ɣef}}. D ta i d tazwara maḍi n ubadu {{channelName}}.", "This chat won't appear in history and your messages will not be saved.": "Aq<PERSON><PERSON><PERSON><PERSON>-a ur d-yettban deg umezruy yerna iznan-nnek ur ttwasel<PERSON>men.", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "D ayen ara yeǧǧen adiwenni-inek s wazal-is ad yettwasellek s tɣellist deg taffa n yisefka-inek n deffir. Tanemmirt!", "This feature is experimental and may be modified or discontinued without notice.": "Tamahilt-a d tirmitant yerna tezmer ad tettwabeddel neɣ ad teḥbes war tamawt.", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Ta d taɣ<PERSON>a tirmitant, <PERSON><PERSON><PERSON> lḥal ur tleḥḥu ara akken i tebɣiḍ, dɣa d asentel n ubeddel melmi tebɣiḍ.", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "Tifrat-a ad teǧǧ neɣ ad tessenqes aseqdec n tmahilt n usseɣẓen deg <PERSON>, ayen yettaǧǧan tamudemt ad txemmem uqbel ad d-tawi tiririt. <PERSON> <PERSON>saweḍ yiwen, tamudemt tezmer ad teṭṭef cwiṭ n wakud akken ad tseddu asatal n umeslay u ad d-tawi tiririt yettxemmimen ugar.", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "Tiririt-a teslal-itt-id \"{model}}\"", "This will delete": "Aya ad yekkes", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "Aya ad yekkes <strong>{NAME}}</strong> akked <strong> akk ayen yellan deg-s</strong>.", "This will delete all models including custom models": "Aya ad yekkes akk timudmin yellan gar-asent timudmin n tannumi", "This will delete all models including custom models and cannot be undone.": "<PERSON>ya ad yekkes akk timudmin gar-asent timudmin tudmawanin yerna ur yezmir yiwen ad tent-id-yerr.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Aya ad yales taffa n tmussni u ad yemtawi akk ifuyla. Tebɣiḍ ad tkemmleḍ?", "Thorough explanation": "", "Thought for {{DURATION}}": "Ixemmem {{DURATION}}", "Thought for {{DURATION}} seconds": "Axemmem ɣef {{DURATION}} n tsinin", "Thought for less than a second": "", "Thread": "Agzul", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "Tansa URL n Tika Server tettwasra.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Tip: <PERSON><PERSON><PERSON><PERSON> a<PERSON> n yidrimen n umutti yal wa s usiti ɣef tsarut n yiccer deg udraw n udiwenni deffir yal as<PERSON><PERSON>i.", "Title": "<PERSON><PERSON><PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "<PERSON><PERSON><PERSON><PERSON> (amedya. Ini-yi-d ayen yessedhayen)", "Title Auto-Generation": "Asirew awurman n izwilen", "Title cannot be an empty string.": "<PERSON><PERSON><PERSON><PERSON> ur yettili ara d azrir ilem.", "Title Generation": "Asirew n uzwel", "Title Generation Prompt": "Aneftaɣ n usirew n uzwel", "TLS": "TLS", "To access the available model names for downloading,": "", "To access the GGUF models available for downloading,": "Akken ad tkecmeḍ ɣer tmudmin GGUF yellan i usader,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Akken ad tkecme<PERSON> ɣer <PERSON>UI, ttxil-k, nermes anedbal. Imedminen zemren ad sselḥun addaden n useqdac seg ugalis Admin.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Akken ad teqqneḍ taffa n tmessunin da, rnu-tent ɣer \"Timessunin\" n temnaḍṭ n umahil, di tazwara.", "To learn more about available endpoints, visit our documentation.": "Akken ad tissineḍ ugar ɣef wagazen n taggara yellan, rzu ɣer warrat-nneɣ.", "To learn more about powerful prompt variables, click here": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Ak<PERSON> ad tḥ<PERSON><PERSON><PERSON> tudert-ik <PERSON>, al<PERSON><PERSON>, isulayen n tmu<PERSON><PERSON>t, tib<PERSON><PERSON>, akked metadata ttwabḍant ɣef tikti-k — iɣmisen-ik n udiwenni qqimen d usligen, ur d-ddan ara.", "To select actions here, add them to the \"Functions\" workspace first.": "Akken ad tferneḍ tigawin da, rnu-tent ɣer \"Tisɣunin\" n temnaḍṭ n umahil, deg tazwara.", "To select filters here, add them to the \"Functions\" workspace first.": "Akken ad tferneḍ imsizdigen da, rnu-ten ɣer \"Tisɣunin\" n temnaḍṭ n umahil, di tazwara.", "To select toolkits here, add them to the \"Tools\" workspace first.": "", "Toast notifications for new updates": "<PERSON><PERSON><PERSON><PERSON> ilɣa i yile<PERSON>man imaynuten", "Today": "Ass-a", "Toggle search": "Ldi/Ffer anadi", "Toggle settings": "Sken/<PERSON>fer i<PERSON>n", "Toggle sidebar": "Ldi/Mdel afeggag adisan", "Toggle whether current connection is active.": "Sken ma yella tuqqna tamirant d turmidt.", "Token": "<PERSON><PERSON><PERSON><PERSON>", "Too verbose": "", "Tool created successfully": "<PERSON><PERSON><PERSON><PERSON> ufecku akken iwata", "Tool deleted successfully": "Afecku yettwakkes akken iwata", "Tool Description": "Aglam n ufecku", "Tool ID": "Asulay n ufecku", "Tool imported successfully": "Afecku yettwakter akken iwata", "Tool Name": "Isem n ufecku", "Tool Servers": "Iqeddacen n ifecka", "Tool updated successfully": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>ken iwata", "Tools": "<PERSON><PERSON><PERSON>", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "", "Tools Function Calling Prompt": "", "Tools have a function calling system that allows arbitrary code execution.": "", "Tools Public Sharing": "Beṭṭu azayaz n yifecka", "Top K": "Top K", "Top K Reranker": "Top K Reranker", "Transformers": "Transformers", "Trouble accessing Ollama?": "<PERSON><PERSON><PERSON> deg adaf <PERSON>?", "Trust Proxy Environment": "Eg laman deg up<PERSON><PERSON>si n twenna<PERSON>t", "Try Again": "Ɛreḍ tikelt nniḍen", "TTS Model": "Tamudemt TTS", "TTS Settings": "Iɣewwaṛen n TTS", "TTS Voice": "Taɣect n TTS", "Type": "<PERSON><PERSON>", "Type Hugging Face Resolve (Download) URL": "Anaw n usefres n wudem amezwer (Download) URL", "Uh-oh! There was an issue with the response.": "", "UI": "Agrudem n useqdac", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Underline": "<PERSON><PERSON>", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "", "Unpin": "<PERSON><PERSON> asente<PERSON>", "Unravel secrets": "<PERSON><PERSON>-d ayen yeffren", "Unsupported file type.": "Tawsit n ufaylu ur tettusefrak ara.", "Untagged": "Tekkes-as teb<PERSON><PERSON>t", "Untitled": "War azwel", "Update": "<PERSON><PERSON>", "Update and Copy Link": "<PERSON><PERSON><PERSON><PERSON>", "Update for the latest features and improvements.": "<PERSON><PERSON><PERSON><PERSON> ti<PERSON><PERSON><PERSON><PERSON> tin<PERSON> d usnerni.", "Update password": "<PERSON><PERSON><PERSON><PERSON> awal n uɛeddi", "Updated": "Yet<PERSON>leqq<PERSON>", "Updated at": "Yet<PERSON><PERSON><PERSON>qq<PERSON>", "Updated At": "Yet<PERSON><PERSON><PERSON>qq<PERSON>", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "Lhu-d d u<PERSON><PERSON><PERSON> n turagt i tzemmar yesnernayen, gar-asent tannumi d ukunyak, d tallelt yemmugen i tallelt.", "Upload": "Sali", "Upload a GGUF model": "Sali tamudemt GGUF", "Upload Audio": "<PERSON><PERSON> am<PERSON>", "Upload directory": "<PERSON><PERSON>", "Upload files": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Upload Files": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Upload Pipeline": "<PERSON><PERSON><PERSON> n uɛebbi", "Upload Progress": "", "Upload Progress: {{uploadedFiles}}/{{totalFiles}} ({{percentage}}%)": "", "URL": "URL", "URL is required": "", "URL Mode": "Askar n URL", "Usage": "Aseqdec", "Use '#' in the prompt input to load and include your knowledge.": "", "Use groups to group your users and assign permissions.": "", "Use LLM": "Seqdec LLM", "Use no proxy to fetch page contents.": "<PERSON>r sseqdacet ara ayen yellan deg usebter ap<PERSON><PERSON><PERSON>.", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "Seqdec proxy i d-yesnulfa http_proxy akked https_proxy environment variables to fetch page contents.", "user": "aseqdac", "User": "Aseqdac", "User Groups": "", "User location successfully retrieved.": "", "User menu": "Umuɣ n useqdac", "User Webhooks": "Webhooks n yiseqdacen", "Username": "Isem n useqdac", "Users": "Iseqdacen", "Using Entire Document": "Aseqdec n isemli Entire", "Using Focused Retrieval": "Aseqdec n Retrieval yeslummes", "Using the default arena model with all models. Click the plus button to add custom models.": "Aseqdec n tmudemt n uzna amezwer s yal timudmin. Tek<PERSON> ɣef tqeffalt-nni n tmerniwt akken ad ternuḍ timudmin tinsayanin.", "Valid time units:": "Tigget n wakud ameɣtu:", "Validate certificate": "", "Valves": "", "Valves updated": "", "Valves updated successfully": "<PERSON><PERSON> ttwaleqmen akken iwata", "variable": "ta<PERSON><PERSON><PERSON>", "Verify Connection": "<PERSON><PERSON><PERSON> tuqqna", "Verify SSL Certificate": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>L", "Version": "Lq<PERSON>", "Version {{selectedVersion}} of {{totalVersions}}": "Lqem {{s lqem <PERSON><PERSON><PERSON>}} n {{totalVersions}}", "View Replies": "<PERSON><PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON>n", "View Result from **{{NAME}}**": "<PERSON><PERSON><PERSON><PERSON> i d-yettuɣalen seg tazwara **{NAME}}**", "Visibility": "<PERSON><PERSON><PERSON><PERSON>", "Vision": "Vision", "Voice": "Taɣect", "Voice Input": "Anekcam s taɣect", "Voice mode": "Askar n taɣect", "Warning": "Ɣur-k", "Warning:": "Alɣu:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Ɣur-k: <PERSON><PERSON><PERSON><PERSON> n waya ad yeǧǧ iseqdacen ad d-salin tangalt tazurant ɣef uq<PERSON>.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Ɣur-k⋅m: Ma tleqqmeḍ neɣ tbeddleḍ tamudemt-ik ⋅im n ujmak, ilaq-ak⋅am ad talseḍ aktar n meṛṛa isemliyen.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "Ɣur-k: <PERSON><PERSON><PERSON><PERSON> n Jupyter yettaǧǧa asselkem n tengalt tazurant, d tukksa n tmijwin n tɣ<PERSON>st qessi<PERSON>en — s leḥder meqqren.", "Web": "Web", "Web API": "API Web", "Web Loader Engine": "Amsedday n uzdam Web", "Web Search": "<PERSON><PERSON> deg <PERSON>", "Web Search Engine": "Amsadday n unadi Web", "Web Search in Chat": "Anadi Web deg udiwenni", "Web Search Query Generation": "", "Webhook URL": "Tansa URL n webhook", "WebUI Settings": "Iɣewwaṛen n WebUI", "WebUI URL": "Tansa URL WebUI", "WebUI will make requests to \"{{url}}\"": "WebUI ad ssutreɣ \"{url}}\"", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI ad ssutreɣ i \"{url}}/api/chat\"", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI ad ssutreɣ i \"{url}}/chat/completions\"", "What are you trying to achieve?": "Sanda ay tettarmed ad tessiwḍed?", "What are you working on?": "Ɣef wacu ay la tettmahaled?", "What's New in": "D acu d amaynut deg", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "anda yeb<PERSON>u til<PERSON>", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "Ma tebɣiḍ ad d-tessugneḍ tuffɣa. Ya<PERSON> asebter ad yebḍu s ulugen igli d wuṭṭun n usebter. Imezwura ɣer False.", "Whisper (Local)": "Whisper (adigan)", "Why?": "<PERSON><PERSON><PERSON><PERSON>?", "Widescreen Mode": "Askar n ugdil aččuran", "Width": "", "Won": "<PERSON><PERSON><PERSON><PERSON>", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "", "Workspace": "Tamnaḍṭ n umahil", "Workspace Permissions": "Tisirag n temnaḍṭ n umahil", "Write": "<PERSON><PERSON>", "Write a prompt suggestion (e.g. Who are you?)": "<PERSON><PERSON>-d assumer i d-yeffɣen s tɣawla (amedya, anwa-k?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Aru agzul s 50 n wawalen i yessewzalen [as<PERSON>ru neɣ tasarut].", "Write something...": "<PERSON><PERSON> kra<PERSON>", "Write your model system prompt content here\ne.g.) You are Mario from Super Mario Bros, acting as an assistant.": "", "Yacy Instance URL": "URL n tummant <PERSON>", "Yacy Password": "A<PERSON> n uɛeddi n <PERSON>cy", "Yacy Username": "Is<PERSON> n useqdac <PERSON>", "Yesterday": "<PERSON><PERSON><PERSON>", "You": "Kečč·mm", "You are currently using a trial license. Please contact support to upgrade your license.": "<PERSON><PERSON><PERSON>a, aql-ik tesseqdaced turagt n ccṛeɛ. <PERSON> ul<PERSON> a<PERSON>, ddu<PERSON>t akken ad tesselhum turagt-nwen.", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "<PERSON><PERSON><PERSON>re<PERSON> kan ad tqeṣṣreḍ s afellay n ufaylu(s) n {{maxCount}} ɣef tikelt.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "", "You cannot upload an empty file.": "Ur tezmireḍ ad tessaliḍ afaylu ilem.", "You do not have permission to upload files.": "Ur tesɛiḍ ara turagt i uceggeɛ n yifuyla.", "You have no archived conversations.": "", "You have shared this chat": "<PERSON><PERSON><PERSON><PERSON><PERSON> aq<PERSON>er-a", "You're a helpful assistant.": "Ke<PERSON><PERSON> d amalal yettallen.", "You're now logged in.": "Aql-ik teqqned imir-a.", "Your Account": "", "Your account status is currently pending activation.": "Addad-nnem n umiḍan atan yettṛaju armad.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "YouTube", "Youtube Language": "Tutlayt n Youtube", "Youtube Proxy URL": "Tansa URL n upṛuksi Youtube"}